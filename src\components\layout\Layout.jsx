import React, { useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import Header from "./Header";
import Footer from "./Footer";
import Chatbot from "../chatbot/Chatbot";
import useThemeStore from "../../store/themeStore";
import useUserStore from "../../store/userStore";
import useScrollReset from "../../hooks/useScrollReset";

const Layout = ({ children, hideFooter = false, hideHeader = false }) => {
  const darkMode = useThemeStore((state) => state.darkMode);
  const user = useUserStore((state) => state.user);
  const logout = useUserStore((state) => state.logout);
  const navigate = useNavigate();
  const location = useLocation();

  // استخدام hook إعادة تعيين موضع التمرير
  useScrollReset();

  // إعادة تعيين موضع التمرير عند تغيير المسار
  useEffect(() => {
    window.scrollTo(0, 0);
  }, [location.pathname]);

  // دالة تسجيل الخروج
  const handleLogout = () => {
    logout();
    navigate("/logout-redirect");
  };

  return (
    <div
      className={`flex flex-col min-h-screen ${
        darkMode
          ? "bg-gray-900 text-white"
          : "bg-gradient-to-br from-blue-50 to-indigo-100 text-black"
      } transition-colors duration-300`}
    >
      {!hideHeader && <Header user={user} onLogout={handleLogout} />}
      <main className={`flex-grow ${!hideHeader ? "md:mt-0 mt-16" : ""}`}>{children}</main>
      {!hideFooter && <Footer />}
      <Chatbot />
    </div>
  );
};

export default Layout;
