# سكريبت تحديث المهن

هذا السكريبت يقوم بتحديث بيانات المهن والتخصصات في قاعدة البيانات بناءً على البيانات الموجودة في الفرونت إند.

## الوظائف

- حذف جميع المهن الموجودة في قاعدة البيانات
- إضافة جميع المهن والتخصصات من بيانات الفرونت إند
- تجنب التكرار في البيانات
- ربط كل مهنة بتخصصاتها المناسبة

## كيفية التشغيل

### من مجلد الباك إند:

```bash
cd backend
node src/scripts/updateProfessions.js
```

### أو من مجلد الجذر:

```bash
cd backend && node src/scripts/updateProfessions.js
```

## متطلبات التشغيل

1. تأكد من وجود ملف `.env` في مجلد الباك إند
2. تأكد من وجود متغير `MONGODB_URI` في ملف `.env`
3. تأكد من تشغيل قاعدة البيانات MongoDB

## البيانات المضافة

السكريبت يضيف المهن التالية مع تخصصاتها:

### المهن الأساسية:
- كهربائي (8 تخصصات)
- سباك (8 تخصصات)
- نجار (8 تخصصات)
- دهان (8 تخصصات)
- مصمم ديكور (8 تخصصات)
- ميكانيكي (8 تخصصات)
- حداد (8 تخصصات)
- بناء (8 تخصصات)
- مكيفات (8 تخصصات)

### المهن الخدمية:
- خياط (8 تخصصات)
- طباخ (8 تخصصات)
- مزارع (8 تخصصات)
- مصلح أجهزة كهربائية (8 تخصصات)
- مصلح موبايلات وكمبيوتر (6 تخصصات)
- سائق (8 تخصصات)
- مصور (8 تخصصات)
- معلم (8 تخصصات)
- حلاق (8 تخصصات)

### المهن المتخصصة:
- تركيب و صيانة ألمنيوم (8 تخصصات)
- معلم سيراميك (8 تخصصات)
- عامل نظافة (8 تخصصات)
- عامل توصيل (8 تخصصات)
- عامل صيانة عامة (8 تخصصات)
- فني تبريد وتكييف (8 تخصصات)
- عامل بناء (8 تخصصات)
- فني تمديدات صحية (8 تخصصات)
- فني لحام وحدادة (8 تخصصات)
- حدّاد متخصص في الأبواب والنوافذ (8 تخصصات)

### مهن الصحة والجمال:
- طبيب (8 تخصصات)
- ممرض (8 تخصصات)
- معالج فيزيائي (8 تخصصات)
- خبير تجميل (8 تخصصات)

### مهن الطعام والضيافة:
- شيف (8 تخصصات)
- نادل (8 تخصصات)
- منسق حفلات (8 تخصصات)

### مهن الإلكترونيات والتقنية:
- فني إلكترونيات (8 تخصصات)
- مصمم مواقع (8 تخصصات)
- مطور تطبيقات (8 تخصصات)

## إجمالي البيانات

- **إجمالي المهن**: 33 مهنة
- **إجمالي التخصصات**: حوالي 260 تخصص

## ملاحظات

- السكريبت يحذف جميع البيانات الموجودة قبل إضافة البيانات الجديدة
- تأكد من عمل نسخة احتياطية من قاعدة البيانات قبل التشغيل
- السكريبت يعرض تقرير مفصل عن العملية
- يتم إغلاق الاتصال بقاعدة البيانات تلقائياً بعد انتهاء العملية

## استكشاف الأخطاء

إذا واجهت خطأ في الاتصال بقاعدة البيانات:
1. تحقق من صحة `MONGODB_URI`
2. تأكد من تشغيل MongoDB
3. تحقق من صحة بيانات الاعتماد

إذا واجهت خطأ في إضافة البيانات:
1. تحقق من صحة بنية البيانات
2. تأكد من عدم وجود قيود على قاعدة البيانات
3. تحقق من مساحة التخزين المتاحة
