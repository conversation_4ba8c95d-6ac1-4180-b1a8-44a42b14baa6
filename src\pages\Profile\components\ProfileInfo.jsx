import React from "react";
import { Phone, Mail, Copy, Check } from "lucide-react";
import Card from "../../../components/common/Card";
import useThemeStore from "../../../store/themeStore";
import { useState } from "react";

const ProfileInfo = ({ user }) => {
  const darkMode = useThemeStore((state) => state.darkMode);
  const [copied, setCopied] = useState({
    phone: false,
    email: false,
  });

  // دالة لنسخ النص إلى الحافظة
  const copyToClipboard = (text, type) => {
    navigator.clipboard.writeText(text);
    setCopied({ ...copied, [type]: true });
    setTimeout(() => {
      setCopied({ ...copied, [type]: false });
    }, 2000);
  };

  return (
    <Card
      className={`p-6 shadow-lg border transition-colors duration-300 ${
        darkMode
          ? "bg-gray-800 text-gray-200 border-gray-700"
          : "bg-gradient-to-br from-blue-50 to-indigo-100 border-indigo-200"
      }`}
    >
      {/* عنوان القسم مع تأثير خط تحته */}
      <div className="flex items-center justify-between mb-6">
        <h2
          className={`text-xl font-bold relative inline-block ${
            darkMode ? "text-indigo-300" : "text-indigo-800"
          } transition-colors duration-300`}
        >
          <span className="relative z-10">بيانات الحرفي</span>
          <span
            className={`absolute bottom-0 left-0 right-0 h-2 ${
              darkMode ? "bg-indigo-500" : "bg-indigo-300"
            } opacity-40 transform -rotate-1 z-0`}
          ></span>
        </h2>
      </div>

      <div className="space-y-6">
        {/* رقم الهاتف */}
        <div
          className={`p-4 rounded-xl ${
            darkMode
              ? "bg-gradient-to-r from-gray-800 to-gray-700 border border-indigo-900/30 shadow-lg"
              : "bg-gradient-to-r from-blue-50 to-indigo-100 border border-indigo-200 shadow-md"
          } transition-all duration-300 hover:shadow-xl relative overflow-hidden group`}
        >
          {/* زخرفة خلفية */}
          <div className="absolute top-0 right-0 w-32 h-32 bg-indigo-500/5 rounded-full -mr-10 -mt-10 z-0"></div>
          <div className="absolute bottom-0 left-0 w-24 h-24 bg-indigo-500/5 rounded-full -ml-10 -mb-10 z-0"></div>

          <div className="relative z-10">
            {/* العنوان */}
            <div className="flex items-center mb-4">
              <div
                className={`p-3 rounded-full mr-3 ${
                  darkMode ? "bg-indigo-900/70" : "bg-indigo-200"
                } transition-all duration-300 group-hover:scale-110 shadow-md`}
              >
                <Phone
                  size={22}
                  className={`${
                    darkMode ? "text-indigo-300" : "text-indigo-600"
                  } transition-colors duration-300`}
                />
              </div>
              <div
                className={`text-base font-bold mr-1 ${
                  darkMode ? "text-indigo-300" : "text-indigo-700"
                }`}
              >
                رقم الهاتف المحمول
              </div>
            </div>

            {/* رقم الهاتف */}
            <div
              className={`font-bold text-xl text-center py-2 px-4 rounded-lg ${
                darkMode
                  ? "bg-gray-900/50 text-indigo-300 border border-indigo-900/30"
                  : "bg-white/80 text-indigo-800 border border-indigo-200"
              } break-all shadow-inner mb-3`}
            >
              {user.phone || "رقم الهاتف غير متوفر"}
            </div>

            {/* زر النسخ */}
            <div className="flex justify-center">
              <button
                onClick={() => copyToClipboard(user.phone, "phone")}
                className={`flex items-center justify-center gap-2 py-2 px-4 rounded-lg ${
                  darkMode
                    ? "bg-indigo-900/50 hover:bg-indigo-800/70 text-indigo-300 border border-indigo-800/30"
                    : "bg-indigo-100 hover:bg-indigo-200 text-indigo-700 border border-indigo-200"
                } transition-all duration-300 shadow-md`}
              >
                {copied.phone ? (
                  <>
                    <Check size={18} />
                    <span>تم النسخ</span>
                  </>
                ) : (
                  <>
                    <Copy size={18} />
                    <span>نسخ الرقم</span>
                  </>
                )}
              </button>
            </div>
          </div>
        </div>

        {/* البريد الإلكتروني */}
        {user.email && (
          <div
            className={`p-4 rounded-xl ${
              darkMode
                ? "bg-gradient-to-r from-gray-800 to-gray-700 border border-indigo-900/30 shadow-lg"
                : "bg-gradient-to-r from-blue-50 to-indigo-100 border border-indigo-200 shadow-md"
            } transition-all duration-300 hover:shadow-xl relative overflow-hidden group`}
          >
            {/* زخرفة خلفية */}
            <div className="absolute top-0 right-0 w-32 h-32 bg-indigo-500/5 rounded-full -mr-10 -mt-10 z-0"></div>
            <div className="absolute bottom-0 left-0 w-24 h-24 bg-indigo-500/5 rounded-full -ml-10 -mb-10 z-0"></div>

            <div className="relative z-10">
              {/* العنوان */}
              <div className="flex items-center mb-4">
                <div
                  className={`p-3 rounded-full mr-3 ${
                    darkMode ? "bg-indigo-900/70" : "bg-indigo-200"
                  } transition-all duration-300 group-hover:scale-110 shadow-md`}
                >
                  <Mail
                    size={22}
                    className={`${
                      darkMode ? "text-indigo-300" : "text-indigo-600"
                    } transition-colors duration-300`}
                  />
                </div>
                <div
                  className={`text-base font-bold mr-1 ${
                    darkMode ? "text-indigo-300" : "text-indigo-700"
                  }`}
                >
                  البريد الإلكتروني
                </div>
              </div>

              {/* البريد الإلكتروني */}
              <div
                className={`font-bold text-xl text-center py-2 px-4 rounded-lg ${
                  darkMode
                    ? "bg-gray-900/50 text-indigo-300 border border-indigo-900/30"
                    : "bg-white/80 text-indigo-800 border border-indigo-200"
                } break-all shadow-inner mb-3`}
              >
                {user.email}
              </div>

              {/* زر النسخ */}
              <div className="flex justify-center">
                <button
                  onClick={() => copyToClipboard(user.email, "email")}
                  className={`flex items-center justify-center gap-2 py-2 px-4 rounded-lg ${
                    darkMode
                      ? "bg-indigo-900/50 hover:bg-indigo-800/70 text-indigo-300 border border-indigo-800/30"
                      : "bg-indigo-100 hover:bg-indigo-200 text-indigo-700 border border-indigo-200"
                  } transition-all duration-300 shadow-md`}
                >
                  {copied.email ? (
                    <>
                      <Check size={18} />
                      <span>تم النسخ</span>
                    </>
                  ) : (
                    <>
                      <Copy size={18} />
                      <span>نسخ البريد</span>
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </Card>
  );
};

export default ProfileInfo;
