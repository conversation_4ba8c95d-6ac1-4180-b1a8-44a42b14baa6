// هذا الملف يستخدم لتشغيل الخادم على منصة Render
const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');
const dotenv = require('dotenv');

// تحميل متغيرات البيئة من ملف .env
const envPath = path.join(__dirname, 'backend', '.env');
if (fs.existsSync(envPath)) {
  console.log(`تحميل متغيرات البيئة من الملف: ${envPath}`);
  dotenv.config({ path: envPath });
} else {
  console.log('ملف .env غير موجود، سيتم استخدام متغيرات البيئة المعرفة في النظام');
  dotenv.config();
}

// طباعة متغيرات البيئة المهمة (مع إخفاء البيانات الحساسة)
console.log('متغيرات البيئة:');
console.log(`- NODE_ENV: ${process.env.NODE_ENV}`);
console.log(`- PORT: ${process.env.PORT}`);
console.log(`- MONGODB_URI: ${process.env.MONGODB_URI ? 'معرف' : 'غير معرف'}`);
console.log(`- JWT_SECRET: ${process.env.JWT_SECRET ? 'معرف' : 'غير معرف'}`);

// تحديد المسار الصحيح للخادم
const serverPath = path.join(__dirname, 'backend', 'src', 'server.js');

// التحقق من وجود الملف
if (!fs.existsSync(serverPath)) {
  console.error(`خطأ: ملف الخادم غير موجود في المسار ${serverPath}`);
  process.exit(1);
}

console.log(`تشغيل الخادم من المسار: ${serverPath}`);

// تشغيل الخادم
const server = spawn('node', [serverPath], {
  stdio: 'inherit',
  cwd: path.join(__dirname, 'backend')
});

// معالجة الخروج
server.on('close', (code) => {
  console.log(`تم إغلاق الخادم برمز الخروج ${code}`);
  process.exit(code);
});

// معالجة الأخطاء
server.on('error', (err) => {
  console.error('خطأ في تشغيل الخادم:', err);
  process.exit(1);
});
