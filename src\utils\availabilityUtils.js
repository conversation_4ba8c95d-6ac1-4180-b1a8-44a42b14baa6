/**
 * دالة للتحقق من توفر الحرفي بناءً على أوقات الدوام
 * @param {Object|Array} workingHours - كائن أو مصفوفة تحتوي على أوقات الدوام للحرفي
 * @returns {boolean} - إذا كان الحرفي متاحًا حاليًا أم لا
 */
export const isAvailableNow = (workingHours) => {
  if (!workingHours) return false;

  // الحصول على الوقت الحالي
  const now = new Date();
  const currentHour = now.getHours();
  const currentMinute = now.getMinutes();
  const currentTimeInMinutes = currentHour * 60 + currentMinute;

  // الحصول على اليوم الحالي
  const daysOfWeek = [
    "saturday",
    "sunday",
    "monday",
    "tuesday",
    "wednesday",
    "thursday",
    "friday",
  ];
  // تعديل الفهرس لأن getDay() يعطي 0 للأحد، 1 للاثنين، إلخ
  // نحتاج إلى تحويل فهرس getDay() إلى فهرس في مصفوفتنا التي تبدأ بالسبت
  const dayIndex = (now.getDay() + 6) % 7; // تحويل 0 (الأحد) إلى 1، 1 (الاثنين) إلى 2، ... 6 (السبت) إلى 0
  const currentDay = daysOfWeek[dayIndex];

  // طباعة معلومات التصحيح
  console.log("isAvailableNow - معلومات اليوم الحالي:", {
    getDay: now.getDay(),
    dayIndex,
    currentDay,
    daysOfWeek,
  });


  // التعامل مع workingHoursArray إذا كانت موجودة
  if (Array.isArray(workingHours)) {
    // البحث عن اليوم الحالي في المصفوفة
    const todaySchedule = workingHours.find((day) => day.day === currentDay);


    // إذا لم يتم العثور على اليوم الحالي
    if (!todaySchedule) {
      return false;
    }

    // تحويل قيمة isWorking إلى قيمة منطقية صريحة
    const isWorkingDay =
      todaySchedule.isWorking === true ||
      todaySchedule.isWorking === "true" ||
      todaySchedule.isWorking === 1 ||
      todaySchedule.isWorking === "1";



    // إذا لم يكن يوم عمل
    if (!isWorkingDay) {
      return false;
    }

    // تحويل وقت البدء والانتهاء إلى دقائق
    const startTime = todaySchedule.start || todaySchedule.from || "";
    const endTime = todaySchedule.end || todaySchedule.to || "";


    if (!startTime || !endTime) {
      return false;
    }

    // تحويل الوقت إلى دقائق
    const convertTimeToMinutes = (timeStr) => {
      if (!timeStr || typeof timeStr !== "string") return 0;
      const parts = timeStr.trim().split(":");
      if (parts.length !== 2) return 0;

      const hours = parseInt(parts[0], 10);
      const minutes = parseInt(parts[1], 10);

      if (isNaN(hours) || isNaN(minutes)) return 0;
      return hours * 60 + minutes;
    };

    const startTimeInMinutes = convertTimeToMinutes(startTime);
    const endTimeInMinutes = convertTimeToMinutes(endTime);



    // التحقق مما إذا كان الوقت الحالي ضمن ساعات العمل
    const isWithinWorkingHours =
      currentTimeInMinutes >= startTimeInMinutes &&
      currentTimeInMinutes <= endTimeInMinutes;


    return isWithinWorkingHours;
  }

  // التعامل مع كائن workingHours
  // التحقق مما إذا كان اليوم الحالي هو يوم عمل
  const todaySchedule = workingHours[currentDay];


  // إذا لم يتم العثور على اليوم الحالي
  if (!todaySchedule) {
    return false;
  }

  // تحويل قيمة isWorking إلى قيمة منطقية صريحة
  const isWorkingDay =
    todaySchedule.isWorking === true ||
    todaySchedule.isWorking === "true" ||
    todaySchedule.isWorking === 1 ||
    todaySchedule.isWorking === "1";



  // إذا لم يكن يوم عمل
  if (!isWorkingDay) {
    return false;
  }

  // تحويل وقت البدء والانتهاء إلى دقائق
  const startTime = todaySchedule.start || todaySchedule.from || "";
  const endTime = todaySchedule.end || todaySchedule.to || "";


  if (!startTime || !endTime) {
    return false;
  }

  // تحويل الوقت إلى دقائق
  const convertTimeToMinutes = (timeStr) => {
    if (!timeStr || typeof timeStr !== "string") return 0;
    const parts = timeStr.trim().split(":");
    if (parts.length !== 2) return 0;

    const hours = parseInt(parts[0], 10);
    const minutes = parseInt(parts[1], 10);

    if (isNaN(hours) || isNaN(minutes)) return 0;
    return hours * 60 + minutes;
  };

  const startTimeInMinutes = convertTimeToMinutes(startTime);
  const endTimeInMinutes = convertTimeToMinutes(endTime);



  // التحقق مما إذا كان الوقت الحالي ضمن ساعات العمل
  const isWithinWorkingHours =
    currentTimeInMinutes >= startTimeInMinutes &&
    currentTimeInMinutes <= endTimeInMinutes;


  return isWithinWorkingHours;
};

/**
 * دالة لتنسيق الوقت بتنسيق 12 ساعة مع مؤشر ص/م
 * @param {string} time - الوقت بتنسيق 24 ساعة (مثل "14:30")
 * @returns {string} - الوقت بتنسيق 12 ساعة مع مؤشر ص/م (مثل "2:30 م")
 */
export const formatTime12Hour = (time) => {
  if (!time) return "";

  const [hours, minutes] = time.split(":");
  let hour = parseInt(hours);
  const ampm = hour >= 12 ? "م" : "ص";

  hour = hour % 12;
  hour = hour ? hour : 12; // الساعة 0 تصبح 12

  return `${hour}:${minutes} ${ampm}`;
};

/**
 * دالة للحصول على أيام العمل كنص
 * @param {Object|Array} workingHours - كائن أو مصفوفة تحتوي على أوقات الدوام للحرفي
 * @returns {string} - نص يحتوي على أيام العمل
 */
export const getWorkingDaysText = (workingHours) => {
  if (!workingHours) return "غير محدد";


  const daysMap = {
    saturday: "السبت",
    sunday: "الأحد",
    monday: "الاثنين",
    tuesday: "الثلاثاء",
    wednesday: "الأربعاء",
    thursday: "الخميس",
    friday: "الجمعة",
  };

  // التعامل مع workingHoursArray إذا كانت موجودة
  if (Array.isArray(workingHours)) {

    // التحقق من وجود أي أيام عمل محددة
    const hasAnyWorkingDay = workingHours.some((day) => !!day.isWorking);


    // إذا لم يكن هناك أي يوم عمل محدد، نعرض "غير محدد"
    if (!hasAnyWorkingDay) {
      return "غير محدد";
    }

    // فلترة الأيام التي تم اختيارها فقط
    const workingDays = workingHours
      .filter((day) => !!day.isWorking)
      .map((day) => daysMap[day.day]);


    if (workingDays.length === 0) return "غير محدد";
    if (workingDays.length === 7) return "كل الأيام";

    return workingDays.join("، ");
  }

  // التعامل مع كائن workingHours

  // التحقق من وجود أي أيام عمل محددة
  const hasAnyWorkingDay = Object.values(workingHours).some((day) => {
    // تحويل القيمة إلى قيمة منطقية صريحة باستخدام !!
    const isWorking = day && !!day.isWorking;
    return isWorking;
  });


  // إذا لم يكن هناك أي يوم عمل محدد، نعرض "غير محدد"
  if (!hasAnyWorkingDay) {
    return "غير محدد";
  }

  // تحضير أيام العمل

  const workingDays = Object.entries(workingHours)
    .filter(([_, value]) => {
      // التحقق من أن اليوم يوم عمل (باستخدام isWorking فقط)
      // تحويل القيمة إلى قيمة منطقية صريحة باستخدام !!
      const isWorkingDay = value && !!value.isWorking;
      return isWorkingDay;
    })
    .map(([day, _]) => daysMap[day]);


  if (workingDays.length === 0) return "غير محدد";
  if (workingDays.length === 7) return "كل الأيام";

  return workingDays.join("، ");
};

/**
 * دالة للحصول على ساعات العمل كنص
 * @param {Object|Array} workingHours - كائن أو مصفوفة تحتوي على أوقات الدوام للحرفي
 * @returns {string} - نص يحتوي على ساعات العمل
 */
export const getWorkingHoursText = (workingHours) => {
  if (!workingHours) return "غير محدد";


  // التعامل مع workingHoursArray إذا كانت موجودة
  if (Array.isArray(workingHours)) {

    // البحث عن أول يوم عمل للحصول على ساعات العمل
    const firstWorkingDay = workingHours.find((day) => !!day.isWorking);


    // إذا لم يتم العثور على يوم عمل، نعرض "غير محدد"
    if (!firstWorkingDay || !firstWorkingDay.isWorking) {
      return "غير محدد";
    }

    // استخدام صيغة start/end أو from/to
    const startTime = firstWorkingDay.start || firstWorkingDay.from;
    const endTime = firstWorkingDay.end || firstWorkingDay.to;


    // إذا لم تكن هناك قيم للوقت، نعرض "غير محدد"
    if (!startTime || !endTime) {
      return "غير محدد";
    }

    const formattedTime = `${formatTime12Hour(startTime)} - ${formatTime12Hour(
      endTime
    )}`;

    return formattedTime;
  }

  // التعامل مع كائن workingHours

  // البحث عن أول يوم عمل للحصول على ساعات العمل
  const firstWorkingDay = Object.values(workingHours).find((day) => {
    // تحويل القيمة إلى قيمة منطقية صريحة باستخدام !!
    const isWorking = day && !!day.isWorking;
    return isWorking;
  });


  // إذا لم يتم العثور على يوم عمل، نعرض "غير محدد"
  if (!firstWorkingDay || !firstWorkingDay.isWorking) {
    return "غير محدد";
  }

  // استخدام صيغة start/end أو from/to
  const startTime = firstWorkingDay.start || firstWorkingDay.from;
  const endTime = firstWorkingDay.end || firstWorkingDay.to;


  // إذا لم تكن هناك قيم للوقت، نعرض "غير محدد"
  if (!startTime || !endTime) {
    return "غير محدد";
  }

  const formattedTime = `${formatTime12Hour(startTime)} - ${formatTime12Hour(
    endTime
  )}`;

  return formattedTime;
};
