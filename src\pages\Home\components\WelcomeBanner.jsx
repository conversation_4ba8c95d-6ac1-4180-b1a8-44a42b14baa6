import React, { useState, useEffect } from "react";
import useThemeStore from "../../../store/themeStore";
import { User, Search, Briefcase } from "lucide-react";
import SimpleLazyImage from "../../../components/common/SimpleLazyImage";

const WelcomeBanner = ({ user, userType }) => {
  const darkMode = useThemeStore((state) => state.darkMode);
  const [userImage, setUserImage] = useState(null);

  // تحديث صورة المستخدم عند تغييرها
  useEffect(() => {
    // تحديث الصورة عند تغيير المستخدم
    if (user) {
      updateUserImage();
    }

    // إضافة مستمع للحدث المخصص لتحديث الصورة
    const handleImageUpdate = (event) => {
      const userId = user?.id || user?._id;
      if (userId && event.detail.userId === userId) {
        console.log("WelcomeBanner: Received image update event:", event.detail);
        setUserImage(`${window.location.origin}${event.detail.newImagePath}`);
      }
    };

    // إضافة المستمع
    window.addEventListener('userImageUpdated', handleImageUpdate);

    // إزالة المستمع عند تفكيك المكون
    return () => {
      window.removeEventListener('userImageUpdated', handleImageUpdate);
    };
  }, [user]);

  // تحديث صورة المستخدم
  const updateUserImage = () => {
    if (!user) {
      setUserImage(null);
      return;
    }

    // استخدام الصورة المؤقتة إذا كانت موجودة
    if (user.tempImage) {
      setUserImage(user.tempImage);
      return;
    }

    // استخدام صورة المستخدم إذا كانت موجودة
    if (user.image) {
      setUserImage(user.image);
      return;
    }

    if (user.profilePicture) {
      setUserImage(user.profilePicture);
      return;
    }

    // استخدام مسار الصورة بناءً على معرف المستخدم
    const userId = user.id || user._id;
    if (userId) {
      // تجربة عدة تنسيقات للصورة
      setUserImage(`/uploads/profile-${userId}-${Date.now()}.png`);
      return;
    }

    setUserImage(null);
  };

  // تحديد مصدر الصورة المناسب للمستخدم
  const getUserImageSrc = () => {
    // استخدام الصورة المحدثة إذا كانت موجودة
    if (userImage) return userImage;

    if (!user) return null;

    // استخدام الصورة المؤقتة إذا كانت موجودة
    if (user.tempImage) return user.tempImage;

    // استخدام صورة المستخدم إذا كانت موجودة
    if (user.image) return user.image;
    if (user.profilePicture) return user.profilePicture;

    // استخدام مسار الصورة بناءً على معرف المستخدم
    const userId = user.id || user._id;
    if (userId) {
      // تجربة عدة تنسيقات للصورة
      return `/uploads/profile-${userId}-${Date.now()}.png`;
    }

    return null;
  };

  return (
    <section
      className={`relative overflow-hidden ${
        darkMode
          ? "bg-gradient-to-br from-indigo-900 via-gray-800 to-gray-900"
          : "bg-gradient-to-br from-blue-500 via-indigo-600 to-blue-700"
      } text-white py-10 shadow-xl transition-colors duration-300 rounded-xl`}
    >
      {/* Patrón decorativo */}
      <div className="absolute inset-0 opacity-10">
        <div
          className="absolute top-0 left-0 w-full h-full "
          style={{
            backgroundImage:
              "url(\"data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%23ffffff' fill-opacity='0.1' fill-rule='evenodd'/%3E%3C/svg%3E\")",
            backgroundSize: "180px 180px",
          }}
        ></div>
      </div>

      {/* Efectos de luz */}
      <div className="absolute -bottom-24 -left-24 w-64 h-64 rounded-full bg-white opacity-10 blur-xl"></div>
      <div className="absolute -top-24 -right-24 w-64 h-64 rounded-full bg-white opacity-10 blur-xl"></div>

      {/* Línea decorativa */}
      <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-transparent via-white to-transparent opacity-20"></div>

      <div className="container mx-auto px-6 relative z-10">
        <div className="flex flex-col md:flex-row items-center md:items-start md:justify-between">
          <div className="flex flex-col md:flex-row items-center mb-6 md:mb-0">
            {/* Avatar decorativo con efecto de resplandor */}
            <div className="relative mb-4 md:mb-0 md:mr-5">
              <div
                className={`w-36 h-36 ml-2 rounded-full flex items-center justify-center overflow-hidden ${
                  darkMode ? "bg-indigo-800/50" : "bg-indigo-700/50"
                } border-2 ${
                  darkMode ? "border-indigo-700" : "border-indigo-500"
                } shadow-lg relative z-10`}
              >
                {user && getUserImageSrc() ? (
                  <SimpleLazyImage
                    src={getUserImageSrc()}
                    alt={user.name}
                    className="w-full h-full object-cover"
                    placeholderClassName="w-full h-full bg-indigo-700/50 animate-pulse"
                  />
                ) : (
                  <User size={32} className="text-white/90" />
                )}
              </div>
              {/* Efecto de resplandor */}
              <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-24 h-24 rounded-full bg-indigo-500/20 blur-xl z-0"></div>
            </div>

            <div className="text-center md:text-right">
              <h1 className="text-3xl font-bold mb-3 relative inline-block">
                <span className="relative z-10">مرحباً {user.name}</span>
                <span
                  className={`absolute bottom-0 left-0 right-0 h-2 ${
                    darkMode ? "bg-indigo-500" : "bg-indigo-300"
                  } opacity-40 transform -rotate-1 z-0`}
                ></span>
              </h1>

              <div
                className={`flex items-center justify-center md:justify-start px-4 py-2 rounded-full ${
                  darkMode ? "bg-indigo-900/40" : "bg-indigo-700/40"
                } backdrop-blur-sm border ${
                  darkMode ? "border-indigo-800/50" : "border-indigo-600/50"
                }`}
              >
                {userType === "client" ? (
                  <>
                    <p className="text-md text-indigo-100 font-medium">
                      ابحث عن أفضل الحرفيين في منطقتك
                    </p>
                  </>
                ) : (
                  <>
                    <Briefcase size={16} className="ml-2 text-indigo-200" />
                    <p className="text-md text-indigo-100 font-medium">
                      استعرض طلبات العمل وأدر ملفك الشخصي
                    </p>
                  </>
                )}
              </div>
            </div>
          </div>

          {/* Elemento decorativo adicional */}
          <div className="hidden md:flex items-center">
            <div
              className={`px-4 py-3 rounded-lg ${
                darkMode ? "bg-indigo-900/40" : "bg-indigo-700/40"
              } backdrop-blur-sm border ${
                darkMode ? "border-indigo-800/50" : "border-indigo-600/50"
              } flex items-center`}
            >
              <div
                className={`w-3 h-3 rounded-full  ${
                  darkMode ? "bg-green-400" : "bg-green-400"
                } animate-pulse`}
              ></div>
              <p className="text-sm text-indigo-100 font-medium mr-2 mb-1">
                {userType === "client" ? "متصل الآن" : "وضع الحرفي نشط"}
              </p>
            </div>
          </div>
        </div>

        {/* Elementos decorativos flotantes */}
        <div className="absolute top-0 right-0 w-32 h-32 opacity-20">
          <div
            className="absolute top-10 right-10 w-4 h-4 rounded-full bg-indigo-300 animate-ping"
            style={{ animationDuration: "3s", animationDelay: "0.2s" }}
          ></div>
          <div
            className="absolute top-20 right-20 w-2 h-2 rounded-full bg-indigo-200 animate-ping"
            style={{ animationDuration: "2.5s", animationDelay: "0.5s" }}
          ></div>
          <div
            className="absolute top-5 right-32 w-3 h-3 rounded-full bg-indigo-400 animate-ping"
            style={{ animationDuration: "4s", animationDelay: "1s" }}
          ></div>
        </div>
      </div>
    </section>
  );
};

export default WelcomeBanner;
