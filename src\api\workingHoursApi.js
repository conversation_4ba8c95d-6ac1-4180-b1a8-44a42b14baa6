import api from '../services/api';

/**
 * الحصول على ساعات العمل للحرفي بناءً على معرفه
 * @param {string} craftsmanId - معرف الحرفي
 * @returns {Promise} - وعد يحتوي على بيانات ساعات العمل
 */
export const getCraftsmanWorkingHours = async (craftsmanId) => {
  try {
    // التحقق من وجود معرف الحرفي
    if (!craftsmanId) {
      throw new Error('لم يتم توفير معرف الحرفي');
    }

    // استخدام نقطة النهاية الجديدة مع api من services/api.js
    const response = await api.get(`/working-hours/craftsman/${craftsmanId}`);
    return response.data;
  } catch (error) {
    // في حالة فشل الطلب، نحاول الحصول على البيانات من نقطة نهاية بديلة
    try {
      const fallbackResponse = await api.get(`/craftsmen/${craftsmanId}`);

      if (fallbackResponse.data && fallbackResponse.data.workingHoursArray && fallbackResponse.data.workingHoursArray.length > 0) {
        return { workingHours: fallbackResponse.data.workingHoursArray };
      } else if (fallbackResponse.data && fallbackResponse.data.workingHours) {
        // تحويل كائن workingHours إلى مصفوفة workingHoursArray
        const workingHoursArray = convertWorkingHoursToArray(fallbackResponse.data.workingHours);
        return { workingHours: workingHoursArray };
      }
    } catch (fallbackError) {
      // تجاهل الخطأ
    }

    // إذا فشلت كل المحاولات، نعيد مصفوفة فارغة
    return { workingHours: [] };
  }
};

/**
 * تحويل كائن ساعات العمل إلى مصفوفة
 * @param {Object} workingHoursObj - كائن يحتوي على ساعات العمل
 * @returns {Array} - مصفوفة تحتوي على ساعات العمل
 */
export const convertWorkingHoursToArray = (workingHoursObj) => {
  if (!workingHoursObj || typeof workingHoursObj !== 'object') {
    return [];
  }

  const daysOfWeek = ["saturday", "sunday", "monday", "tuesday", "wednesday", "thursday", "friday"];
  const workingHoursArray = [];

  daysOfWeek.forEach(day => {
    if (workingHoursObj[day]) {
      workingHoursArray.push({
        day,
        isWorking: !!workingHoursObj[day].isWorking,
        start: workingHoursObj[day].start || workingHoursObj[day].from || "",
        end: workingHoursObj[day].end || workingHoursObj[day].to || ""
      });
    } else {
      // إضافة يوم بقيم افتراضية إذا لم يكن موجوداً
      workingHoursArray.push({
        day,
        isWorking: false,
        start: "",
        end: ""
      });
    }
  });

  return workingHoursArray;
};

/**
 * تحديث ساعات العمل للحرفي
 * @param {string} craftsmanId - معرف الحرفي
 * @param {Array} workingHoursArray - مصفوفة تحتوي على ساعات العمل المحدثة
 * @returns {Promise} - وعد يحتوي على نتيجة التحديث
 */
export const updateCraftsmanWorkingHours = async (craftsmanId, workingHoursArray) => {
  try {
    // التحقق من وجود معرف الحرفي
    if (!craftsmanId) {
      throw new Error('لم يتم توفير معرف الحرفي');
    }

    // إرسال طلب لتحديث ساعات العمل باستخدام api من services/api.js
    const response = await api.put(
      `/working-hours/craftsman/${craftsmanId}`,
      { workingHours: workingHoursArray }
    );

    return response.data;
  } catch (error) {
    throw error;
  }
};
