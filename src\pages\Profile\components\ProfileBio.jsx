import React, { useState } from "react";
import Card from "../../../components/common/Card";
import useThemeStore from "../../../store/themeStore";
import {
  User,
  Edit3,
  Award,
  Clock,
  CheckCircle,
  ChevronDown,
  ChevronUp,
  Plus,
} from "lucide-react";

const ProfileBio = ({
  bio,
  features = [],
  isEditing,
  onBioChange,
  onFeaturesChange,
}) => {
  const darkMode = useThemeStore((state) => state.darkMode);
  const [showFeaturesMenu, setShowFeaturesMenu] = useState(false);

  // قائمة الخصائص المتاحة
  const availableFeatures = [
    { id: "highExperience", name: "خبرة مهنية عالية", icon: Award },
    { id: "punctual", name: "التزام بالمواعيد", icon: Clock },
    { id: "qualityWork", name: "جودة عمل مضمونة", icon: CheckCircle },
    { id: "fastService", name: "خدمة سريعة", icon: Clock },
    { id: "fairPrices", name: "أسعار عادلة", icon: Award },
    { id: "cleanWork", name: "نظافة في العمل", icon: CheckCircle },
  ];

  // تأكد من أن الخصائص مصفوفة
  const featuresArray = Array.isArray(features)
    ? features
    : features
    ? [features]
    : [];

  // لا نضيف خصائص افتراضية، نترك المستخدم يختار ما يريد

  // التحقق مما إذا كانت الخاصية محددة
  const isFeatureSelected = (featureId) => {
    // البحث عن الخاصية إما بالمعرف أو بالاسم
    return featuresArray.some(
      (feature) =>
        feature === featureId ||
        feature === availableFeatures.find((f) => f.id === featureId)?.name
    );
  };

  // تبديل حالة الخاصية (إضافة/إزالة)
  const toggleFeature = (featureId) => {
    let updatedFeatures;
    if (isFeatureSelected(featureId)) {
      // إزالة الخاصية سواء كانت بالمعرف أو بالاسم
      updatedFeatures = featuresArray.filter(
        (feature) =>
          feature !== featureId &&
          feature !== availableFeatures.find((f) => f.id === featureId)?.name
      );
    } else {
      // إضافة الخاصية بالمعرف
      updatedFeatures = [...featuresArray, featureId];
    }
    console.log("تحديث الخصائص:", updatedFeatures);
    onFeaturesChange(updatedFeatures);
  };

  return (
    <Card
      className={`p-6 mb-6 rounded-xl shadow-lg ${
        darkMode
          ? "bg-gray-800 text-gray-200 border border-gray-700"
          : "bg-gradient-to-br from-blue-50/80 to-indigo-50 border border-indigo-100"
      } transition-colors duration-300 hover:shadow-xl`}
    >
      <div className="flex items-center mb-4">
        <div
          className={`p-2 rounded-full mr-3 ${
            darkMode ? "bg-indigo-900/50" : "bg-indigo-100"
          }`}
        >
          <User
            size={24}
            className={`${
              darkMode ? "text-indigo-400" : "text-indigo-500"
            } transition-colors duration-300`}
          />
        </div>
        <h2
          className={`text-xl font-bold ${
            darkMode ? "text-indigo-300" : "text-indigo-800"
          } relative inline-block transition-colors duration-300`}
        >
          <span className="relative z-10">نبذة عني</span>
          <span
            className={`absolute bottom-0 left-0 right-0 h-2 ${
              darkMode ? "bg-indigo-600" : "bg-indigo-400"
            } opacity-40 transform -rotate-1 z-0 rounded-full`}
          ></span>
        </h2>
      </div>

      {isEditing ? (
        <div className="relative">
          <textarea
            value={bio}
            onChange={(e) => {
              // طباعة النبذة للتصحيح
              console.log("تغيير النبذة في ProfileBio:", {
                oldBio: bio,
                newBio: e.target.value,
                bioType: typeof e.target.value,
                bioLength: e.target.value ? e.target.value.length : 0,
              });
              onBioChange(e.target.value);
            }}
            className={`min-h-[150px] w-full p-4 rounded-lg shadow-inner ${
              darkMode
                ? "bg-gray-700 border-gray-600 text-white focus:border-indigo-500"
                : "bg-white border-indigo-200 focus:border-indigo-500"
            } focus:ring-2 focus:ring-indigo-500/50 transition-all duration-300 resize-none`}
            placeholder="اكتب نبذة قصيرة عن نفسك، خبراتك، ومهاراتك..."
          />
          <div className="absolute top-3 left-3">
            <Edit3
              size={18}
              className={`${
                darkMode ? "text-indigo-400" : "text-indigo-500"
              } opacity-50`}
            />
          </div>
          <p
            className={`mt-2 text-xs ${
              darkMode ? "text-gray-400" : "text-gray-500"
            }`}
          >
            اكتب نبذة واضحة ومختصرة تعبر عن مهاراتك وخبراتك لجذب المزيد من
            العملاء.
          </p>

          {/* قسم اختيار الخصائص */}
          <div className="mt-4">
            <div className="flex items-center justify-between">
              <button
                type="button"
                onClick={() => setShowFeaturesMenu(!showFeaturesMenu)}
                className={`flex items-center px-4 py-2 rounded-lg ${
                  darkMode
                    ? "bg-indigo-900/30 text-indigo-300 hover:bg-indigo-900/50"
                    : "bg-indigo-100/80 text-indigo-700 hover:bg-indigo-200/80"
                } transition-all duration-300`}
              >
                <Plus size={16} className="ml-1" />
                إضافة خصائص مميزة
                {showFeaturesMenu ? (
                  <ChevronUp size={16} className="mr-1" />
                ) : (
                  <ChevronDown size={16} className="mr-1" />
                )}
              </button>

              <p
                className={`text-xs ${
                  darkMode ? "text-gray-400" : "text-gray-500"
                }`}
              >
                {features.length > 0
                  ? `تم اختيار ${features.length} خصائص`
                  : "لم يتم اختيار أي خصائص"}
              </p>
            </div>

            {/* قائمة الخصائص */}
            {showFeaturesMenu && (
              <div
                className={`mt-2 p-3 rounded-lg ${
                  darkMode
                    ? "bg-gray-800 border border-gray-700"
                    : "bg-white border border-indigo-100"
                } shadow-lg`}
              >
                <p
                  className={`text-sm mb-2 ${
                    darkMode ? "text-gray-300" : "text-gray-600"
                  }`}
                >
                  اختر الخصائص التي تميزك (ستظهر في صفحة ملفك الشخصي):
                </p>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                  {availableFeatures.map((feature) => {
                    const Icon = feature.icon;
                    const isSelected = isFeatureSelected(feature.id);
                    return (
                      <button
                        key={feature.id}
                        type="button"
                        onClick={() => toggleFeature(feature.id)}
                        className={`flex items-center p-2 rounded-lg transition-all duration-300 ${
                          isSelected
                            ? darkMode
                              ? "bg-indigo-700/50 border border-indigo-600"
                              : "bg-indigo-100 border border-indigo-300"
                            : darkMode
                            ? "bg-gray-700/50 border border-gray-600 hover:bg-gray-700"
                            : "bg-gray-100/50 border border-gray-200 hover:bg-gray-200/50"
                        }`}
                      >
                        <Icon
                          size={18}
                          className={`ml-2 ${
                            isSelected
                              ? darkMode
                                ? "text-indigo-300"
                                : "text-indigo-600"
                              : darkMode
                              ? "text-gray-400"
                              : "text-gray-500"
                          }`}
                        />
                        <span
                          className={`text-sm ${
                            isSelected
                              ? darkMode
                                ? "text-indigo-300"
                                : "text-indigo-700"
                              : darkMode
                              ? "text-gray-300"
                              : "text-gray-600"
                          }`}
                        >
                          {feature.name}
                        </span>
                      </button>
                    );
                  })}
                </div>
              </div>
            )}

            {/* عرض الخصائص المختارة */}
            {featuresArray.length > 0 && (
              <div className="mt-3 grid grid-cols-1 md:grid-cols-3 gap-2">
                {featuresArray.map((featureIdOrName, index) => {
                  // البحث عن الخاصية إما بالمعرف أو بالاسم
                  let feature = availableFeatures.find(
                    (f) => f.id === featureIdOrName
                  );

                  // إذا لم يتم العثور على الخاصية بالمعرف، نبحث عنها بالاسم
                  if (!feature) {
                    feature = availableFeatures.find(
                      (f) => f.name === featureIdOrName
                    );
                  }

                  // إذا لم يتم العثور على الخاصية، نستخدم قيمة افتراضية
                  if (!feature) {
                    // تحديد أيقونة افتراضية بناءً على الفهرس
                    const defaultIcons = [Award, Clock, CheckCircle];
                    const DefaultIcon =
                      defaultIcons[index % defaultIcons.length];

                    return (
                      <div
                        key={`feature-${index}`}
                        className={`flex items-center p-2 rounded-lg ${
                          darkMode
                            ? "bg-indigo-900/30 border border-indigo-800/50"
                            : "bg-indigo-50 border border-indigo-200"
                        }`}
                      >
                        <DefaultIcon
                          size={18}
                          className={`ml-2 ${
                            darkMode ? "text-indigo-400" : "text-indigo-500"
                          }`}
                        />
                        <span
                          className={`text-sm ${
                            darkMode ? "text-indigo-300" : "text-indigo-700"
                          }`}
                        >
                          {featureIdOrName}
                        </span>
                      </div>
                    );
                  }

                  const Icon = feature.icon;
                  return (
                    <div
                      key={`feature-${index}`}
                      className={`flex items-center p-2 rounded-lg ${
                        darkMode
                          ? "bg-indigo-900/30 border border-indigo-800/50"
                          : "bg-indigo-50 border border-indigo-200"
                      }`}
                    >
                      <Icon
                        size={18}
                        className={`ml-2 ${
                          darkMode ? "text-indigo-400" : "text-indigo-500"
                        }`}
                      />
                      <span
                        className={`text-sm ${
                          darkMode ? "text-indigo-300" : "text-indigo-700"
                        }`}
                      >
                        {feature.name}
                      </span>
                    </div>
                  );
                })}
              </div>
            )}
          </div>
        </div>
      ) : (
        <div
          className={`p-4 rounded-lg ${
            darkMode
              ? "bg-gray-700/50 border border-gray-600/50"
              : "bg-white/80 border border-indigo-100"
          } transition-colors duration-300 shadow-inner`}
        >
          {bio ? (
            <p
              className={`${
                darkMode ? "text-indigo-300" : "text-indigo-700"
              } leading-relaxed transition-colors duration-300 whitespace-pre-wrap`}
            >
              {bio}
            </p>
          ) : (
            <p
              className={`${
                darkMode ? "text-gray-400" : "text-gray-500"
              } text-center py-4 italic`}
            >
              لم يتم إضافة نبذة بعد. قم بتعديل ملفك الشخصي لإضافة نبذة عنك.
            </p>
          )}

          {/* عرض الخصائص المختارة في وضع العرض */}
          {featuresArray.length > 0 && (
            <div className="mt-4 grid grid-cols-1 md:grid-cols-3 gap-3">
              {featuresArray.map((featureIdOrName, index) => {
                // البحث عن الخاصية إما بالمعرف أو بالاسم
                let feature = availableFeatures.find(
                  (f) => f.id === featureIdOrName
                );

                // إذا لم يتم العثور على الخاصية بالمعرف، نبحث عنها بالاسم
                if (!feature) {
                  feature = availableFeatures.find(
                    (f) => f.name === featureIdOrName
                  );
                }

                // إذا لم يتم العثور على الخاصية، نستخدم قيمة افتراضية
                if (!feature) {
                  // تحديد أيقونة افتراضية بناءً على الفهرس
                  const defaultIcons = [Award, Clock, CheckCircle];
                  const DefaultIcon = defaultIcons[index % defaultIcons.length];

                  return (
                    <div
                      key={`feature-${index}`}
                      className={`flex items-center p-3 rounded-lg ${
                        darkMode
                          ? "bg-gray-800/70 border border-gray-700"
                          : "bg-white/70 border border-indigo-100"
                      }`}
                    >
                      <DefaultIcon
                        size={20}
                        className={`ml-2 ${
                          darkMode ? "text-indigo-400" : "text-indigo-500"
                        }`}
                      />
                      <span
                        className={`text-sm font-medium ${
                          darkMode ? "text-indigo-300" : "text-indigo-700"
                        }`}
                      >
                        {featureIdOrName}
                      </span>
                    </div>
                  );
                }

                const Icon = feature.icon;
                return (
                  <div
                    key={`feature-${index}`}
                    className={`flex items-center p-3 rounded-lg ${
                      darkMode
                        ? "bg-gray-800/70 border border-gray-700"
                        : "bg-white/70 border border-indigo-100"
                    }`}
                  >
                    <Icon
                      size={20}
                      className={`ml-2 ${
                        darkMode ? "text-indigo-400" : "text-indigo-500"
                      }`}
                    />
                    <span
                      className={`text-sm font-medium ${
                        darkMode ? "text-indigo-300" : "text-indigo-700"
                      }`}
                    >
                      {feature.name}
                    </span>
                  </div>
                );
              })}
            </div>
          )}
        </div>
      )}
    </Card>
  );
};

export default ProfileBio;
