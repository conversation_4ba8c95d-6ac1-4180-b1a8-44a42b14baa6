import React from "react";
import { Home, Search, ClipboardList, Settings } from "lucide-react";

/**
 * مكون أزرار الإجراءات السريعة
 * @param {Object} props - خصائص المكون
 * @param {boolean} props.darkMode - وضع الألوان الداكنة
 * @param {Function} props.onNavigate - دالة تنفذ عند الضغط على زر التنقل
 */
const ChatActions = ({ darkMode, onNavigate }) => {
  return (
    <div
      className={`p-2 flex justify-around items-center border-b ${
        darkMode
          ? "bg-gray-800 border-gray-700"
          : "bg-gray-100 border-gray-200"
      }`}
    >
      <button
        onClick={() => onNavigate("/")}
        className={`p-2 rounded-full flex flex-col items-center justify-center ${
          darkMode ? "hover:bg-gray-700" : "hover:bg-gray-200"
        }`}
        title="الصفحة الرئيسية"
      >
        <Home
          size={20}
          className={darkMode ? "text-white" : "text-gray-700"}
        />
        <span
          className={`text-xs mt-1 ${
            darkMode ? "text-gray-300" : "text-gray-600"
          }`}
        >
          الرئيسية
        </span>
      </button>

      <button
        onClick={() => onNavigate("/search")}
        className={`p-2 rounded-full flex flex-col items-center justify-center ${
          darkMode ? "hover:bg-gray-700" : "hover:bg-gray-200"
        }`}
        title="البحث عن حرفي"
      >
        <Search
          size={20}
          className={darkMode ? "text-white" : "text-gray-700"}
        />
        <span
          className={`text-xs mt-1 ${
            darkMode ? "text-gray-300" : "text-gray-600"
          }`}
        >
          البحث
        </span>
      </button>

      <button
        onClick={() => onNavigate("/bookings")}
        className={`p-2 rounded-full flex flex-col items-center justify-center ${
          darkMode ? "hover:bg-gray-700" : "hover:bg-gray-200"
        }`}
        title="طلباتي"
      >
        <ClipboardList
          size={20}
          className={darkMode ? "text-white" : "text-gray-700"}
        />
        <span
          className={`text-xs mt-1 ${
            darkMode ? "text-gray-300" : "text-gray-600"
          }`}
        >
          طلباتي
        </span>
      </button>

      <button
        onClick={() => onNavigate("/settings")}
        className={`p-2 rounded-full flex flex-col items-center justify-center ${
          darkMode ? "hover:bg-gray-700" : "hover:bg-gray-200"
        }`}
        title="الإعدادات"
      >
        <Settings
          size={20}
          className={darkMode ? "text-white" : "text-gray-700"}
        />
        <span
          className={`text-xs mt-1 ${
            darkMode ? "text-gray-300" : "text-gray-600"
          }`}
        >
          الإعدادات
        </span>
      </button>
    </div>
  );
};

export default ChatActions;
