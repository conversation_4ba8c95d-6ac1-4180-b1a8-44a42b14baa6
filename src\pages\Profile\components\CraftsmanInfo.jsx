import React, { useEffect, useState } from "react";
import { Star, MapPin, Phone, Calendar, AlertCircle, EyeOff } from "lucide-react";
import { <PERSON> } from "react-router-dom";
import Card from "../../../components/common/Card";
import Button from "../../../components/common/Button";
import SimpleLazyImage from "../../../components/common/SimpleLazyImage";
import useReviewStore from "../../../store/reviewStore";
import useUserStore from "../../../store/userStore";
import { getRelatedSpecializations } from "../../../data/professionsData";

const CraftsmanInfo = ({ craftsman, darkMode, onBookingClick, reviews }) => {
  // التحقق من حالة تسجيل الدخول باستخدام المتجر
  const isAuthenticated = useUserStore((state) => state.isAuthenticated);

  // تعريف تأثيرات النبض للحالة
  const pulseStyles = `
  @keyframes pulse-green {
    0% {
      box-shadow: 0 0 0 0 rgba(34, 197, 94, 0.7);
    }
    70% {
      box-shadow: 0 0 0 8px rgba(34, 197, 94, 0);
    }
    100% {
      box-shadow: 0 0 0 0 rgba(34, 197, 94, 0);
    }
  }

  @keyframes pulse-red {
    0% {
      box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7);
    }
    70% {
      box-shadow: 0 0 0 8px rgba(239, 68, 68, 0);
    }
    100% {
      box-shadow: 0 0 0 0 rgba(239, 68, 68, 0);
    }
  }
  `;

  // إضافة تأثيرات النبض إلى الصفحة
  useEffect(() => {
    const styleElement = document.createElement("style");
    styleElement.textContent = pulseStyles;
    document.head.appendChild(styleElement);

    return () => {
      document.head.removeChild(styleElement);
    };
  }, []);

  // استخدام متجر التقييمات
  const getCraftsmanReviews = useReviewStore(
    (state) => state.getCraftsmanReviews
  );
  const getCraftsmanAverageRating = useReviewStore(
    (state) => state.getCraftsmanAverageRating
  );

  // حالة التقييمات
  const [averageRating, setAverageRating] = useState(0);
  const [reviewsCount, setReviewsCount] = useState(0);

  // جلب التقييمات وحساب المتوسط
  useEffect(() => {
    const updateRatings = async () => {
      if (craftsman && (craftsman.id || craftsman._id)) {
        const craftsmanId = craftsman.id || craftsman._id;

        // استخدام قيمة التقييم المخزنة في كائن الحرفي إذا كانت موجودة
        if (
          craftsman.rating !== undefined &&
          craftsman.reviewCount !== undefined
        ) {
          console.log("استخدام التقييم المخزن في كائن الحرفي:", {
            rating: craftsman.rating,
            reviewCount: craftsman.reviewCount,
          });
          setAverageRating(parseFloat(craftsman.rating) || 0);
          setReviewsCount(craftsman.reviewCount || 0);
        } else {
          // محاولة تحميل التقييمات من الخادم أولاً
          try {
            console.log(
              "محاولة تحميل التقييمات من الخادم في CraftsmanInfo:",
              craftsmanId
            );
            const fetchReviews = useReviewStore.getState().fetchReviews;
            await fetchReviews(craftsmanId);
          } catch (error) {
            console.error(
              "خطأ في تحميل التقييمات من الخادم في CraftsmanInfo:",
              error
            );
          }

          // جلب التقييمات من متجر التقييمات (بعد التحديث من الخادم)
          const craftsmanReviews = getCraftsmanReviews(craftsmanId);
          setReviewsCount(craftsmanReviews.length);

          // حساب متوسط التقييم
          if (craftsmanReviews.length > 0) {
            const avgRating = getCraftsmanAverageRating(craftsmanId);
            setAverageRating(avgRating);
          } else {
            // إذا لم تكن هناك تقييمات، استخدم قيمة افتراضية 5
            setAverageRating(5);
            // إضافة تقييم افتراضي واحد
            setReviewsCount(1);
          }

          console.log("معلومات التقييم من متجر التقييمات:", {
            craftsmanId,
            reviewsCount: craftsmanReviews.length,
            averageRating:
              craftsmanReviews.length > 0
                ? getCraftsmanAverageRating(craftsmanId)
                : 0,
          });
        }
      }
    };

    updateRatings();
  }, [craftsman, getCraftsmanReviews, getCraftsmanAverageRating, reviews]);
  return (
    <Card
      className={`overflow-hidden rounded-xl shadow-xl ${
        darkMode
          ? "bg-gray-800 text-gray-200 border border-gray-700"
          : "bg-gradient-to-br from-blue-50/80 to-indigo-50 border border-indigo-100"
      } transition-colors duration-300`}
    >
      {/* هيدر البطاقة مع خلفية متدرجة */}
      <div className="relative">
        {/* صورة الخلفية الضبابية */}
        <div
          className={`absolute inset-0 h-48 ${
            darkMode ? "bg-gray-900" : "bg-gray-100"
          } overflow-hidden`}
        >
          {/* صورة الخلفية الضبابية */}
          <div
            className="absolute inset-0 bg-center bg-cover filter blur-md opacity-50"
            style={{
              backgroundImage: darkMode
                ? `url('https://images.unsplash.com/photo-1478760329108-5c3ed9d495a0?q=80&w=1000&auto=format&fit=crop')`
                : `url('https://images.unsplash.com/photo-1557682224-5b8590cd9ec5?q=80&w=1000&auto=format&fit=crop')`,
              transform: "scale(1.1)",
            }}
          ></div>
          {/* طبقة تراكب للتحكم في الألوان */}
          <div
            className={`absolute inset-0 ${
              darkMode
                ? "bg-gradient-to-br from-black/90 via-indigo-950/80 to-black/90"
                : "bg-gradient-to-br from-indigo-950/80 via-blue-900/70 to-indigo-950/80"
            }`}
          ></div>
          {/* أشكال زخرفية */}
          <div className="absolute inset-0 overflow-hidden">
            {/* نمط هندسي */}

            {/* دوائر زخرفية متحركة */}
            <div className="absolute top-5 right-10 w-24 h-24 rounded-full bg-white opacity-5 animate-pulse"></div>
            <div
              className="absolute top-20 right-20 w-16 h-16 rounded-full bg-white opacity-5 animate-pulse"
              style={{ animationDelay: "1s" }}
            ></div>
            <div
              className="absolute bottom-5 left-10 w-20 h-20 rounded-full bg-white opacity-5 animate-pulse"
              style={{ animationDelay: "1.5s" }}
            ></div>

            {/* خطوط زخرفية */}
            <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-white to-transparent opacity-20"></div>
            <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-white to-transparent opacity-20"></div>
          </div>

          {/* تأثير التدرج */}
          <div
            className={`absolute inset-0 ${
              darkMode
                ? "bg-gradient-to-t from-gray-900/90 via-gray-900/50 to-transparent"
                : "bg-gradient-to-t from-indigo-500 via-indigo-800 to-transparent"
            }`}
          ></div>

          {/* نص حالة التوفر */}
          <div
            className={`absolute bottom-2 right-2 px-4 py-1.5 rounded-full text-center text-xs font-bold shadow-md border border-white/30 flex items-center justify-center ${
              craftsman.available
                ? "bg-gradient-to-r from-green-600 to-green-500 text-white"
                : "bg-gradient-to-r from-red-600 to-red-500 text-white"
            }`}
            style={{
              zIndex: 20,
              minWidth: "80px",
              animation: craftsman.available
                ? "pulse-green 1.5s infinite"
                : "pulse-red 1.5s infinite",
            }}
          >
            {craftsman.available ? <>متاح الآن</> : <>غير متاح</>}
          </div>
        </div>

        {/* صورة الملف الشخصي - موضوعة في المنتصف وإلى الأعلى قليلاً */}
        <div
          className="relative px-6 py-4 flex justify-center"
          style={{ height: "180px" }}
        >
          <div className="absolute z-10 top-6 w-40 h-40 flex items-center justify-center">
            {/* حلقات خارجية متحركة */}
            <div className="absolute inset-0 rounded-full border-4 border-blue-300/40 animate-pulse"></div>
            <div
              className="absolute inset-0 rounded-full border-2 border-indigo-400/30 animate-pulse"
              style={{ animationDuration: "3s" }}
            ></div>

            {/* تأثير الهالة */}
            <div className="absolute inset-0 rounded-full bg-indigo-500/20 filter blur-md"></div>

            {/* الصورة الرئيسية */}
            <div
              className="relative w-36 h-36 rounded-full overflow-hidden border-4 transform hover:scale-105 transition-all duration-500 bg-white"
              style={{
                borderColor: darkMode ? "#4338C8" : "#6366f1",
                boxShadow: darkMode
                  ? "0 0 25px rgba(67, 56, 200, 0.5), inset 0 0 15px rgba(79, 70, 229, 0.2)"
                  : "0 0 30px rgba(79, 70, 229, 0.4), inset 0 0 10px rgba(99, 102, 241, 0.1)",
              }}
            >
              {/* تأثير الإضاءة */}
              <div className="absolute inset-0 bg-gradient-to-tr from-indigo-500/10 to-transparent"></div>

              <SimpleLazyImage
                src={
                  craftsman.image ||
                  craftsman.profilePicture ||
                  (craftsman.user &&
                    (craftsman.user.image || craftsman.user.profilePicture)) ||
                  "/img/default-avatar-2-modified.svg"
                }
                alt={
                  craftsman.name ||
                  (craftsman.user && craftsman.user.name) ||
                  "حرفي"
                }
                className="w-full h-full object-cover"
                placeholderClassName="w-full h-full bg-gray-200 animate-pulse"
              />

              {/* تأثير التوهج عند التحويم */}
              <div className="absolute inset-0 bg-indigo-500/0 hover:bg-indigo-500/10 transition-colors duration-300"></div>
            </div>
          </div>
        </div>
      </div>

      {/* محتوى البطاقة */}
      <div className="relative mt-16 px-6 pb-6">
        <div className="text-center mb-6">
          {/* الاسم مع تأثير خط تحته */}
          <div className="relative inline-block">
            <h1
              className={`text-2xl font-bold mb-1 ${
                darkMode ? "text-indigo-300" : "text-indigo-800"
              } transition-colors duration-300 relative z-10`}
            >
              {craftsman.name ||
                (craftsman.user && craftsman.user.name) ||
                "حرفي"}
            </h1>
            <span
              className={`absolute bottom-0 left-0 right-0 h-1 ${
                darkMode ? "bg-indigo-600" : "bg-indigo-400"
              } opacity-40 transform -rotate-1 z-0 rounded-full`}
            ></span>
          </div>
        </div>

        {/* معلومات إضافية */}
        <div className="mb-6">
          {/* التقييمات */}
          <div
            className={`flex flex-col items-center justify-center p-4 rounded-xl ${
              darkMode
                ? "bg-gray-800/80 border border-indigo-900/50"
                : "bg-gradient-to-br from-indigo-50 to-blue-50 border border-indigo-100"
            } transition-all duration-300 transform hover:scale-105 shadow-md hover:shadow-lg`}
          >
            <div className="text-sm font-medium mb-2 text-center">
              <span
                className={darkMode ? "text-indigo-300" : "text-indigo-700"}
              >
                التقييم العام
              </span>
            </div>

            <div className="flex items-center justify-center mb-1">
              {[1, 2, 3, 4, 5].map((star) => (
                <Star
                  key={star}
                  size={20}
                  className={`${
                    star <= Math.round(averageRating)
                      ? darkMode
                        ? "text-yellow-400 fill-yellow-400"
                        : "text-yellow-500 fill-yellow-500"
                      : darkMode
                      ? "text-gray-600"
                      : "text-gray-300"
                  } transition-all duration-300 transform ${
                    star <= Math.round(averageRating) ? "scale-110" : ""
                  }`}
                />
              ))}
            </div>

            <div className="flex items-center justify-center">
              <span
                className={`font-bold text-xl ${
                  darkMode ? "text-yellow-400" : "text-yellow-600"
                } transition-colors duration-300`}
              >
                {averageRating > 0 ? averageRating.toFixed(1) : "5.0"}
              </span>
              <span
                className={`mr-1 text-sm ${
                  darkMode ? "text-gray-400" : "text-gray-500"
                } transition-colors duration-300`}
              >
                /5
              </span>
              <span
                className={`mr-2 ${
                  darkMode ? "text-indigo-400" : "text-indigo-500"
                } transition-colors duration-300 text-sm`}
              >
                ({reviewsCount > 0 ? reviewsCount : 1} تقييم)
              </span>
            </div>
          </div>
        </div>

        {/* أزرار الإجراءات */}
        <div className="grid grid-cols-1 gap-4 mt-2">
          {isAuthenticated ? (
            <>
              {/* زر الحجز - يظهر فقط إذا لم تكن معلومات الاتصال مخفية */}
              {!craftsman.hideContactInfo && (
                <Button
                  variant="primary"
                  className={`text-white flex items-center justify-center gap-2 shadow-lg hover:shadow-xl transition-all duration-300 relative overflow-hidden group py-4 px-6 rounded-xl ${
                    darkMode
                      ? "bg-gradient-to-r from-indigo-700 via-indigo-600 to-purple-800 hover:from-indigo-800 hover:via-indigo-700 hover:to-purple-900"
                      : "bg-gradient-to-r from-blue-600 via-indigo-600 to-indigo-700 hover:from-blue-700 hover:via-indigo-700 hover:to-indigo-800"
                  }`}
                  onClick={(e) => {
                    e.preventDefault();
                    onBookingClick();
                  }}
                >
                  <span className="relative z-10 flex items-center font-bold text-lg">
                    <Calendar size={20} className="ml-2" />
                    احجز موعداً
                  </span>
                  {/* تأثيرات متحركة */}
                  <span className="absolute inset-0 bg-white opacity-20 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700"></span>
                  <span className="absolute inset-0 opacity-0 group-hover:opacity-20 bg-gradient-to-r from-yellow-400 via-pink-500 to-indigo-500 transition-opacity duration-700"></span>
                  {/* تأثير الظل */}
                  <span className="absolute inset-0 opacity-0 group-hover:opacity-100 shadow-inner transition-opacity duration-300"></span>
                </Button>
              )}

              {/* زر الاتصال - يظهر فقط إذا لم تكن معلومات الاتصال مخفية */}
              {!craftsman.hideContactInfo && (
                <Button
                  variant="secondary"
                  className={`flex items-center justify-center gap-2 shadow-md hover:shadow-lg transition-all duration-300 relative overflow-hidden group py-4 px-6 rounded-xl ${
                    darkMode
                      ? "bg-gray-800 text-indigo-300 border border-indigo-800/50 hover:bg-gray-700"
                      : "bg-white text-indigo-700 border border-indigo-200 hover:bg-indigo-50"
                  }`}
                  onClick={() =>
                    craftsman.phone
                      ? window.open(`tel:${craftsman.phone}`)
                      : alert("رقم الهاتف غير متوفر")
                  }
                  disabled={!craftsman.available}
                >
                  <span className="relative z-10 flex items-center font-bold text-lg">
                    <Phone size={20} className="ml-2" />
                    اتصل الآن
                  </span>
                  {/* تأثير الظل */}
                  <span className="absolute inset-0 opacity-0 group-hover:opacity-100 shadow-inner transition-opacity duration-300"></span>
                  {/* تأثير الخلفية */}
                  <span
                    className={`absolute inset-0 opacity-0 group-hover:opacity-10 ${
                      darkMode ? "bg-indigo-500" : "bg-indigo-600"
                    } transition-opacity duration-300`}
                  ></span>
                </Button>
              )}

              {/* رسالة عندما تكون معلومات الاتصال مخفية */}
              {craftsman.hideContactInfo && (
                <div
                  className={`flex items-center justify-center mt-1 text-sm ${
                    darkMode ? "text-red-300" : "text-red-600"
                  } bg-red-500/10 p-4 rounded-lg border ${
                    darkMode ? "border-red-700/30" : "border-red-200"
                  }`}
                >
                  <EyeOff size={18} className="ml-2" />
                  <span>معلومات الاتصال غير متاحة حالياً</span>
                </div>
              )}
            </>
          ) : (
            <>
              <Link to="/login" className="w-full">
                <Button
                  variant="primary"
                  className={`text-white flex items-center justify-center gap-2 shadow-lg hover:shadow-xl transition-all duration-300 relative overflow-hidden group py-4 px-6 w-full rounded-xl ${
                    darkMode
                      ? "bg-gradient-to-r from-indigo-700 via-indigo-600 to-purple-800 hover:from-indigo-800 hover:via-indigo-700 hover:to-purple-900"
                      : "bg-gradient-to-r from-blue-600 via-indigo-600 to-indigo-700 hover:from-blue-700 hover:via-indigo-700 hover:to-indigo-800"
                  }`}
                >
                  <span className="relative z-10 flex items-center font-bold text-lg">
                    <Calendar size={20} className="ml-2" />
                    سجل دخول للحجز
                  </span>
                  {/* تأثيرات متحركة */}
                  <span className="absolute inset-0 bg-white opacity-20 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700"></span>
                  <span className="absolute inset-0 opacity-0 group-hover:opacity-20 bg-gradient-to-r from-yellow-400 via-pink-500 to-indigo-500 transition-opacity duration-700"></span>
                </Button>
              </Link>

              <Link to="/login" className="w-full">
                <Button
                  variant="secondary"
                  className={`flex items-center justify-center gap-2 shadow-md hover:shadow-lg transition-all duration-300 relative overflow-hidden group py-4 px-6 w-full rounded-xl ${
                    darkMode
                      ? "bg-gray-800 text-indigo-300 border border-indigo-800/50 hover:bg-gray-700"
                      : "bg-white text-indigo-700 border border-indigo-200 hover:bg-indigo-50"
                  }`}
                >
                  <span className="relative z-10 flex items-center font-bold text-lg">
                    <Phone size={20} className="ml-2" />
                    سجل دخول للاتصال
                  </span>
                  {/* تأثير الظل */}
                  <span className="absolute inset-0 opacity-0 group-hover:opacity-100 shadow-inner transition-opacity duration-300"></span>
                  {/* تأثير الخلفية */}
                  <span
                    className={`absolute inset-0 opacity-0 group-hover:opacity-10 ${
                      darkMode ? "bg-indigo-500" : "bg-indigo-600"
                    } transition-opacity duration-300`}
                  ></span>
                </Button>
              </Link>

              <div
                className={`flex items-center justify-center mt-1 text-sm ${
                  darkMode ? "text-yellow-300" : "text-yellow-600"
                } bg-yellow-500/10 p-2 rounded-lg border ${
                  darkMode ? "border-yellow-700/30" : "border-yellow-200"
                }`}
              >
                <AlertCircle size={18} className="ml-2" />
                <span>يجب تسجيل الدخول للتواصل مع الحرفي</span>
              </div>
            </>
          )}
        </div>
      </div>
    </Card>
  );
};

export default CraftsmanInfo;
