// سكريبت لإنشاء المجموعات المتبقية في قاعدة بيانات MongoDB

const { MongoClient } = require('mongodb');

// رابط الاتصال بقاعدة البيانات
const uri = "mongodb+srv://jobscope_user:<EMAIL>/jobscope?retryWrites=true&w=majority";
const dbName = "jobscope";

// خيارات الاتصال
const options = {
  useNewUrlParser: true,
  useUnifiedTopology: true,
  serverSelectionTimeoutMS: 30000,
  socketTimeoutMS: 45000,
};

// إنشاء عميل MongoDB
const client = new MongoClient(uri, options);

// بيانات المهن الأولية
const professionsData = [
  {
    name: "كهرباء",
    icon: "electricity",
    category: "خدمات الصيانة والإصلاح",
    description: "خدمات الكهرباء وتركيب وإصلاح الأجهزة الكهربائية"
  },
  {
    name: "سباكة",
    icon: "plumbing",
    category: "خدمات الصيانة والإصلاح",
    description: "خدمات السباكة وإصلاح تسربات المياه وتركيب الأدوات الصحية"
  },
  {
    name: "نجارة",
    icon: "carpentry",
    category: "خدمات الصيانة والإصلاح",
    description: "خدمات النجارة وصناعة وإصلاح الأثاث الخشبي"
  },
  {
    name: "دهان",
    icon: "painting",
    category: "أعمال البناء",
    description: "خدمات الدهان والطلاء للمنازل والمباني"
  },
  {
    name: "تنظيف",
    icon: "cleaning",
    category: "خدمات التنظيف",
    description: "خدمات تنظيف المنازل والمكاتب والمباني"
  },
  {
    name: "بستنة",
    icon: "gardening",
    category: "الزراعة والحدائق",
    description: "خدمات العناية بالحدائق والنباتات"
  },
  {
    name: "تكييف وتبريد",
    icon: "ac",
    category: "خدمات الصيانة والإصلاح",
    description: "تركيب وصيانة أجهزة التكييف والتبريد"
  },
  {
    name: "بناء",
    icon: "construction",
    category: "أعمال البناء",
    description: "خدمات البناء والترميم"
  }
];

// بيانات التخصصات الفرعية الأولية
const specializationsData = [
  {
    professionId: null, // سيتم تحديثه بعد إنشاء المهن
    name: "تمديدات كهربائية",
    description: "تركيب وصيانة التمديدات الكهربائية المنزلية والتجارية"
  },
  {
    professionId: null,
    name: "إصلاح أجهزة كهربائية",
    description: "إصلاح وصيانة الأجهزة الكهربائية المنزلية"
  },
  {
    professionId: null,
    name: "تركيب سخانات",
    description: "تركيب وصيانة سخانات المياه"
  },
  {
    professionId: null,
    name: "إصلاح تسربات",
    description: "كشف وإصلاح تسربات المياه"
  },
  {
    professionId: null,
    name: "تركيب أدوات صحية",
    description: "تركيب وصيانة الأدوات الصحية"
  },
  {
    professionId: null,
    name: "صناعة أثاث",
    description: "تصميم وصناعة الأثاث الخشبي"
  },
  {
    professionId: null,
    name: "إصلاح أثاث",
    description: "إصلاح وترميم الأثاث الخشبي"
  }
];

// بيانات إعدادات الموقع الأولية
const settingsData = {
  siteName: "JobScope",
  siteDescription: "منصة للربط بين طالبي الخدمة والحرفيين",
  siteEmail: "<EMAIL>",
  siteLogo: "/img/Favicon/favicon-96x96.png",
  sitePhone: "+963 912 345 678",
  siteAddress: "دمشق، سوريا",
  siteWorkingHours: "24/7",
  defaultLanguage: "ar",
  defaultTheme: "light",
  createdAt: new Date(),
  updatedAt: new Date()
};

// بيانات التقييمات الأولية
const reviewsData = [
  {
    bookingId: null, // سيتم تحديثه لاحقًا
    craftsmanId: null, // سيتم تحديثه لاحقًا
    clientId: null, // سيتم تحديثه لاحقًا
    overallRating: 4.5,
    qualityRating: 4,
    punctualityRating: 5,
    priceRating: 4,
    communicationRating: 5,
    comment: "خدمة ممتازة وسريعة، أنصح بالتعامل معه",
    images: [],
    createdAt: new Date(),
    updatedAt: new Date()
  }
];

// بيانات الطلبات الأولية
const requestsData = [
  {
    clientId: null, // سيتم تحديثه لاحقًا
    professionId: null, // سيتم تحديثه لاحقًا
    title: "إصلاح تسرب مياه",
    description: "يوجد تسرب مياه في المطبخ بحاجة إلى إصلاح عاجل",
    location: {
      address: "دمشق - المزة",
      lat: 33.5138,
      lng: 36.2765
    },
    status: "pending",
    createdAt: new Date(),
    updatedAt: new Date()
  }
];

async function run() {
  try {
    // الاتصال بقاعدة البيانات
    await client.connect();
    console.log("تم الاتصال بقاعدة البيانات بنجاح");

    // الوصول إلى قاعدة البيانات
    const db = client.db(dbName);

    // 1. عرض المجموعات الموجودة حاليًا
    console.log("\n=== المجموعات الموجودة حاليًا في قاعدة البيانات ===");
    const existingCollections = await db.listCollections().toArray();
    const existingCollectionNames = existingCollections.map(c => c.name);
    existingCollectionNames.forEach(name => {
      console.log(`- ${name}`);
    });

    // 2. إنشاء مجموعة المهن (professions) إذا لم تكن موجودة
    if (!existingCollectionNames.includes('professions')) {
      await db.createCollection('professions');
      console.log("\n=== تم إنشاء مجموعة المهن (professions) ===");
      
      // إضافة بيانات المهن الأولية
      if (professionsData.length > 0) {
        const result = await db.collection('professions').insertMany(professionsData);
        console.log(`- تم إضافة ${result.insertedCount} مهنة إلى المجموعة`);
      }
    } else {
      console.log("\n=== مجموعة المهن (professions) موجودة بالفعل ===");
    }

    // 3. إنشاء مجموعة التخصصات (specializations) إذا لم تكن موجودة
    if (!existingCollectionNames.includes('specializations')) {
      await db.createCollection('specializations');
      console.log("\n=== تم إنشاء مجموعة التخصصات (specializations) ===");
      
      // الحصول على معرفات المهن لربطها بالتخصصات
      const professions = await db.collection('professions').find({}).toArray();
      
      // تحديث معرفات المهن في بيانات التخصصات
      if (professions.length > 0) {
        specializationsData[0].professionId = professions[0]._id; // كهرباء - تمديدات كهربائية
        specializationsData[1].professionId = professions[0]._id; // كهرباء - إصلاح أجهزة كهربائية
        specializationsData[2].professionId = professions[1]._id; // سباكة - تركيب سخانات
        specializationsData[3].professionId = professions[1]._id; // سباكة - إصلاح تسربات
        specializationsData[4].professionId = professions[1]._id; // سباكة - تركيب أدوات صحية
        specializationsData[5].professionId = professions[2]._id; // نجارة - صناعة أثاث
        specializationsData[6].professionId = professions[2]._id; // نجارة - إصلاح أثاث
        
        // إضافة بيانات التخصصات الأولية
        const result = await db.collection('specializations').insertMany(specializationsData);
        console.log(`- تم إضافة ${result.insertedCount} تخصص إلى المجموعة`);
      }
    } else {
      console.log("\n=== مجموعة التخصصات (specializations) موجودة بالفعل ===");
    }

    // 4. إنشاء مجموعة الإعدادات (settings) إذا لم تكن موجودة
    if (!existingCollectionNames.includes('settings')) {
      await db.createCollection('settings');
      console.log("\n=== تم إنشاء مجموعة الإعدادات (settings) ===");
      
      // إضافة بيانات الإعدادات الأولية
      const result = await db.collection('settings').insertOne(settingsData);
      console.log(`- تم إضافة إعدادات الموقع إلى المجموعة`);
    } else {
      console.log("\n=== مجموعة الإعدادات (settings) موجودة بالفعل ===");
    }

    // 5. إنشاء مجموعة التقييمات (reviews) إذا لم تكن موجودة
    if (!existingCollectionNames.includes('reviews')) {
      await db.createCollection('reviews');
      console.log("\n=== تم إنشاء مجموعة التقييمات (reviews) ===");
      
      // الحصول على معرفات الحرفيين والمستخدمين لربطها بالتقييمات
      const craftsmen = await db.collection('craftsmen').find({}).limit(1).toArray();
      const users = await db.collection('users').find({}).limit(1).toArray();
      const bookings = await db.collection('bookings').find({}).limit(1).toArray();
      
      // تحديث معرفات الحرفيين والمستخدمين في بيانات التقييمات
      if (craftsmen.length > 0 && users.length > 0) {
        reviewsData[0].craftsmanId = craftsmen[0]._id;
        reviewsData[0].clientId = users[0]._id;
        
        if (bookings.length > 0) {
          reviewsData[0].bookingId = bookings[0]._id;
        }
        
        // إضافة بيانات التقييمات الأولية
        const result = await db.collection('reviews').insertMany(reviewsData);
        console.log(`- تم إضافة ${result.insertedCount} تقييم إلى المجموعة`);
      }
    } else {
      console.log("\n=== مجموعة التقييمات (reviews) موجودة بالفعل ===");
    }

    // 6. إنشاء مجموعة الطلبات (requests) إذا لم تكن موجودة
    if (!existingCollectionNames.includes('requests')) {
      await db.createCollection('requests');
      console.log("\n=== تم إنشاء مجموعة الطلبات (requests) ===");
      
      // الحصول على معرفات المستخدمين والمهن لربطها بالطلبات
      const users = await db.collection('users').find({}).limit(1).toArray();
      const professions = await db.collection('professions').find({}).limit(1).toArray();
      
      // تحديث معرفات المستخدمين والمهن في بيانات الطلبات
      if (users.length > 0 && professions.length > 0) {
        requestsData[0].clientId = users[0]._id;
        requestsData[0].professionId = professions[0]._id;
        
        // إضافة بيانات الطلبات الأولية
        const result = await db.collection('requests').insertMany(requestsData);
        console.log(`- تم إضافة ${result.insertedCount} طلب إلى المجموعة`);
      }
    } else {
      console.log("\n=== مجموعة الطلبات (requests) موجودة بالفعل ===");
    }

    // 7. عرض المجموعات النهائية بعد الإنشاء
    console.log("\n=== المجموعات النهائية في قاعدة البيانات ===");
    const finalCollections = await db.listCollections().toArray();
    finalCollections.forEach(collection => {
      console.log(`- ${collection.name}`);
    });

  } catch (error) {
    console.error("حدث خطأ أثناء تنفيذ العمليات:", error);
  } finally {
    // إغلاق الاتصال بقاعدة البيانات
    await client.close();
    console.log("\nتم إغلاق الاتصال بقاعدة البيانات");
  }
}

// تنفيذ الدالة الرئيسية
run().catch(console.error);
