import React, { useEffect, useRef } from "react";
import { Briefcase, Star } from "lucide-react";
import useThemeStore from "../../store/themeStore";

const MapFilter = ({
  professions = [],
  selectedProfessions = [],
  onProfessionChange,
  selectedRating = 0,
  onRatingChange,
  searchRadius = 5,
  onRadiusChange,
}) => {
  const darkMode = useThemeStore((state) => state.darkMode);
  const containerRef = useRef(null);

  // التبديل بين تحديد وإلغاء تحديد المهنة
  const toggleProfession = (profession) => {
    if (selectedProfessions.includes(profession)) {
      onProfessionChange(selectedProfessions.filter((p) => p !== profession));
    } else {
      onProfessionChange([...selectedProfessions, profession]);
    }
  };

  // تأكد من عدم وجود مهنة محددة افتراضيًا
  useEffect(() => {
    // إذا كان هناك مهن محددة، تأكد من أنها موجودة في قائمة المهن
    if (selectedProfessions.length > 0) {
      // إعادة تعيين المهن المحددة إلى مصفوفة فارغة عند تحميل المكون
      onProfessionChange([]);
    }
  }, []);

  // إعادة تهيئة الخريطة عند التحميل
  useEffect(() => {
    // محاكاة تغيير حجم النافذة لإصلاح مشكلة تهيئة الخريطة
    const simulateResize = () => {
      console.log("محاكاة تغيير حجم النافذة لإصلاح مشكلة تهيئة الخريطة");

      // تأخير قصير للتأكد من تحميل الخريطة
      setTimeout(() => {
        window.dispatchEvent(new Event("resize"));
      }, 100);

      // تأخير آخر للتأكد من تطبيق التغييرات
      setTimeout(() => {
        window.dispatchEvent(new Event("resize"));
      }, 500);
    };

    // تنفيذ محاكاة تغيير الحجم عند التحميل
    simulateResize();

    // إنشاء مراقب تغيير الحجم للحاوية
    if (window.ResizeObserver) {
      const resizeObserver = new ResizeObserver(() => {
        // إطلاق حدث تغيير الحجم عند تغيير حجم الحاوية
        window.dispatchEvent(new Event("resize"));
      });

      // بدء مراقبة الحاوية إذا كانت موجودة
      if (containerRef.current) {
        resizeObserver.observe(containerRef.current);
      }

      return () => {
        if (containerRef.current) {
          resizeObserver.unobserve(containerRef.current);
        }
      };
    }
  }, []);

  // تحديد تقييم معين
  const handleRatingSelect = (rating) => {
    onRatingChange(rating === selectedRating ? 0 : rating);
  };

  return (
    <div
      ref={containerRef}
      className={`map-filter-container hidden md:block ${
        darkMode ? "bg-gray-800 text-gray-200" : "bg-white text-gray-800"
      } shadow-md`}
    >
      {/* عنوان الفلتر - مخفي على الشاشات الصغيرة */}
      <div className="hidden md:flex justify-between items-center mb-4">
        <h3 className="text-lg font-bold">فلترة النتائج</h3>
      </div>

      {/* فلتر المهن - مخفي على الشاشات الصغيرة */}
      <div className="map-filter-group hidden md:block">
        <div className="map-filter-title flex items-center">
          <Briefcase size={16} className="ml-1" />
          المهنة
        </div>

        <div className="map-filter-options">
          {professions.map((profession) => (
            <div
              key={profession}
              onClick={() => toggleProfession(profession)}
              className={`map-filter-option ${
                selectedProfessions.includes(profession) ? "active" : ""
              } ${
                darkMode
                  ? "bg-gray-700 hover:bg-gray-600"
                  : "bg-gray-100 hover:bg-gray-200"
              }`}
            >
              {profession}
            </div>
          ))}
        </div>
      </div>

      {/* فلتر التقييم - مخفي على الشاشات الصغيرة */}
      <div className="map-filter-group hidden md:block">
        <div className="map-filter-title flex items-center">
          <Star size={16} className="ml-1" />
          التقييم
        </div>

        <div className="map-filter-options">
          {[1, 2, 3, 4, 5].map((rating) => (
            <div
              key={rating}
              onClick={() => handleRatingSelect(rating)}
              className={`map-filter-option ${
                selectedRating >= rating && selectedRating > 0 ? "active" : ""
              } ${
                darkMode
                  ? "bg-gray-700 hover:bg-gray-600"
                  : "bg-gray-100 hover:bg-gray-200"
              }`}
            >
              {rating}+
            </div>
          ))}
        </div>
      </div>

      {/* تنويه للمستخدم - يظهر فقط على الشاشات الصغيرة */}
      <div
        className={`mt-4 p-3 rounded-lg text-sm md:hidden ${
          darkMode
            ? "bg-blue-900/30 text-blue-200 border border-blue-800/50"
            : "bg-blue-50 text-blue-700 border border-blue-200"
        }`}
      >
        <div className="flex items-start gap-2 ">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="w-5 h-5 mt-0.5 flex-shrink-0"
          >
            <circle cx="12" cy="12" r="10"></circle>
            <line x1="12" y1="8" x2="12" y2="12"></line>
            <line x1="12" y1="16" x2="12.01" y2="16"></line>
          </svg>
          <div>
            <p className="font-medium">
              مرر للأسفل لمشاهدة خريطة النتائج والحرفيين المتاحين
            </p>
            <div className="flex justify-center mt-2 animate-bounce">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="w-5 h-5"
              >
                <line x1="12" y1="5" x2="12" y2="19"></line>
                <polyline points="19 12 12 19 5 12"></polyline>
              </svg>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MapFilter;
