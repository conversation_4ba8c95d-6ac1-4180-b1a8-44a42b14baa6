import React from "react";
import { Send } from "lucide-react";

/**
 * مكون إدخال الرسائل
 * @param {Object} props - خصائص المكون
 * @param {string} props.value - قيمة حقل الإدخال
 * @param {Function} props.onChange - دالة تنفذ عند تغيير قيمة الإدخال
 * @param {Function} props.onSubmit - دالة تنفذ عند إرسال الرسالة
 * @param {string} props.placeholder - نص توضيحي لحقل الإدخال
 * @param {boolean} props.darkMode - وضع الألوان الداكنة
 * @param {string} props.dir - اتجاه النص (rtl/ltr)
 * @param {boolean} props.disabled - هل الحقل معطل
 * @param {React.RefObject} props.inputRef - مرجع لعنصر الإدخال
 */
const ChatInput = ({
  value,
  onChange,
  onSubmit,
  placeholder,
  darkMode,
  dir,
  disabled,
  inputRef,
}) => {
  return (
    <div
      className={`p-3 ${
        darkMode ? "bg-gray-900" : "bg-white"
      } border-t ${darkMode ? "border-gray-700" : "border-gray-200"}`}
    >
      <form
        onSubmit={(e) => {
          e.preventDefault();
          onSubmit();
        }}
        className="flex items-center"
      >
        <input
          ref={inputRef}
          type="text"
          value={value}
          onChange={(e) => onChange(e.target.value)}
          placeholder={placeholder}
          className={`flex-grow p-2 rounded-l-md ${
            darkMode
              ? "bg-gray-800 text-white border-gray-700"
              : "bg-gray-100 text-gray-900 border-gray-300"
          } border focus:outline-none focus:ring-2 focus:ring-indigo-500 ${
            disabled ? "opacity-70" : ""
          }`}
          dir={dir}
          disabled={disabled}
        />

        <button
          type="submit"
          className={`p-2 rounded-r-md mr-2 ${
            darkMode
              ? "bg-indigo-600 hover:bg-indigo-700"
              : "bg-indigo-500 hover:bg-indigo-600"
          } text-white transition-colors ${
            disabled ? "opacity-50 cursor-not-allowed" : ""
          }`}
          disabled={disabled}
        >
          <Send size={20} />
        </button>
      </form>
    </div>
  );
};

export default ChatInput;
