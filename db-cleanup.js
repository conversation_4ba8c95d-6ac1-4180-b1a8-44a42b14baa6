// سكريبت لتنظيف وتوحيد المجموعات المكررة في قاعدة بيانات MongoDB

const { MongoClient } = require('mongodb');

// رابط الاتصال بقاعدة البيانات
const uri = "mongodb+srv://jobscope_user:<EMAIL>/jobscope?retryWrites=true&w=majority";
const dbName = "jobscope";

// خيارات الاتصال
const options = {
  useNewUrlParser: true,
  useUnifiedTopology: true,
  serverSelectionTimeoutMS: 30000, // زيادة مهلة اختيار الخادم
  socketTimeoutMS: 45000, // زيادة مهلة الاتصال
};

// إنشاء عميل MongoDB
const client = new MongoClient(uri, options);

async function run() {
  try {
    // الاتصال بقاعدة البيانات
    await client.connect();
    console.log("تم الاتصال بقاعدة البيانات بنجاح");

    // الوصول إلى قاعدة البيانات
    const db = client.db(dbName);

    // 1. عرض جميع المجموعات الموجودة
    console.log("\n=== المجموعات الموجودة في قاعدة البيانات ===");
    const collections = await db.listCollections().toArray();
    collections.forEach(collection => {
      console.log(`- ${collection.name}`);
    });

    // 2. التحقق من عدد الوثائق في كل مجموعة
    console.log("\n=== عدد الوثائق في كل مجموعة ===");
    for (const collection of collections) {
      const count = await db.collection(collection.name).countDocuments();
      console.log(`- ${collection.name}: ${count} وثيقة`);
    }

    // 3. نقل البيانات من craftsman إلى craftsmen (إذا كانت موجودة)
    if (collections.some(c => c.name === 'craftsman') && collections.some(c => c.name === 'craftsmen')) {
      console.log("\n=== نقل البيانات من craftsman إلى craftsmen ===");

      // التحقق من عدد الوثائق قبل النقل
      const craftsmanCount = await db.collection('craftsman').countDocuments();
      const craftsmenCount = await db.collection('craftsmen').countDocuments();
      console.log(`- عدد الوثائق في craftsman قبل النقل: ${craftsmanCount}`);
      console.log(`- عدد الوثائق في craftsmen قبل النقل: ${craftsmenCount}`);

      if (craftsmanCount > 0) {
        // الحصول على جميع الوثائق من مجموعة craftsman
        const craftsmanDocs = await db.collection('craftsman').find({}).toArray();

        // إضافة كل وثيقة إلى مجموعة craftsmen
        for (const doc of craftsmanDocs) {
          // التحقق مما إذا كانت الوثيقة موجودة بالفعل في craftsmen
          const existingDoc = await db.collection('craftsmen').findOne({ _id: doc._id });

          if (!existingDoc) {
            await db.collection('craftsmen').insertOne(doc);
            console.log(`- تم نقل الوثيقة بمعرف ${doc._id} إلى craftsmen`);
          } else {
            console.log(`- الوثيقة بمعرف ${doc._id} موجودة بالفعل في craftsmen`);
          }
        }

        // التحقق من عدد الوثائق بعد النقل
        const newCraftsmenCount = await db.collection('craftsmen').countDocuments();
        console.log(`- عدد الوثائق في craftsmen بعد النقل: ${newCraftsmenCount}`);
      } else {
        console.log("- لا توجد وثائق في craftsman للنقل");
      }
    } else {
      console.log("\n=== لا توجد مجموعة craftsman أو craftsmen ===");
    }

    // 4. نقل البيانات من professions المكررة (إذا كانت موجودة)
    const professionsCollections = collections.filter(c => c.name === 'professions');
    if (professionsCollections.length > 1) {
      console.log("\n=== توحيد مجموعات professions المكررة ===");
      // هذا الجزء معقد لأننا نحتاج إلى تحديد أي المجموعات هي الأصلية
      // سنحتاج إلى مزيد من المعلومات لتنفيذ هذا الجزء
    }

    // 5. توحيد مجموعات notifications (إذا كانت موجودة)
    const notificationsCollections = collections.filter(c => c.name === 'notifications');
    if (notificationsCollections.length > 1) {
      console.log("\n=== توحيد مجموعات notifications المكررة ===");
      // هذا الجزء معقد لأننا نحتاج إلى تحديد أي المجموعات هي الأصلية
      // سنحتاج إلى مزيد من المعلومات لتنفيذ هذا الجزء
    }

    // 6. حذف المجموعات الفارغة (إذا كان ممكنًا)
    console.log("\n=== محاولة حذف المجموعات الفارغة ===");
    for (const collection of collections) {
      const count = await db.collection(collection.name).countDocuments();
      if (count === 0) {
        try {
          // محاولة حذف المجموعة الفارغة
          await db.collection(collection.name).drop();
          console.log(`- تم حذف المجموعة الفارغة: ${collection.name}`);
        } catch (error) {
          console.log(`- لا يمكن حذف المجموعة: ${collection.name} - ${error.message}`);
        }
      }
    }

    // 7. تحديث الكود لاستخدام أسماء المجموعات الصحيحة
    console.log("\n=== توصيات لتحديث الكود ===");
    console.log("- تأكد من أن جميع استدعاءات API تستخدم 'craftsmen' بدلاً من 'craftsman'");
    console.log("- تأكد من أن جميع استدعاءات API تستخدم 'notifications' بشكل صحيح");

    // 8. عرض المجموعات النهائية بعد التنظيف
    console.log("\n=== المجموعات النهائية بعد التنظيف ===");
    const finalCollections = await db.listCollections().toArray();
    finalCollections.forEach(collection => {
      console.log(`- ${collection.name}`);
    });

  } catch (error) {
    console.error("حدث خطأ أثناء تنفيذ العمليات:", error);
  } finally {
    // إغلاق الاتصال بقاعدة البيانات
    await client.close();
    console.log("\nتم إغلاق الاتصال بقاعدة البيانات");
  }
}

// تنفيذ الدالة الرئيسية
run().catch(console.error);
