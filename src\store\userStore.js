﻿import { create } from "zustand";
import { authService, userService } from "../services/api";

let isCheckingAuth = false;
// متغير لمنع التحقق من المصادقة أثناء تسجيل الدخول
let isLoggingIn = false;

// تنظيف localStorage عند بدء التطبيق
const cleanupLocalStorage = () => {
  try {
    const importantKeys = [
      "token",
      "user",
      "userType",
      "userId",
      "tokenExpiry",
      "rememberMe",
      "savedCredentials",
      "adminToken",
      "adminUser",
      "theme",
      "language",
      "darkMode",
    ];

    for (let i = localStorage.length - 1; i >= 0; i--) {
      const key = localStorage.key(i);
      if (key && !importantKeys.includes(key)) {
        localStorage.removeItem(key);
      }
    }

    console.log("localStorage cleaned up");
  } catch (error) {
    console.warn("localStorage cleanup failed:", error);
  }
};

// تنظيف localStorage عند تحميل التطبيق
if (typeof window !== "undefined") {
  cleanupLocalStorage();

  // مراقبة حجم localStorage
  try {
    const used = JSON.stringify(localStorage).length;
    const quota = 5 * 1024 * 1024; // 5MB تقريباً
    console.log(
      `localStorage usage: ${(used / 1024).toFixed(2)}KB / ${(
        quota /
        1024 /
        1024
      ).toFixed(2)}MB`
    );

    if (used > quota * 0.8) {
      console.warn("localStorage is nearly full, cleaning up...");
      cleanupLocalStorage();
    }
  } catch (error) {
    console.warn("Could not check localStorage usage:", error);
  }
}

const useUserStore = create((set, get) => {
  let initialUser = null;
  let initialUserType = null;
  let initialIsAuthenticated = false;
  let storedRememberMe = false;
  let storedCredentials = null;

  if (typeof window !== "undefined") {
    const token = localStorage.getItem("token");
    const userStr = localStorage.getItem("user");

    if (token && userStr) {
      try {
        initialUser = JSON.parse(userStr);
        initialUserType =
          initialUser.userType || localStorage.getItem("userType");
        initialIsAuthenticated = true;

        storedRememberMe = localStorage.getItem("rememberMe") === "true";

        const savedCredentialsStr = localStorage.getItem("savedCredentials");
        if (savedCredentialsStr) {
          storedCredentials = JSON.parse(savedCredentialsStr);
        }
      } catch (error) {
        console.error("Error retrieving user data from localStorage:", error);
      }
    }
  }

  return {
    user: initialUser,
    isAuthenticated: initialIsAuthenticated,
    userType: initialUserType,
    rememberMe: storedRememberMe,
    savedCredentials: storedCredentials,
    loading: false,
    error: null,

    checkAuth: async () => {
      if (isCheckingAuth) {
        console.log("Auth check already in progress, ignoring request");
        return false;
      }

      if (isLoggingIn) {
        console.log("Login in progress, skipping auth check");
        return false;
      }

      isCheckingAuth = true;
      console.log("Starting auth check...");

      try {
        const currentPath = window.location.pathname;
        if (currentPath.startsWith("/admin")) {
          console.log("Ignoring auth check for admin path");
          return false;
        }

        const token = localStorage.getItem("token");
        const userData = localStorage.getItem("user");
        const userType = localStorage.getItem("userType");
        const userId = localStorage.getItem("userId");
        const tokenExpiry = localStorage.getItem("tokenExpiry");

        console.log("Local auth data:", {
          hasToken: !!token,
          hasUserData: !!userData,
          userType,
          userId,
          tokenExpiry,
        });

        // التحقق من انتهاء صلاحية التوكن
        if (tokenExpiry) {
          const expiryDate = new Date(tokenExpiry);
          const now = new Date();
          if (now > expiryDate) {
            console.log("Token expired, removing...");
            localStorage.removeItem("token");
            localStorage.removeItem("user");
            localStorage.removeItem("userType");
            localStorage.removeItem("userId");
            localStorage.removeItem("tokenExpiry");

            set({
              user: null,
              isAuthenticated: false,
              userType: null,
              userId: null,
              loading: false,
            });
            return false;
          }
        }

        if (
          !token ||
          token === "null" ||
          token === "undefined" ||
          !token.trim()
        ) {
          console.log("No valid auth token found");
          set({
            user: null,
            isAuthenticated: false,
            userType: null,
            userId: null,
            loading: false,
          });
          return false;
        }

        if (userData && userType && userId) {
          try {
            const parsedUserData = JSON.parse(userData);
            console.log("Found local user data:", parsedUserData);
            set({
              user: parsedUserData,
              isAuthenticated: true,
              userType: userType,
              userId: userId,
              loading: false,
            });

            // تجاهل التحقق من الخادم إذا كانت البيانات المحلية موجودة
            console.log("Using local user data, skipping server verification");

            return true;
          } catch (parseError) {
            console.error("Error parsing user data:", parseError);
            localStorage.removeItem("user");
            localStorage.removeItem("userType");
            localStorage.removeItem("userId");
          }
        }

        // إذا لم تكن هناك بيانات محلية، لا نحاول التحقق من الخادم
        console.log("No local user data found, setting as unauthenticated");
        set({
          user: null,
          isAuthenticated: false,
          userType: null,
          userId: null,
          loading: false,
        });
        return false;
      } catch (outerError) {
        console.error("General auth check error:", outerError);
        set({
          user: null,
          isAuthenticated: false,
          userType: null,
          userId: null,
          loading: false,
          error: outerError.message || "Auth check failed",
        });
        return false;
      } finally {
        isCheckingAuth = false;
      }
    },

    login: async (userData, type, rememberMe = false, credentials = null) => {
      isLoggingIn = true; // منع checkAuth أثناء تسجيل الدخول
      set({ loading: true, error: null });

      try {
        console.log("Simple login:", { name: userData.name, type, rememberMe });

        if (userData.token) {
          try {
            // تنظيف localStorage أولاً لتوفير مساحة
            try {
              // حذف البيانات القديمة غير المهمة
              for (let i = localStorage.length - 1; i >= 0; i--) {
                const key = localStorage.key(i);
                if (
                  key &&
                  ![
                    "token",
                    "user",
                    "userType",
                    "userId",
                    "tokenExpiry",
                    "rememberMe",
                    "savedCredentials",
                    "adminToken",
                    "adminUser",
                    "theme",
                    "language",
                  ].includes(key)
                ) {
                  localStorage.removeItem(key);
                }
              }
            } catch (cleanupError) {
              console.warn("Cleanup error:", cleanupError);
            }

            localStorage.setItem("token", userData.token);
            if (rememberMe) {
              const expiryDate = new Date();
              expiryDate.setDate(expiryDate.getDate() + 30);
              localStorage.setItem("tokenExpiry", expiryDate.toISOString());
            }
            console.log("Token saved successfully");
          } catch (tokenError) {
            console.error("Error saving token:", tokenError);
            // في حالة فشل حفظ التوكن، نحاول مسح localStorage تماماً
            try {
              localStorage.clear();
              localStorage.setItem("token", userData.token);
              console.log("Token saved after clearing localStorage");
            } catch (clearError) {
              console.error(
                "Critical error: Cannot save token even after clearing localStorage"
              );
            }
          }
        }

        const userObj = userData.user || userData;
        const userWithId = {
          ...userObj,
          id: userObj._id || userObj.id,
          userType: type,
        };

        const userId = userWithId.id;

        try {
          // تقليل حجم بيانات المستخدم المحفوظة
          const minimalUser = {
            id: userWithId.id,
            name: userWithId.name,
            email: userWithId.email,
            phone: userWithId.phone,
            userType: userWithId.userType,
            profilePicture: userWithId.profilePicture,
            // حفظ البيانات الأساسية فقط للحرفيين
            ...(userWithId.userType === "craftsman" && {
              profession: userWithId.profession,
              professions: userWithId.professions,
              specialization: userWithId.specialization,
              specializations: userWithId.specializations,
              location: userWithId.location,
              available: userWithId.available,
              workingHours: userWithId.workingHours,
              workingHoursArray: userWithId.workingHoursArray,
              features: userWithId.features,
              bio: userWithId.bio,
              workRadius: userWithId.workRadius,
            }),
          };

          localStorage.setItem("user", JSON.stringify(minimalUser));
          localStorage.setItem("userType", type);
          localStorage.setItem("userId", userId);
          localStorage.setItem("rememberMe", rememberMe.toString());
          console.log("Data saved to localStorage successfully");
        } catch (storageError) {
          console.error("Error saving data to localStorage:", storageError);
          console.warn("Login will proceed without local storage");

          // محاولة أخيرة بحفظ البيانات الأساسية فقط
          try {
            const essentialUser = {
              id: userWithId.id,
              name: userWithId.name,
              userType: userWithId.userType,
            };
            localStorage.setItem("user", JSON.stringify(essentialUser));
            localStorage.setItem("userType", type);
            localStorage.setItem("userId", userId);
            console.log("Essential data saved to localStorage");
          } catch (essentialError) {
            console.error(
              "Failed to save even essential data:",
              essentialError
            );
          }
        }

        if (rememberMe && credentials) {
          localStorage.setItem("savedCredentials", JSON.stringify(credentials));
        } else {
          localStorage.removeItem("savedCredentials");
        }

        set({
          user: userWithId,
          isAuthenticated: true,
          userType: type,
          userId: userId,
          rememberMe,
          savedCredentials: rememberMe ? credentials : null,
          loading: false,
        });

        console.log("Login successful:", { userId, userType: type });

        // التحقق من أن التوكن محفوظ بشكل صحيح
        const savedToken = localStorage.getItem("token");
        if (!savedToken && userData.token) {
          console.warn("Token not saved properly, attempting to save again");
          try {
            localStorage.setItem("token", userData.token);
          } catch (retryError) {
            console.error("Failed to save token on retry:", retryError);
          }
        }

        return true;
      } catch (error) {
        console.error("Login error:", error);
        set({
          loading: false,
          error: error.message || "Login failed",
        });
        return false;
      } finally {
        isLoggingIn = false; // السماح بـ checkAuth مرة أخرى
      }
    },

    logout: async () => {
      set({ loading: true });

      try {
        const rememberMe = get().rememberMe;
        const savedCredentials = get().savedCredentials;

        localStorage.removeItem("token");
        localStorage.removeItem("user");
        localStorage.removeItem("userId");
        localStorage.removeItem("userType");
        localStorage.removeItem("tokenExpiry");

        if (!rememberMe) {
          localStorage.removeItem("savedCredentials");
          localStorage.removeItem("rememberMe");
        }

        console.log("Logout successful");

        set({
          user: null,
          isAuthenticated: false,
          userType: null,
          userId: null,
          savedCredentials: rememberMe ? savedCredentials : null,
          rememberMe: rememberMe,
          loading: false,
        });

        return true;
      } catch (error) {
        console.error("Logout error:", error);
        set({ loading: false });
        return false;
      }
    },

    updateUser: async (userData) => {
      set({ loading: true, error: null });

      try {
        let updatedUserData = userData;

        if (!userData.skipApiCall) {
          try {
            updatedUserData = await userService.updateProfile(userData);
            console.log("User data updated on server:", updatedUserData);
          } catch (apiError) {
            console.error("Error updating user data on server:", apiError);
          }
        }

        const currentUser = get().user || {};
        const updatedUser = {
          ...currentUser,
          ...updatedUserData,
          id: updatedUserData._id || updatedUserData.id || currentUser.id,
        };

        const userId = updatedUser.id;

        try {
          // حفظ البيانات الكاملة للحرفيين
          const dataToSave =
            updatedUser.userType === "craftsman"
              ? {
                  ...updatedUser,
                  // التأكد من حفظ جميع البيانات المهمة
                  professions: updatedUser.professions || [],
                  specializations: updatedUser.specializations || [],
                  workingHours: updatedUser.workingHours || {},
                  workingHoursArray: updatedUser.workingHoursArray || [],
                  features: updatedUser.features || [],
                  bio: updatedUser.bio || "",
                  workRadius: updatedUser.workRadius || 5,
                  location: updatedUser.location || {},
                }
              : updatedUser;

          localStorage.setItem("user", JSON.stringify(dataToSave));
          localStorage.setItem("userId", userId);
          console.log(
            "تم حفظ بيانات المستخدم المحدثة في localStorage:",
            dataToSave
          );
        } catch (storageError) {
          console.error("Error updating data in localStorage:", storageError);
        }

        const userType = updatedUser.userType || get().userType;
        if (userType) {
          localStorage.setItem("userType", userType);
        }

        set({
          user: updatedUser,
          userId: userId,
          userType: userType,
          isAuthenticated: true,
          loading: false,
        });

        return updatedUser;
      } catch (error) {
        console.error("Error updating user data:", error);
        set({
          loading: false,
          error: error.message || "Error updating user data",
        });
        return null;
      }
    },

    setRememberMe: (value) => {
      localStorage.setItem("rememberMe", value.toString());
      set({ rememberMe: value });
    },

    setSavedCredentials: (credentials) => {
      if (credentials) {
        localStorage.setItem("savedCredentials", JSON.stringify(credentials));
      } else {
        localStorage.removeItem("savedCredentials");
      }
      set({ savedCredentials: credentials });
    },

    clearSavedCredentials: () => {
      localStorage.removeItem("savedCredentials");
      set({ savedCredentials: null });
    },
  };
});

export default useUserStore;
