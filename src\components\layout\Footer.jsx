import React, { useEffect } from "react";
import { Link } from "react-router-dom";
import useThemeStore from "../../store/themeStore";
import useTranslation from "../../hooks/useTranslation";
import useSiteSettingsStore from "../../store/siteSettingsStore";

const Footer = () => {
  const darkMode = useThemeStore((state) => state.darkMode);
  const { t } = useTranslation();
  const { settings, fetchSettings } = useSiteSettingsStore();

  useEffect(() => {
    fetchSettings();
  }, [fetchSettings]);

  // إعادة حساب أجزاء الاسم عند تغيير الإعدادات
  useEffect(() => {
    // هذا سيؤدي إلى إعادة تصيير المكون عند تغيير settings
  }, [settings]);

  let part1 = "Job";
  let part2 = "Scope";

  if (settings?.siteName) {
    const nameParts = settings.siteName.split(" ");
    if (nameParts.length > 1) {
      part1 = nameParts[0];
      part2 = nameParts.slice(1).join(" ");
    } else if (nameParts[0]) {
      const singleWord = nameParts[0];
      const midPoint = Math.ceil(singleWord.length / 2);
      part1 = singleWord.substring(0, midPoint);
      part2 = singleWord.substring(midPoint);
    }
  }

  return (
    <footer
      className={`${
        darkMode
          ? "bg-gradient-to-b from-gray-900 to-gray-950"
          : "bg-gradient-to-b from-indigo-900 to-blue-950"
      } text-white py-12 mt-auto shadow-[0_-10px_15px_-3px_rgba(0,0,0,0.1)] dark:shadow-[0_-10px_15px_-3px_rgba(0,0,0,0.3)] relative z-[50] transition-colors duration-300 border-t ${
        darkMode ? "border-gray-800" : "border-indigo-800"
      }`}
    >
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div>
            <h3 className="text-2xl font-bold mb-4">
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-yellow-400 to-orange-500">
                {part1}
              </span>
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-indigo-400 to-blue-500">
                {part2}
              </span>
            </h3>
            <p className="text-indigo-200 mb-4 leading-relaxed">
              {settings?.description ||
                "منصة سورية متخصصة لربط طالبي الخدمات بالحرفيين المهرة الأقرب إليك"}
            </p>
            <div className="flex space-x-4 rtl:space-x-reverse mt-6">
              <a
                href="#"
                className="w-10 h-10 flex items-center justify-center rounded-full bg-indigo-800/50 text-indigo-300 hover:text-white hover:bg-indigo-700/50 transition-colors duration-300"
              >
                <svg
                  className="w-5 h-5"
                  fill="currentColor"
                  viewBox="0 0 24 24"
                  aria-hidden="true"
                >
                  <path
                    fillRule="evenodd"
                    d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z"
                    clipRule="evenodd"
                  ></path>
                </svg>
              </a>
              <a
                href="#"
                className="w-10 h-10 flex items-center justify-center rounded-full bg-indigo-800/50 text-indigo-300 hover:text-white hover:bg-indigo-700/50 transition-colors duration-300"
              >
                <svg
                  className="w-5 h-5"
                  fill="currentColor"
                  viewBox="0 0 24 24"
                  aria-hidden="true"
                >
                  <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84"></path>
                </svg>
              </a>
              <a
                href="#"
                className="w-10 h-10 flex items-center justify-center rounded-full bg-indigo-800/50 text-indigo-300 hover:text-white hover:bg-indigo-700/50 transition-colors duration-300"
              >
                <svg
                  className="w-5 h-5"
                  fill="currentColor"
                  viewBox="0 0 24 24"
                  aria-hidden="true"
                >
                  <path
                    fillRule="evenodd"
                    d="M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z"
                    clipRule="evenodd"
                  ></path>
                </svg>
              </a>
            </div>
          </div>

          <div>
            <h3 className="text-xl font-bold mb-6 relative inline-block">
              <span className="relative z-10">
                {t("quickLinks") || "روابط سريعة"}
              </span>
              <span
                className={`absolute bottom-0 left-0 right-0 h-1 ${
                  darkMode ? "bg-indigo-500" : "bg-indigo-300"
                } opacity-30 transform -rotate-1 z-0`}
              ></span>
            </h3>
            <ul className="space-y-3">
              <li className="w-fit">
                <Link
                  to="/"
                  className="text-indigo-200 hover:text-white transition-colors duration-200 flex items-center gap-2"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-4 w-4"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"
                    />
                  </svg>
                  {t("home") || "الرئيسية"}
                </Link>
              </li>
              <li className="w-fit">
                <Link
                  to="/search"
                  className="text-indigo-200 hover:text-white transition-colors duration-200 flex items-center gap-2"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-4 w-4"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                    />
                  </svg>
                  {t("searchForCraftsman") || "البحث عن حرفي"}
                </Link>
              </li>
              <li className="w-fit">
                <Link
                  to="/register/craftsman"
                  className="text-indigo-200 hover:text-white transition-colors duration-200 flex items-center gap-2"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-4 w-4"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                    />
                  </svg>
                  {t("registerAsCraftsman") || "تسجيل كحرفي"}
                </Link>
              </li>
              <li className="w-fit">
                <Link
                  to="/register/client"
                  className="text-indigo-200 hover:text-white transition-colors duration-200 flex items-center gap-2"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-4 w-4"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                    />
                  </svg>
                  تسجيل كطالب خدمة
                </Link>
              </li>
              <li className="w-fit">
                <Link
                  to="/login"
                  className="text-indigo-200 hover:text-white transition-colors duration-200 flex items-center gap-2"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-4 w-4"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"
                    />
                  </svg>
                  {t("login") || "تسجيل الدخول"}
                </Link>
              </li>
            </ul>
          </div>

          <div>
            <h3 className="text-xl font-bold mb-6 relative inline-block">
              <span className="relative z-10">
                {t("contact") || "تواصل معنا"}
              </span>
              <span
                className={`absolute bottom-0 left-0 right-0 h-1 ${
                  darkMode ? "bg-indigo-500" : "bg-indigo-300"
                } opacity-30 transform -rotate-1 z-0`}
              ></span>
            </h3>
            <div className="space-y-4">
              <div className="flex items-center gap-3 w-fit">
                <div className="bg-indigo-800/50 p-2 rounded-full">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5 text-indigo-300"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                    />
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
                    />
                  </svg>
                </div>
                <p className="text-indigo-200">
                  {settings?.siteAddress || "سوريا، دمشق"}
                </p>
              </div>

              <div className="flex items-center gap-3 w-fit">
                <div className="bg-indigo-800/50 p-2 rounded-full">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5 text-indigo-300"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                    />
                  </svg>
                </div>
                <a
                  href={`mailto:${settings?.contactEmail ||
                    "<EMAIL>"}`}
                  className="text-indigo-200 hover:text-white transition-colors duration-200"
                >
                  {settings?.contactEmail || "<EMAIL>"}
                </a>
              </div>

              <div className="flex items-center gap-3 w-fit">
                <div className="bg-indigo-800/50 p-2 rounded-full">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5 text-indigo-300"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
                    />
                  </svg>
                </div>
                <a
                  href={`tel:${settings?.contactPhone || "+96311123456"}`}
                  className="text-indigo-200 hover:text-white transition-colors duration-200"
                >
                  {settings?.contactPhone || "+963 11 123 4567"}
                </a>
              </div>
            </div>
          </div>
        </div>

        <div className="border-t border-indigo-700/50 mt-12 pt-8 text-center">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-indigo-200 mb-4 md:mb-0">
              &copy; {new Date().getFullYear()}{" "}
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-yellow-400 to-orange-500">
                {part1}
              </span>
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-indigo-400 to-blue-500">
                {part2}
              </span>{" "}
              {t("copyright") || " جميع الحقوق محفوظة "}
            </p>

            <div className="flex items-center gap-4">
              <Link
                to="/how-to-use"
                className="text-sm text-indigo-300 hover:text-white transition-colors duration-300 px-2 py-1 rounded-md hover:bg-indigo-800/30 w-fit flex items-center gap-2"
              >
                دليل استخدام الموقع
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
