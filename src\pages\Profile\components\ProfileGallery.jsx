import React, { useState } from "react";
import { motion } from "framer-motion";
import Card from "../../../components/common/Card";
import LazyImage from "../../../components/common/LazyImage";
import ConfirmationToast from "../../../components/common/ConfirmationToast";
import useThemeStore from "../../../store/themeStore";
import { Image, Plus, X, Camera, AlertCircle } from "lucide-react";

const ProfileGallery = ({
  gallery = [],
  isEditing,
  onAddImage,
  onRemoveImage,
}) => {
  const darkMode = useThemeStore((state) => state.darkMode);
  const fileInputRef = React.useRef(null);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadError, setUploadError] = useState(null);
  const [images, setImages] = useState(gallery);
  const [confirmDelete, setConfirmDelete] = useState(false);
  const [imageToDelete, setImageToDelete] = useState(null);

  // تعريف المراجع في بداية المكون
  const prevGalleryRef = React.useRef(JSON.stringify(gallery || []));
  const imagesRef = React.useRef(images);

  // تحديث الصور عند تغيير المعرض
  React.useEffect(() => {
    // تجنب التحديثات المتكررة
    const currentGallery = gallery || [];
    const newGalleryStr = JSON.stringify(currentGallery);

    // تحديث الصور فقط إذا كانت مختلفة عن القيمة المخزنة
    if (prevGalleryRef.current !== newGalleryStr) {
      setImages(currentGallery);
      prevGalleryRef.current = newGalleryStr;
    }
  }, [gallery]);

  // تحديث مرجع الصور عندما تتغير الصور
  React.useEffect(() => {
    imagesRef.current = images;
  }, [images]);

  // الاستماع إلى حدث تحميل الصورة
  React.useEffect(() => {
    const handleImageLoaded = (event) => {
      // التحقق مما إذا كانت الصورة المحملة هي من معرض الأعمال
      const loadedSrc = event.detail.src;
      // استخدام المرجع للحصول على أحدث قيمة للصور
      const currentImages = imagesRef.current;

      const isGalleryImage = currentImages.some(
        (img) =>
          loadedSrc.includes(img) ||
          (typeof img === "string" && loadedSrc.includes(img.split("/").pop()))
      );

      if (isGalleryImage) {
        // إيقاف حالة التحميل بعد تحميل الصورة
        setIsUploading(false);
      }
    };

    // إضافة المستمع للحدث
    window.addEventListener("imageLoaded", handleImageLoaded);

    // إزالة المستمع عند تفكيك المكون
    return () => {
      window.removeEventListener("imageLoaded", handleImageLoaded);
    };
  }, []); // إزالة التبعية على images

  const handleAddClick = () => {
    // السماح بإضافة الصور مباشرة بدون الحاجة لوضع التعديل
    fileInputRef.current.click();
  };

  const handleFileChange = (e) => {
    const files = e.target.files;
    if (files && files.length > 0 && onAddImage) {
      // إظهار حالة التحميل
      setIsUploading(true);
      setUploadError(null);

      // استخدام onAddImage مباشرة (سيتم التعامل مع الملفات في المكون الأب)
      try {
        // تمرير الملفات مباشرة إلى المكون الأب
        // نستخدم Promise لمعرفة متى ينتهي التحميل
        const uploadPromise = onAddImage(files);

        // إذا كانت الدالة تعيد Promise، ننتظر حتى تنتهي
        if (uploadPromise && typeof uploadPromise.then === "function") {
          uploadPromise
            .then(() => {
              // نتأكد من أن الصور ظهرت قبل إيقاف حالة التحميل
              // ننتظر لفترة إضافية للتأكد من تحديث واجهة المستخدم
              setTimeout(() => {
                setIsUploading(false);
              }, 1000);
            })
            .catch(() => {
              setUploadError(
                "حدث خطأ أثناء رفع الصور. يرجى المحاولة مرة أخرى."
              );
              setIsUploading(false);

              // إعادة تعيين حالة الخطأ بعد 5 ثوان
              setTimeout(() => {
                setUploadError(null);
              }, 5000);
            });
        } else {
          // إذا لم تكن الدالة تعيد Promise، ننتظر فترة أطول قبل إيقاف حالة التحميل
          // هذا يعطي وقتًا كافيًا للصور للظهور
          setTimeout(() => {
            setIsUploading(false);
          }, 5000);
        }
      } catch {
        setUploadError("حدث خطأ أثناء معالجة الصور. يرجى المحاولة مرة أخرى.");
        setIsUploading(false);

        // إعادة تعيين حالة الخطأ بعد 5 ثوان
        setTimeout(() => {
          setUploadError(null);
        }, 5000);
      }
    }
  };

  return (
    <Card
      className={`p-6 mb-6 rounded-xl shadow-lg ${
        darkMode
          ? "bg-gray-800 text-gray-200 border border-gray-700"
          : "bg-gradient-to-br from-blue-50/80 to-indigo-50 border border-indigo-100"
      } transition-colors duration-300 hover:shadow-xl`}
    >
      <div className="flex items-center mb-6">
        <div
          className={`p-2 rounded-full mr-3 ${
            darkMode ? "bg-indigo-900/50" : "bg-indigo-100"
          }`}
        >
          <Image
            size={24}
            className={`${
              darkMode ? "text-indigo-400" : "text-indigo-500"
            } transition-colors duration-300`}
          />
        </div>
        <h2
          className={`text-xl font-bold ${
            darkMode ? "text-indigo-300" : "text-indigo-800"
          } relative inline-block transition-colors duration-300`}
        >
          <span className="relative z-10">معرض الأعمال</span>
          <span
            className={`absolute bottom-0 left-0 right-0 h-2 ${
              darkMode ? "bg-indigo-600" : "bg-indigo-400"
            } opacity-40 transform -rotate-1 z-0 rounded-full`}
          ></span>
        </h2>

        {/* عنصر input موجود دائمًا بغض النظر عن وضع التعديل */}
        <input
          type="file"
          ref={fileInputRef}
          onChange={handleFileChange}
          accept="image/*"
          multiple
          className="hidden"
        />

        {/* عرض رسالة الخطأ دائمًا بغض النظر عن وضع التعديل */}
        <div className="mr-auto">
          <div className="flex items-center gap-2">
            {uploadError && (
              <div
                className={`flex items-center gap-1 py-1.5 px-3 rounded-lg text-xs font-medium ${
                  darkMode
                    ? "bg-red-900/50 text-red-300"
                    : "bg-red-100 text-red-700"
                }`}
              >
                <AlertCircle
                  size={14}
                  className={darkMode ? "text-red-400" : "text-red-600"}
                />
                <span>{uploadError}</span>
              </div>
            )}
          </div>
        </div>
      </div>

      {images.length > 0 ? (
        <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
          {images.map((image, index) => (
            <motion.div
              key={index}
              whileHover={{ scale: 1.03 }}
              className="relative group"
            >
              <div
                className={`h-48 rounded-lg overflow-hidden shadow-md border ${
                  darkMode ? "border-gray-700" : "border-indigo-200"
                } transition-all duration-300 group-hover:shadow-xl`}
              >
                <LazyImage
                  src={image}
                  alt={`عمل ${index + 1}`}
                  className="w-full h-full object-cover"
                  placeholderClassName="w-full h-full bg-gray-200 animate-pulse"
                />

                {/* تأثير التوهج عند التحويم */}
                <div className="absolute inset-0 bg-indigo-500/0 group-hover:bg-indigo-500/10 transition-colors duration-300"></div>
              </div>

              {/* زر حذف الصورة - يظهر عند تمرير المؤشر فوق الصورة دائمًا ويعمل مباشرة */}
              <button
                onClick={() => {
                  setImageToDelete({ index, image });
                  setConfirmDelete(true);
                }}
                className={`absolute top-2 left-2 text-white rounded-full w-8 h-8 flex items-center justify-center shadow-lg transition-all duration-300 transform scale-0 group-hover:scale-100 ${
                  darkMode
                    ? "bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800"
                    : "bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700"
                }`}
              >
                <X size={16} />
              </button>
            </motion.div>
          ))}

          {/* زر إضافة الصور في حالة وجود صور بالفعل - يظهر دائمًا */}
          <motion.div whileHover={{ scale: 1.03 }} className="relative">
            <button
              onClick={handleAddClick}
              disabled={isUploading}
              className={`h-48 w-full rounded-lg overflow-hidden shadow-md border flex flex-col items-center justify-center cursor-pointer transition-all duration-300 hover:shadow-xl ${
                darkMode
                  ? "bg-indigo-900/30 border-indigo-800/50 hover:bg-indigo-900/50"
                  : "bg-indigo-50/80 border-indigo-200 hover:bg-indigo-100/80"
              } ${isUploading ? "opacity-70 cursor-wait" : ""}`}
            >
              {isUploading ? (
                <>
                  <div className="relative mb-2">
                    <div className="animate-spin h-10 w-10 border-4 border-indigo-500 rounded-full border-t-transparent"></div>
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="h-6 w-6 rounded-full bg-indigo-100 flex items-center justify-center">
                        <div className="h-4 w-4 rounded-full bg-indigo-500 animate-pulse"></div>
                      </div>
                    </div>
                  </div>
                  <span
                    className={`font-bold ${
                      darkMode ? "text-indigo-300" : "text-indigo-700"
                    }`}
                  >
                    جاري الرفع...
                  </span>
                  <span
                    className={`text-xs mt-1 ${
                      darkMode ? "text-indigo-400" : "text-indigo-600"
                    }`}
                  >
                    يرجى الانتظار حتى تظهر الصورة
                  </span>
                </>
              ) : (
                <>
                  <div
                    className={`p-3 rounded-full mb-2 ${
                      darkMode ? "bg-indigo-800" : "bg-indigo-200"
                    }`}
                  >
                    <Plus
                      size={24}
                      className={
                        darkMode ? "text-indigo-300" : "text-indigo-600"
                      }
                    />
                  </div>
                  <span
                    className={`font-bold ${
                      darkMode ? "text-indigo-300" : "text-indigo-700"
                    }`}
                  >
                    إضافة صور
                  </span>
                </>
              )}
            </button>
          </motion.div>
        </div>
      ) : (
        <div
          className={`p-8 rounded-lg text-center ${
            darkMode
              ? "bg-gray-700/50 border border-gray-600/50"
              : "bg-white/80 border border-indigo-100"
          } transition-colors duration-300 shadow-inner`}
        >
          <div className="flex flex-col items-center justify-center">
            <div
              className={`p-4 rounded-full mb-3 ${
                darkMode ? "bg-indigo-900/30" : "bg-indigo-100/70"
              }`}
            >
              <Camera
                size={32}
                className={`${
                  darkMode ? "text-indigo-400" : "text-indigo-500"
                } transition-colors duration-300`}
              />
            </div>
            <p
              className={`${
                darkMode ? "text-gray-400" : "text-gray-500"
              } transition-colors duration-300 mb-2`}
            >
              لا توجد صور في المعرض
            </p>
            <p
              className={`text-xs ${
                darkMode ? "text-gray-500" : "text-gray-400"
              } mb-4`}
            >
              قم بإضافة صور لأعمالك السابقة لزيادة فرص الحصول على طلبات جديدة
            </p>

            {/* زر إضافة الصور في حالة عدم وجود صور - يظهر دائمًا */}
            <button
              onClick={handleAddClick}
              disabled={isUploading}
              className={`text-white flex items-center justify-center gap-2 shadow-lg hover:shadow-xl transition-all duration-300 relative overflow-hidden group py-2.5 px-5 rounded-lg ${
                darkMode
                  ? "bg-gradient-to-r from-indigo-600 via-indigo-700 to-purple-800 hover:from-indigo-700 hover:via-indigo-800 hover:to-purple-900"
                  : "bg-gradient-to-r from-blue-500 via-indigo-600 to-blue-600 hover:from-blue-600 hover:via-indigo-700 hover:to-blue-700"
              } ${isUploading ? "opacity-70 cursor-wait" : ""}`}
            >
              {isUploading ? (
                <>
                  <div className="relative">
                    <div className="animate-spin h-5 w-5 border-2 border-white rounded-full border-t-transparent"></div>
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="h-2 w-2 rounded-full bg-white animate-pulse"></div>
                    </div>
                  </div>
                  <span className="relative z-10 font-bold text-white mr-2">
                    جاري الرفع...
                  </span>
                </>
              ) : (
                <>
                  <Plus size={18} className="text-white" />
                  <span className="relative z-10 font-bold text-white">
                    إضافة صور
                  </span>
                  <span className="absolute inset-0 bg-white opacity-20 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700"></span>
                  <span className="absolute inset-0 opacity-0 group-hover:opacity-20 bg-gradient-to-r from-yellow-400 via-pink-500 to-indigo-500 transition-opacity duration-700"></span>
                </>
              )}
            </button>
          </div>
        </div>
      )}

      {images.length > 0 && (
        <div className="mt-4">
         
        </div>
      )}

      {/* ملاحظات حول معرض الصور - تظهر دائمًا */}
      <div
        className={`mt-4 p-3 rounded-lg ${
          darkMode
            ? "bg-indigo-900/30 border border-indigo-800/50"
            : "bg-indigo-50/80 border border-indigo-200"
        }`}
      >
        <div className="flex items-start">
          <div
            className={`p-1.5 rounded-full mt-0.5 ml-2 ${
              darkMode ? "bg-indigo-900/50" : "bg-indigo-100"
            }`}
          >
            <AlertCircle
              size={16}
              className={`${
                darkMode ? "text-indigo-400" : "text-indigo-500"
              } transition-colors duration-300`}
            />
          </div>
          <div>
            <p
              className={`text-xs mt-1 ${
                darkMode ? "text-gray-400" : "text-gray-500"
              }`}
            >
              يمكنك إضافة صور متعددة دفعة واحدة لعرض أعمالك السابقة (الحد الأقصى
              5 صور في المرة الواحدة)
            </p>
            {isEditing && <></>}
          </div>
        </div>
      </div>

      {/* توست تأكيد الحذف */}
      <ConfirmationToast
        isOpen={confirmDelete}
        onClose={() => setConfirmDelete(false)}
        onConfirm={() => {
          if (imageToDelete) {
            onRemoveImage(imageToDelete.index);
            setImageToDelete(null);
          }
        }}
        title="تأكيد حذف الصورة"
        message="هل أنت متأكد من حذف هذه الصورة؟ لا يمكن التراجع عن هذه العملية."
        confirmText="حذف الصورة"
        cancelText="إلغاء"
        type="warning"
      />
    </Card>
  );
};

export default ProfileGallery;
