import { create } from "zustand";
import adminService from "../services/adminService";

const useAdminStore = create((set) => ({
  admin: null,
  isAuthenticated: false,
  loading: false,
  error: null,

  // التحقق من حالة المصادقة عند تحميل التطبيق
  checkAuth: async () => {
    set({ loading: true, error: null });
    try {
      console.log("adminStore: بدء التحقق من مصادقة الأدمن");

      // التحقق من وجود رمز المصادقة المحلي
      const token = localStorage.getItem("adminToken");
      const adminData = localStorage.getItem("admin");

      if (!token) {
        console.log("adminStore: لا يوجد توكن أدمن");
        set({
          admin: null,
          isAuthenticated: false,
          loading: false,
        });
        return false;
      }

      // إذا كانت البيانات موجودة محلياً، استخدمها أولاً
      if (adminData) {
        try {
          const parsedAdminData = JSON.parse(adminData);
          console.log("adminStore: تم العثور على بيانات أدمن محلية");
          set({
            admin: parsedAdminData,
            isAuthenticated: true,
            loading: false,
          });

          // التحقق من صحة التوكن مع الخادم في الخلفية
          try {
            const response = await adminService.checkAdminAuth();
            console.log("adminStore: تم التحقق من صحة التوكن مع الخادم");

            // تحديث البيانات إذا كانت مختلفة
            if (JSON.stringify(response) !== JSON.stringify(parsedAdminData)) {
              localStorage.setItem("admin", JSON.stringify(response));
              set({ admin: response });
            }
          } catch (serverError) {
            console.error(
              "adminStore: فشل التحقق من الخادم، استخدام البيانات المحلية"
            );
            // في حالة فشل التحقق من الخادم، نستمر باستخدام البيانات المحلية
          }

          return true;
        } catch (parseError) {
          console.error("adminStore: خطأ في تحليل بيانات الأدمن:", parseError);
          // إذا فشل تحليل البيانات المحلية، نحذفها ونتحقق من الخادم
          localStorage.removeItem("admin");
        }
      }

      // التحقق من صحة التوكن مع الخادم
      console.log("adminStore: التحقق من صحة التوكن مع الخادم");
      const response = await adminService.checkAdminAuth();

      // حفظ البيانات المحدثة
      localStorage.setItem("admin", JSON.stringify(response));

      set({
        admin: response,
        isAuthenticated: true,
        loading: false,
      });

      console.log("adminStore: تم التحقق من مصادقة الأدمن بنجاح");
      return true;
    } catch (error) {
      console.error("adminStore: خطأ في التحقق من حالة مصادقة المسؤول:", error);

      // حذف البيانات في حالة حدوث خطأ
      localStorage.removeItem("adminToken");
      localStorage.removeItem("admin");
      localStorage.removeItem("adminTokenExpiry");

      set({
        admin: null,
        isAuthenticated: false,
        loading: false,
        error: error.message || "حدث خطأ أثناء التحقق من حالة مصادقة المسؤول",
      });

      return false;
    }
  },

  // تسجيل دخول الأدمن
  loginAdmin: async (credentials) => {
    set({ loading: true, error: null });
    try {
      console.log("adminStore: بدء تسجيل دخول الأدمن");

      // إرسال طلب للخادم للتحقق من بيانات الاعتماد
      const response = await adminService.adminLogin(credentials);

      if (response && response.token && response.admin) {
        console.log("adminStore: تم تسجيل دخول الأدمن بنجاح");

        // حفظ رمز المصادقة في localStorage
        localStorage.setItem("adminToken", response.token);

        // حفظ بيانات الأدمن في localStorage
        localStorage.setItem("admin", JSON.stringify(response.admin));

        // حفظ تاريخ انتهاء صلاحية التوكن إذا كان متوفراً
        if (response.expiresIn) {
          const expiryDate = new Date(Date.now() + response.expiresIn * 1000);
          localStorage.setItem("adminTokenExpiry", expiryDate.toISOString());
        }

        // إذا كان خيار "تذكرني" مفعل، نحفظ بيانات الاعتماد
        if (credentials.rememberMe) {
          localStorage.setItem("adminRememberMe", "true");
          localStorage.setItem(
            "adminCredentials",
            JSON.stringify({
              email: credentials.email,
              // لا نحفظ كلمة المرور لأسباب أمنية
            })
          );
        } else {
          // إذا لم يكن مفعل، نحذف بيانات "تذكرني" السابقة
          localStorage.removeItem("adminRememberMe");
          localStorage.removeItem("adminCredentials");
        }

        // تحديث حالة المسؤول
        set({
          admin: response.admin,
          isAuthenticated: true,
          loading: false,
          error: null,
        });

        console.log("adminStore: تم تحديث حالة الأدمن في المتجر");
        return true;
      } else {
        console.log("adminStore: فشل تسجيل دخول الأدمن - استجابة غير صالحة");
        set({
          loading: false,
          error: "اسم المستخدم أو كلمة المرور غير صحيحة",
        });
        return false;
      }
    } catch (error) {
      console.error("adminStore: خطأ في تسجيل دخول الأدمن:", error);

      let errorMessage = "حدث خطأ أثناء تسجيل الدخول";

      // تخصيص رسالة الخطأ بناءً على نوع الخطأ
      if (error.response?.status === 401) {
        errorMessage = "اسم المستخدم أو كلمة المرور غير صحيحة";
      } else if (error.response?.status === 403) {
        errorMessage = "ليس لديك صلاحية للوصول إلى لوحة التحكم";
      } else if (error.response?.status >= 500) {
        errorMessage = "خطأ في الخادم، يرجى المحاولة لاحقاً";
      } else if (error.message) {
        errorMessage = error.message;
      }

      set({
        loading: false,
        error: errorMessage,
      });
      return false;
    }
  },

  // تعيين بيانات الأدمن مباشرة
  setAdmin: (adminData) => {
    set({
      admin: adminData,
      isAuthenticated: true,
      loading: false,
      error: null,
    });
  },

  // تسجيل خروج الأدمن
  logoutAdmin: () => {
    console.log("adminStore: بدء تسجيل خروج الأدمن");

    // حذف بيانات الجلسة من التخزين المحلي
    localStorage.removeItem("adminToken");
    localStorage.removeItem("admin");
    localStorage.removeItem("adminTokenExpiry");
    sessionStorage.removeItem("admin-session");

    // نحتفظ ببيانات الاعتماد (adminCredentials) إذا كان المستخدم قد فعل "تذكرني"
    // يمكن للمستخدم مسحها يدوياً من صفحة تسجيل الدخول إذا أراد
    const rememberMe = localStorage.getItem("adminRememberMe");
    if (!rememberMe) {
      localStorage.removeItem("adminCredentials");
    }

    // إعادة تعيين حالة المسؤول
    set({
      admin: null,
      isAuthenticated: false,
      loading: false,
      error: null,
    });

    console.log("adminStore: تم تسجيل خروج الأدمن بنجاح");
  },

  // إعادة تعيين حالة الخطأ
  resetError: () => {
    set({ error: null });
  },

  // تحديث بيانات الأدمن
  updateAdminProfile: async (updatedData, imageFile = null) => {
    set({ loading: true, error: null });
    try {
      let response;

      // إذا كان هناك ملف صورة، استخدم الدالة المخصصة لرفع الصورة
      if (imageFile) {
        response = await adminService.updateAdminProfileWithImage(
          updatedData,
          imageFile
        );
      } else {
        // إذا لم تكن هناك صورة، استخدم الدالة العادية
        response = await adminService.updateAdminProfile(updatedData);
      }

      // تحديث حالة المسؤول
      set((state) => ({
        admin: {
          ...state.admin,
          ...response,
        },
        loading: false,
      }));

      // تحديث البيانات في localStorage
      const adminData = localStorage.getItem("admin");
      if (adminData) {
        const parsedAdminData = JSON.parse(adminData);
        const updatedAdminData = {
          ...parsedAdminData,
          ...response,
        };
        localStorage.setItem("admin", JSON.stringify(updatedAdminData));
      }

      return response;
    } catch (error) {
      console.error("خطأ في تحديث بيانات المسؤول:", error);
      set({
        loading: false,
        error: error.message || "حدث خطأ أثناء تحديث بيانات المسؤول",
      });
      throw error;
    }
  },

  // تحديث كلمة مرور الأدمن
  updateAdminPassword: async (passwordData) => {
    set({ loading: true, error: null });
    try {
      const response = await adminService.updateAdminPassword(passwordData);

      set({ loading: false });
      return response;
    } catch (error) {
      console.error("خطأ في تحديث كلمة مرور المسؤول:", error);
      set({
        loading: false,
        error: error.message || "حدث خطأ أثناء تحديث كلمة المرور",
      });
      throw error;
    }
  },
}));

// التحقق من حالة المصادقة عند تحميل الصفحة (معطل مؤقتاً)
// if (typeof window !== "undefined") {
//   useAdminStore.getState().checkAuth();
// }

export default useAdminStore;
