import React, { useState, useEffect } from "react";
import Button from "../../common/Button";
import useThemeStore from "../../../store/themeStore";
import { adminService } from "../../../services/api";
import {
  Users,
  UserPlus,
  Search,
  Filter,
  Edit,
  Trash2,
  Eye,
  MoreVertical,
} from "lucide-react";
import toast from "react-hot-toast";

const UsersSection = () => {
  const darkMode = useThemeStore((state) => state.darkMode);
  const [users, setUsers] = useState([]);
  const [stats, setStats] = useState({
    totalUsers: 0,
    totalClients: 0,
    totalCraftsmen: 0,
    newUsersThisMonth: 0,
    activeUsers: 0,
  });
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [filterType, setFilterType] = useState("all");
  const [selectedUser, setSelectedUser] = useState(null);
  const [showUserModal, setShowUserModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);

  // Pagination states
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // جلب البيانات عند تحميل المكون
  useEffect(() => {
    fetchUsers();
    fetchStats();
  }, []);

  // جلب قائمة المستخدمين
  const fetchUsers = async () => {
    try {
      setLoading(true);
      const usersData = await adminService.getAllUsers();
      setUsers(usersData);
    } catch (error) {
      console.error("خطأ في جلب المستخدمين:", error);
      toast.error("فشل في جلب قائمة المستخدمين");
    } finally {
      setLoading(false);
    }
  };

  // جلب الإحصائيات
  const fetchStats = async () => {
    try {
      const statsData = await adminService.getDashboardStats();
      setStats({
        totalUsers: statsData.totalUsers,
        totalClients: statsData.totalClients,
        totalCraftsmen: statsData.totalCraftsmen,
        newUsersThisMonth: statsData.newUsersThisMonth,
        activeUsers: statsData.activeUsers,
      });
    } catch (error) {
      console.error("خطأ في جلب الإحصائيات:", error);
    }
  };

  // تصفية المستخدمين
  const filteredUsers = users.filter((user) => {
    const matchesSearch =
      user.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email?.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesFilter = filterType === "all" || user.userType === filterType;
    return matchesSearch && matchesFilter;
  });

  // Pagination logic
  const totalItems = filteredUsers.length;
  const totalPages = Math.ceil(totalItems / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentUsers = filteredUsers.slice(startIndex, endIndex);

  // Reset to first page when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm, filterType]);

  // تحديث حالة المستخدم
  const toggleUserStatus = async (userId, currentStatus) => {
    try {
      await adminService.updateUser(userId, { isActive: !currentStatus });
      toast.success(
        `تم ${!currentStatus ? "تفعيل" : "إلغاء تفعيل"} المستخدم بنجاح`
      );
      fetchUsers(); // إعادة جلب البيانات
    } catch (error) {
      console.error("خطأ في تحديث حالة المستخدم:", error);
      toast.error("فشل في تحديث حالة المستخدم");
    }
  };

  // حذف مستخدم
  const deleteUser = async (userId, userName) => {
    if (window.confirm(`هل أنت متأكد من حذف المستخدم "${userName}"؟`)) {
      try {
        await adminService.deleteUser(userId);
        toast.success("تم حذف المستخدم بنجاح");
        fetchUsers(); // إعادة جلب البيانات
      } catch (error) {
        console.error("خطأ في حذف المستخدم:", error);
        toast.error("فشل في حذف المستخدم");
      }
    }
  };

  // عرض تفاصيل المستخدم
  const viewUser = (user) => {
    setSelectedUser(user);
    setShowUserModal(true);
  };

  // تعديل المستخدم
  const editUser = (user) => {
    setSelectedUser(user);
    setShowEditModal(true);
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h3
          className={`text-xl font-bold ${
            darkMode ? "text-indigo-300" : "text-indigo-800"
          } relative`}
        >
          <span className="relative z-10">إدارة المستخدمين</span>
          <span
            className={`absolute bottom-0 left-0 right-0 h-2 ${
              darkMode ? "bg-indigo-500" : "bg-indigo-300"
            } opacity-40 transform -rotate-1 z-0`}
          ></span>
        </h3>
        <Button
          variant="primary"
          className={`${
            darkMode
              ? "bg-gradient-to-r from-[#3730A3] to-[#4238C8] hover:from-[#322e92] hover:to-[#3b32b4]"
              : "bg-gradient-to-r from-[#4238C8] to-[#3730A3] hover:from-[#3b32b4] hover:to-[#322e92]"
          } text-white transition-all duration-200 shadow-md hover:shadow-lg relative overflow-hidden group flex items-center gap-2`}
        >
          <UserPlus size={18} />
          <span className="relative z-10">إضافة مستخدم جديد</span>
          <span className="absolute inset-0 bg-white opacity-20 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700"></span>
        </Button>
      </div>

      {/* إحصائيات المستخدمين */}
      <div
        className={`mb-6 p-4 rounded-lg ${
          darkMode
            ? "bg-gray-700"
            : "bg-gradient-to-r from-indigo-50 to-blue-50"
        } border ${darkMode ? "border-gray-600" : "border-indigo-100"}`}
      >
        <div className="flex justify-between items-center mb-4">
          <h4
            className={`font-bold ${
              darkMode ? "text-white" : "text-indigo-700"
            }`}
          >
            إحصائيات المستخدمين
          </h4>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
          <div
            className={`p-4 rounded-lg ${
              darkMode
                ? "bg-gray-800"
                : "bg-gradient-to-br from-white to-indigo-50/50"
            } border ${
              darkMode ? "border-gray-700" : "border-indigo-100"
            } shadow-sm`}
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-500 text-sm mb-1">إجمالي المستخدمين</p>
                <p
                  className={`text-2xl font-bold ${
                    darkMode ? "text-indigo-300" : "text-indigo-600"
                  }`}
                >
                  {loading ? "..." : stats.totalUsers}
                </p>
              </div>
              <Users
                className={`${
                  darkMode ? "text-indigo-300" : "text-indigo-600"
                }`}
                size={24}
              />
            </div>
          </div>
          <div
            className={`p-4 rounded-lg ${
              darkMode
                ? "bg-gray-800"
                : "bg-gradient-to-br from-white to-blue-50/50"
            } border ${
              darkMode ? "border-gray-700" : "border-blue-100"
            } shadow-sm`}
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-500 text-sm mb-1">طالبي الخدمة</p>
                <p
                  className={`text-2xl font-bold ${
                    darkMode ? "text-indigo-300" : "text-indigo-600"
                  }`}
                >
                  {loading ? "..." : stats.totalClients}
                </p>
              </div>
              <Users
                className={`${
                  darkMode ? "text-indigo-300" : "text-indigo-600"
                }`}
                size={24}
              />
            </div>
          </div>
          <div
            className={`p-4 rounded-lg ${
              darkMode
                ? "bg-gray-800"
                : "bg-gradient-to-br from-white to-orange-50/50"
            } border ${
              darkMode ? "border-gray-700" : "border-orange-100"
            } shadow-sm`}
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-500 text-sm mb-1">إجمالي الحرفيين</p>
                <p
                  className={`text-2xl font-bold ${
                    darkMode ? "text-orange-300" : "text-orange-600"
                  }`}
                >
                  {loading ? "..." : stats.totalCraftsmen}
                </p>
              </div>
              <Users
                className={`${
                  darkMode ? "text-orange-300" : "text-orange-600"
                }`}
                size={24}
              />
            </div>
          </div>
          <div
            className={`p-4 rounded-lg ${
              darkMode
                ? "bg-gray-800"
                : "bg-gradient-to-br from-white to-green-50/50"
            } border ${
              darkMode ? "border-gray-700" : "border-green-100"
            } shadow-sm`}
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-500 text-sm mb-1">مستخدمين جدد</p>
                <p
                  className={`text-2xl font-bold ${
                    darkMode ? "text-green-300" : "text-green-600"
                  }`}
                >
                  {loading ? "..." : stats.newUsersThisMonth}
                </p>
                <p className="text-xs text-gray-500">هذا الشهر</p>
              </div>
              <UserPlus
                className={`${darkMode ? "text-green-300" : "text-green-600"}`}
                size={24}
              />
            </div>
          </div>
          <div
            className={`p-4 rounded-lg ${
              darkMode
                ? "bg-gray-800"
                : "bg-gradient-to-br from-white to-purple-50/50"
            } border ${
              darkMode ? "border-gray-700" : "border-purple-100"
            } shadow-sm`}
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-500 text-sm mb-1">المستخدمين النشطين</p>
                <p
                  className={`text-2xl font-bold ${
                    darkMode ? "text-purple-300" : "text-purple-600"
                  }`}
                >
                  {loading ? "..." : stats.activeUsers}
                </p>
              </div>
              <Users
                className={`${
                  darkMode ? "text-purple-300" : "text-purple-600"
                }`}
                size={24}
              />
            </div>
          </div>
        </div>
      </div>

      {/* أدوات البحث والتصفية */}
      <div
        className={`mb-6 p-4 rounded-lg ${
          darkMode ? "bg-gray-700" : "bg-white"
        } border ${darkMode ? "border-gray-600" : "border-gray-200"} shadow-sm`}
      >
        <div className="flex flex-col md:flex-row gap-4">
          {/* شريط البحث */}
          <div className="flex-1 relative">
            <Search
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400"
              size={20}
            />
            <input
              type="text"
              placeholder="البحث بالاسم أو البريد الإلكتروني..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className={`w-full pr-10 pl-4 py-2 rounded-lg border ${
                darkMode
                  ? "bg-gray-800 border-gray-600 text-white placeholder-gray-400"
                  : "bg-gray-50 border-gray-300 text-gray-900 placeholder-gray-500"
              } focus:outline-none focus:ring-2 focus:ring-indigo-500`}
            />
          </div>

          {/* تصفية نوع المستخدم */}
          <div className="relative">
            <Filter
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400"
              size={20}
            />
            <select
              value={filterType}
              onChange={(e) => setFilterType(e.target.value)}
              className={`pr-10 pl-4 py-2 rounded-lg border ${
                darkMode
                  ? "bg-gray-800 border-gray-600 text-white"
                  : "bg-gray-50 border-gray-300 text-gray-900"
              } focus:outline-none focus:ring-2 focus:ring-indigo-500`}
            >
              <option value="all">جميع المستخدمين</option>
              <option value="client">طالبي الخدمة</option>
              <option value="craftsman">الحرفيين</option>
              <option value="admin">المديرين</option>
            </select>
          </div>
        </div>
      </div>

      {/* جدول المستخدمين */}
      <div
        className={`rounded-lg ${
          darkMode ? "bg-gray-700" : "bg-white"
        } border ${
          darkMode ? "border-gray-600" : "border-gray-200"
        } shadow-sm overflow-hidden`}
      >
        <div
          className={`px-6 py-4 border-b ${
            darkMode
              ? "border-gray-600 bg-gray-800"
              : "border-gray-200 bg-gray-50"
          }`}
        >
          <h4
            className={`font-semibold ${
              darkMode ? "text-white" : "text-gray-800"
            }`}
          >
            قائمة المستخدمين ({totalItems} مستخدم - عرض {startIndex + 1}-
            {Math.min(endIndex, totalItems)})
          </h4>
        </div>

        {loading ? (
          <div className="flex justify-center items-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
            <span
              className={`mr-3 ${darkMode ? "text-gray-300" : "text-gray-600"}`}
            >
              جاري التحميل...
            </span>
          </div>
        ) : filteredUsers.length === 0 ? (
          <div className="text-center py-12">
            <Users
              className={`mx-auto h-12 w-12 ${
                darkMode ? "text-gray-400" : "text-gray-300"
              } mb-4`}
            />
            <p className={`${darkMode ? "text-gray-300" : "text-gray-600"}`}>
              {searchTerm || filterType !== "all"
                ? "لا توجد نتائج مطابقة للبحث"
                : "لا يوجد مستخدمين"}
            </p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className={darkMode ? "bg-gray-800" : "bg-gray-50"}>
                <tr>
                  <th
                    className={`px-6 py-3 text-right text-xs font-medium ${
                      darkMode ? "text-gray-300" : "text-gray-500"
                    } uppercase tracking-wider`}
                  >
                    المستخدم
                  </th>
                  <th
                    className={`px-6 py-3 text-right text-xs font-medium ${
                      darkMode ? "text-gray-300" : "text-gray-500"
                    } uppercase tracking-wider`}
                  >
                    النوع
                  </th>
                  <th
                    className={`px-6 py-3 text-right text-xs font-medium ${
                      darkMode ? "text-gray-300" : "text-gray-500"
                    } uppercase tracking-wider`}
                  >
                    الحالة
                  </th>
                  <th
                    className={`px-6 py-3 text-right text-xs font-medium ${
                      darkMode ? "text-gray-300" : "text-gray-500"
                    } uppercase tracking-wider`}
                  >
                    تاريخ التسجيل
                  </th>
                  <th
                    className={`px-6 py-3 text-right text-xs font-medium ${
                      darkMode ? "text-gray-300" : "text-gray-500"
                    } uppercase tracking-wider`}
                  >
                    الإجراءات
                  </th>
                </tr>
              </thead>
              <tbody
                className={`${darkMode ? "bg-gray-700" : "bg-white"} divide-y ${
                  darkMode ? "divide-gray-600" : "divide-gray-200"
                }`}
              >
                {currentUsers.map((user) => (
                  <tr
                    key={user._id}
                    className={`hover:${
                      darkMode ? "bg-gray-600" : "bg-gray-50"
                    } transition-colors`}
                  >
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div
                          className={`text-sm font-medium ${
                            darkMode ? "text-white" : "text-gray-900"
                          }`}
                        >
                          {user.name}
                        </div>
                        <div
                          className={`text-sm ${
                            darkMode ? "text-gray-300" : "text-gray-500"
                          }`}
                        >
                          {user.email}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span
                        className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          user.userType === "admin"
                            ? "bg-purple-100 text-purple-800"
                            : user.userType === "craftsman"
                            ? "bg-blue-100 text-blue-800"
                            : "bg-green-100 text-green-800"
                        }`}
                      >
                        {user.userType === "admin"
                          ? "مدير"
                          : user.userType === "craftsman"
                          ? "حرفي"
                          : "طالب خدمة"}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span
                        className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          user.isActive
                            ? "bg-green-100 text-green-800"
                            : "bg-red-100 text-red-800"
                        }`}
                      >
                        {user.isActive ? "نشط" : "غير نشط"}
                      </span>
                    </td>
                    <td
                      className={`px-6 py-4 whitespace-nowrap text-sm ${
                        darkMode ? "text-gray-300" : "text-gray-500"
                      }`}
                    >
                      {new Date(user.createdAt).toLocaleDateString("en-GB")}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex items-center gap-2">
                        <button
                          onClick={() => viewUser(user)}
                          className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                          title="عرض التفاصيل"
                        >
                          <Eye size={16} />
                        </button>
                        <button
                          onClick={() => editUser(user)}
                          className="p-2 text-indigo-600 hover:bg-indigo-50 rounded-lg transition-colors"
                          title="تعديل"
                        >
                          <Edit size={16} />
                        </button>
                        {user.userType !== "admin" && (
                          <button
                            onClick={() => deleteUser(user._id, user.name)}
                            className="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                            title="حذف"
                          >
                            <Trash2 size={16} />
                          </button>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Pagination Controls */}
      {!loading && filteredUsers.length > 0 && (
        <div
          className={`mt-6 flex flex-col sm:flex-row justify-between items-center gap-4 p-4 rounded-lg ${
            darkMode ? "bg-gray-700" : "bg-white"
          } border ${
            darkMode ? "border-gray-600" : "border-gray-200"
          } shadow-sm`}
        >
          {/* Items per page selector */}
          <div className="flex items-center gap-2">
            <span
              className={`text-sm ${
                darkMode ? "text-gray-300" : "text-gray-600"
              }`}
            >
              عرض
            </span>
            <select
              value={itemsPerPage}
              onChange={(e) => {
                setItemsPerPage(Number(e.target.value));
                setCurrentPage(1);
              }}
              className={`px-3 py-1 rounded border ${
                darkMode
                  ? "bg-gray-800 border-gray-600 text-white"
                  : "bg-white border-gray-300 text-gray-900"
              } focus:outline-none focus:ring-2 focus:ring-indigo-500`}
            >
              <option value={5}>5</option>
              <option value={10}>10</option>
              <option value={20}>20</option>
              <option value={50}>50</option>
            </select>
            <span
              className={`text-sm ${
                darkMode ? "text-gray-300" : "text-gray-600"
              }`}
            >
              من {totalItems} مستخدم
            </span>
          </div>

          {/* Page info */}
          <div
            className={`text-sm ${
              darkMode ? "text-gray-300" : "text-gray-600"
            }`}
          >
            صفحة {currentPage} من {totalPages}
          </div>

          {/* Pagination buttons */}
          <div className="flex items-center gap-1">
            <button
              onClick={() => setCurrentPage(1)}
              disabled={currentPage === 1}
              className={`px-3 py-1 rounded ${
                currentPage === 1
                  ? "bg-gray-200 text-gray-400 cursor-not-allowed"
                  : darkMode
                  ? "bg-gray-600 text-white hover:bg-gray-500"
                  : "bg-gray-100 text-gray-700 hover:bg-gray-200"
              } transition-colors`}
            >
              الأولى
            </button>

            <button
              onClick={() => setCurrentPage(currentPage - 1)}
              disabled={currentPage === 1}
              className={`px-3 py-1 rounded ${
                currentPage === 1
                  ? "bg-gray-200 text-gray-400 cursor-not-allowed"
                  : darkMode
                  ? "bg-gray-600 text-white hover:bg-gray-500"
                  : "bg-gray-100 text-gray-700 hover:bg-gray-200"
              } transition-colors`}
            >
              السابق
            </button>

            {/* Page numbers */}
            {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
              let pageNum;
              if (totalPages <= 5) {
                pageNum = i + 1;
              } else if (currentPage <= 3) {
                pageNum = i + 1;
              } else if (currentPage >= totalPages - 2) {
                pageNum = totalPages - 4 + i;
              } else {
                pageNum = currentPage - 2 + i;
              }

              return (
                <button
                  key={pageNum}
                  onClick={() => setCurrentPage(pageNum)}
                  className={`px-3 py-1 rounded ${
                    currentPage === pageNum
                      ? "bg-indigo-600 text-white"
                      : darkMode
                      ? "bg-gray-600 text-white hover:bg-gray-500"
                      : "bg-gray-100 text-gray-700 hover:bg-gray-200"
                  } transition-colors`}
                >
                  {pageNum}
                </button>
              );
            })}

            <button
              onClick={() => setCurrentPage(currentPage + 1)}
              disabled={currentPage === totalPages}
              className={`px-3 py-1 rounded ${
                currentPage === totalPages
                  ? "bg-gray-200 text-gray-400 cursor-not-allowed"
                  : darkMode
                  ? "bg-gray-600 text-white hover:bg-gray-500"
                  : "bg-gray-100 text-gray-700 hover:bg-gray-200"
              } transition-colors`}
            >
              التالي
            </button>

            <button
              onClick={() => setCurrentPage(totalPages)}
              disabled={currentPage === totalPages}
              className={`px-3 py-1 rounded ${
                currentPage === totalPages
                  ? "bg-gray-200 text-gray-400 cursor-not-allowed"
                  : darkMode
                  ? "bg-gray-600 text-white hover:bg-gray-500"
                  : "bg-gray-100 text-gray-700 hover:bg-gray-200"
              } transition-colors`}
            >
              الأخيرة
            </button>
          </div>
        </div>
      )}

      {/* مودال عرض تفاصيل المستخدم */}
      {showUserModal && selectedUser && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div
            className={`${
              darkMode ? "bg-gray-800" : "bg-white"
            } rounded-lg p-6 max-w-md w-full mx-4 shadow-xl`}
          >
            <div className="flex justify-between items-center mb-4">
              <h3
                className={`text-lg font-bold ${
                  darkMode ? "text-white" : "text-gray-900"
                }`}
              >
                تفاصيل المستخدم
              </h3>
              <button
                onClick={() => setShowUserModal(false)}
                className={`text-gray-500 hover:text-gray-700 ${
                  darkMode ? "hover:text-gray-300" : ""
                }`}
              >
                ✕
              </button>
            </div>
            <div className="space-y-3">
              <div>
                <span className="font-semibold">الاسم: </span>
                <span>{selectedUser.name}</span>
              </div>
              <div>
                <span className="font-semibold">البريد الإلكتروني: </span>
                <span>{selectedUser.email}</span>
              </div>
              {selectedUser.phone && (
                <div>
                  <span className="font-semibold">الهاتف: </span>
                  <span>{selectedUser.phone}</span>
                </div>
              )}
              <div>
                <span className="font-semibold">النوع: </span>
                <span>
                  {selectedUser.userType === "admin"
                    ? "مدير"
                    : selectedUser.userType === "craftsman"
                    ? "حرفي"
                    : "طالب خدمة"}
                </span>
              </div>
              <div>
                <span className="font-semibold">الحالة: </span>
                <span
                  className={`px-2 py-1 rounded text-xs ${
                    selectedUser.isActive
                      ? "bg-green-100 text-green-800"
                      : "bg-red-100 text-red-800"
                  }`}
                >
                  {selectedUser.isActive ? "نشط" : "غير نشط"}
                </span>
              </div>
              <div>
                <span className="font-semibold">تاريخ التسجيل: </span>
                <span>
                  {new Date(selectedUser.createdAt).toLocaleDateString("en-GB")}
                </span>
              </div>
            </div>
            <div className="mt-6 flex justify-end">
              <button
                onClick={() => setShowUserModal(false)}
                className="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors"
              >
                إغلاق
              </button>
            </div>
          </div>
        </div>
      )}

      {/* مودال تعديل المستخدم */}
      {showEditModal && selectedUser && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div
            className={`${
              darkMode ? "bg-gray-800" : "bg-white"
            } rounded-lg p-6 max-w-md w-full mx-4 shadow-xl`}
          >
            <div className="flex justify-between items-center mb-4">
              <h3
                className={`text-lg font-bold ${
                  darkMode ? "text-white" : "text-gray-900"
                }`}
              >
                تعديل المستخدم
              </h3>
              <button
                onClick={() => setShowEditModal(false)}
                className={`text-gray-500 hover:text-gray-700 ${
                  darkMode ? "hover:text-gray-300" : ""
                }`}
              >
                ✕
              </button>
            </div>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-1">الاسم</label>
                <input
                  type="text"
                  defaultValue={selectedUser.name}
                  className={`w-full px-3 py-2 border rounded-lg ${
                    darkMode
                      ? "bg-gray-700 border-gray-600 text-white"
                      : "bg-white border-gray-300"
                  }`}
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">
                  البريد الإلكتروني
                </label>
                <input
                  type="email"
                  defaultValue={selectedUser.email}
                  className={`w-full px-3 py-2 border rounded-lg ${
                    darkMode
                      ? "bg-gray-700 border-gray-600 text-white"
                      : "bg-white border-gray-300"
                  }`}
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">الحالة</label>
                <select
                  defaultValue={selectedUser.isActive}
                  className={`w-full px-3 py-2 border rounded-lg ${
                    darkMode
                      ? "bg-gray-700 border-gray-600 text-white"
                      : "bg-white border-gray-300"
                  }`}
                >
                  <option value={true}>نشط</option>
                  <option value={false}>غير نشط</option>
                </select>
              </div>
            </div>
            <div className="mt-6 flex justify-end gap-2">
              <button
                onClick={() => setShowEditModal(false)}
                className="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors"
              >
                إلغاء
              </button>
              <button
                onClick={() => {
                  toast.success("سيتم تطبيق التعديل قريباً");
                  setShowEditModal(false);
                }}
                className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
              >
                حفظ
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default UsersSection;
