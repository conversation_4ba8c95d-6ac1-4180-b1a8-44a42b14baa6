import React, { useState } from "react";
import Button from "../../common/Button";
import Input from "../../common/Input";
import { Eye, EyeOff } from "lucide-react";
import useThemeStore from "../../../store/themeStore";
import useAdminStore from "../../../store/adminStore";
import { toast } from "react-hot-toast";

const EditProfileSection = ({ admin }) => {
  const darkMode = useThemeStore((state) => state.darkMode);
  const updateAdminProfile = useAdminStore((state) => state.updateAdminProfile);
  const updateAdminPassword = useAdminStore(
    (state) => state.updateAdminPassword
  );
  const loading = useAdminStore((state) => state.loading);
  const [imageFile, setImageFile] = useState(null);
  const [formData, setFormData] = useState({
    name: admin?.name || "",
    email: admin?.email || "<EMAIL>",
    phone: admin?.phone || "",
    password: "",
    confirmPassword: "",
    image: admin?.image || "",
  });

  // حالة لإظهار/إخفاء كلمة المرور
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    // التحقق من الحقول المطلوبة
    if (!formData.name || formData.name.trim() === "") {
      toast.error("الاسم مطلوب", {
        duration: 3000,
        style: {
          background: darkMode ? "#1f2937" : "#ffffff",
          color: darkMode ? "#ffffff" : "#000000",
          border: darkMode ? "1px solid #374151" : "1px solid #e5e7eb",
        },
      });

      // التمرير إلى حقل الاسم
      setTimeout(() => {
        const nameField = document.querySelector('input[name="name"]');
        if (nameField) {
          nameField.scrollIntoView({ behavior: "smooth", block: "center" });
          nameField.focus();
          nameField.classList.add("error-flash");
          setTimeout(() => {
            nameField.classList.remove("error-flash");
          }, 2000);
        }
      }, 100);

      return;
    }

    if (!formData.email || formData.email.trim() === "") {
      toast.error("البريد الإلكتروني مطلوب", {
        duration: 3000,
        style: {
          background: darkMode ? "#1f2937" : "#ffffff",
          color: darkMode ? "#ffffff" : "#000000",
          border: darkMode ? "1px solid #374151" : "1px solid #e5e7eb",
        },
      });

      // التمرير إلى حقل البريد الإلكتروني
      setTimeout(() => {
        const emailField = document.querySelector('input[name="email"]');
        if (emailField) {
          emailField.scrollIntoView({ behavior: "smooth", block: "center" });
          emailField.focus();
          emailField.classList.add("error-flash");
          setTimeout(() => {
            emailField.classList.remove("error-flash");
          }, 2000);
        }
      }, 100);

      return;
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      toast.error("يرجى إدخال بريد إلكتروني صالح", {
        duration: 3000,
        style: {
          background: darkMode ? "#1f2937" : "#ffffff",
          color: darkMode ? "#ffffff" : "#000000",
          border: darkMode ? "1px solid #374151" : "1px solid #e5e7eb",
        },
      });

      // التمرير إلى حقل البريد الإلكتروني
      setTimeout(() => {
        const emailField = document.querySelector('input[name="email"]');
        if (emailField) {
          emailField.scrollIntoView({ behavior: "smooth", block: "center" });
          emailField.focus();
          emailField.classList.add("error-flash");
          setTimeout(() => {
            emailField.classList.remove("error-flash");
          }, 2000);
        }
      }, 100);

      return;
    }

    // التحقق من كلمة المرور إذا تم إدخالها
    if (formData.password) {
      if (formData.password.length < 6) {
        toast.error("كلمة المرور يجب أن تكون 6 أحرف على الأقل", {
          duration: 3000,
          style: {
            background: darkMode ? "#1f2937" : "#ffffff",
            color: darkMode ? "#ffffff" : "#000000",
            border: darkMode ? "1px solid #374151" : "1px solid #e5e7eb",
          },
        });
        return;
      }

      if (formData.password !== formData.confirmPassword) {
        toast.error("كلمات المرور غير متطابقة", {
          duration: 3000,
          style: {
            background: darkMode ? "#1f2937" : "#ffffff",
            color: darkMode ? "#ffffff" : "#000000",
            border: darkMode ? "1px solid #374151" : "1px solid #e5e7eb",
          },
        });

        // التمرير إلى حقل تأكيد كلمة المرور
        setTimeout(() => {
          const confirmPasswordField = document.querySelector(
            'input[name="confirmPassword"]'
          );
          if (confirmPasswordField) {
            confirmPasswordField.scrollIntoView({
              behavior: "smooth",
              block: "center",
            });
            confirmPasswordField.focus();
            confirmPasswordField.classList.add("error-flash");
            setTimeout(() => {
              confirmPasswordField.classList.remove("error-flash");
            }, 2000);
          }
        }, 100);

        return;
      }
    }

    try {
      console.log("بدء عملية تحديث البيانات...");

      // إعداد البيانات المحدثة
      const updatedData = {
        name: formData.name.trim(),
        email: formData.email.trim(),
      };

      // إضافة رقم الهاتف إذا كان موجودًا
      if (formData.phone) {
        updatedData.phone = formData.phone.trim();
      }

      // إضافة الصورة إذا كانت موجودة ومختلفة عن الحالية
      if (formData.image && formData.image !== admin?.image) {
        updatedData.image = formData.image;
      }

      console.log("البيانات المحدثة:", updatedData);
      console.log("ملف الصورة:", imageFile);

      // تحديث البيانات الأساسية
      console.log("استدعاء updateAdminProfile...");
      const profileResult = await updateAdminProfile(updatedData, imageFile);
      console.log("نتيجة تحديث البيانات:", profileResult);

      // تحديث كلمة المرور منفصلة إذا تم إدخالها
      if (formData.password) {
        console.log('استدعاء updateAdminPassword...');
        const passwordData = {
          newPassword: formData.password,
        };

        const passwordResult = await updateAdminPassword(passwordData);
        console.log('نتيجة تحديث كلمة المرور:', passwordResult);
      }

      toast.success("تم تحديث البيانات بنجاح", {
        duration: 3000,
        style: {
          background: darkMode ? "#1f2937" : "#ffffff",
          color: darkMode ? "#ffffff" : "#000000",
          border: darkMode ? "1px solid #374151" : "1px solid #e5e7eb",
        },
      });

      // إعادة تعيين حقول كلمة المرور والصورة
      setFormData((prev) => ({
        ...prev,
        password: "",
        confirmPassword: "",
      }));
      setImageFile(null);
    } catch (error) {
      console.error("خطأ في تحديث البيانات:", error);

      let errorMsg = "حدث خطأ أثناء تحديث البيانات";

      if (error.response?.status === 401) {
        errorMsg = "انتهت صلاحية جلسة العمل، يرجى تسجيل الدخول مرة أخرى";
      } else if (error.response?.status === 403) {
        errorMsg = "ليس لديك صلاحية لتنفيذ هذا الإجراء";
      } else if (error.response?.data?.message) {
        errorMsg = error.response.data.message;
      } else if (error.message) {
        errorMsg = error.message;
      }

      toast.error(errorMsg, {
        duration: 5000,
        style: {
          background: darkMode ? "#1f2937" : "#ffffff",
          color: darkMode ? "#ffffff" : "#000000",
          border: darkMode ? "1px solid #374151" : "1px solid #e5e7eb",
        },
      });
    }
  };

  const handleImageChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      // التحقق من نوع الملف
      if (!file.type.startsWith("image/")) {
        toast.error("يرجى اختيار ملف صورة صالح", {
          duration: 3000,
          style: {
            background: darkMode ? "#1f2937" : "#ffffff",
            color: darkMode ? "#ffffff" : "#000000",
            border: darkMode ? "1px solid #374151" : "1px solid #e5e7eb",
          },
        });
        return;
      }

      // التحقق من حجم الملف (أقل من 5MB)
      if (file.size > 5 * 1024 * 1024) {
        toast.error("حجم الصورة يجب أن يكون أقل من 5 ميجابايت", {
          duration: 3000,
          style: {
            background: darkMode ? "#1f2937" : "#ffffff",
            color: darkMode ? "#ffffff" : "#000000",
            border: darkMode ? "1px solid #374151" : "1px solid #e5e7eb",
          },
        });
        return;
      }

      // حفظ ملف الصورة للرفع لاحق<|im_start|>
      setImageFile(file);

      // عرض معاينة الصورة
      const reader = new FileReader();
      reader.onloadend = () => {
        setFormData((prev) => ({
          ...prev,
          image: reader.result,
        }));
      };
      reader.readAsDataURL(file);
    }
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h3
          className={`text-xl font-bold ${
            darkMode ? "text-indigo-300" : "text-indigo-800"
          } relative`}
        >
          <span className="relative z-10">تعديل البيانات الشخصية</span>
          <span
            className={`absolute bottom-0 left-0 right-0 h-2 ${
              darkMode ? "bg-indigo-500" : "bg-indigo-300"
            } opacity-40 transform -rotate-1 z-0`}
          ></span>
        </h3>
      </div>

      <div
        className={`p-6 rounded-lg ${
          darkMode ? "bg-gray-700" : "bg-gradient-to-br from-white to-indigo-50"
        } border ${
          darkMode ? "border-gray-600" : "border-indigo-100"
        } shadow-md`}
      >
        <form
          onSubmit={handleSubmit}
          className="grid grid-cols-1 md:grid-cols-3 gap-6"
        >
          {/* قسم الصورة الشخصية */}
          <div className="col-span-1 flex flex-col items-center">
            <div className="mb-4 relative">
              <img
                src={formData.image}
                alt={formData.name}
                className="w-32 h-32 rounded-full border-4 border-indigo-300 object-cover"
              />
              <label
                htmlFor="image-upload"
                className={`absolute bottom-0 right-0 p-2 rounded-full ${
                  darkMode
                    ? "bg-gray-800"
                    : "bg-gradient-to-br from-white to-blue-50"
                } border ${
                  darkMode ? "border-gray-700" : "border-indigo-200"
                } shadow-md cursor-pointer hover:bg-indigo-100 transition-colors duration-200`}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className={`h-5 w-5 ${
                    darkMode ? "text-indigo-300" : "text-indigo-600"
                  }`}
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z"
                  />
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M15 13a3 3 0 11-6 0 3 3 0 016 0z"
                  />
                </svg>
              </label>
              <input
                type="file"
                id="image-upload"
                accept="image/*"
                className="hidden"
                onChange={handleImageChange}
              />
            </div>
            <p
              className={`text-sm ${
                darkMode ? "text-gray-300" : "text-gray-600"
              } mb-2 text-center`}
            >
              انقر على الأيقونة لتغيير الصورة الشخصية
            </p>
          </div>

          {/* قسم البيانات الشخصية */}
          <div className="col-span-2 space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Input
                label="الاسم"
                type="text"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                required
                className={`w-full p-2 rounded-md ${
                  darkMode
                    ? "bg-gray-800 border-gray-700 text-white"
                    : "bg-white border-gray-300 text-gray-800"
                } focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-colors duration-200`}
              />

              <Input
                label="البريد الإلكتروني"
                type="email"
                name="email"
                value={formData.email}
                onChange={handleInputChange}
                required
                className={`w-full p-2 rounded-md ${
                  darkMode
                    ? "bg-gray-800 border-gray-700 text-white"
                    : "bg-white border-gray-300 text-gray-800"
                } focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-colors duration-200`}
              />

              <Input
                label="رقم الهاتف"
                type="tel"
                name="phone"
                value={formData.phone}
                onChange={handleInputChange}
                className={`w-full p-2 rounded-md ${
                  darkMode
                    ? "bg-gray-800 border-gray-700 text-white"
                    : "bg-white border-gray-300 text-gray-800"
                } focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-colors duration-200`}
              />
            </div>

            <div
              className={`p-4 rounded-md ${
                darkMode
                  ? "bg-gray-800"
                  : "bg-gradient-to-r from-indigo-50 to-blue-50"
              } border ${
                darkMode ? "border-gray-700" : "border-indigo-100"
              } mb-4`}
            >
              <h4
                className={`font-bold mb-3 ${
                  darkMode ? "text-indigo-300" : "text-indigo-700"
                }`}
              >
                تغيير كلمة المرور
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="relative">
                  <Input
                    label="كلمة المرور الجديدة"
                    type={showPassword ? "text" : "password"}
                    name="password"
                    value={formData.password}
                    onChange={handleInputChange}
                    className={`w-full p-2 rounded-md ${
                      darkMode
                        ? "bg-gray-700 border-gray-600 text-white"
                        : "bg-gradient-to-r from-white to-blue-50/50 border-blue-200 text-gray-800"
                    } focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-colors duration-200 pl-10`}
                  />
                  <button
                    type="button"
                    className={`absolute left-3 top-1/2 transform -translate-y-1/2 mt-2 p-1 rounded-full ${
                      darkMode
                        ? "text-gray-400 hover:text-gray-300"
                        : "text-gray-500 hover:text-gray-700"
                    } focus:outline-none transition-colors duration-200`}
                    onClick={() => setShowPassword(!showPassword)}
                    tabIndex="-1"
                  >
                    {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
                  </button>
                </div>

                <div className="relative">
                  <Input
                    label="تأكيد كلمة المرور"
                    type={showConfirmPassword ? "text" : "password"}
                    name="confirmPassword"
                    value={formData.confirmPassword}
                    onChange={handleInputChange}
                    className={`w-full p-2 rounded-md ${
                      darkMode
                        ? "bg-gray-700 border-gray-600 text-white"
                        : "bg-gradient-to-r from-white to-indigo-50/50 border-indigo-200 text-gray-800"
                    } focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-colors duration-200 pl-10`}
                  />
                  <button
                    type="button"
                    className={`absolute left-3 top-1/2 transform -translate-y-1/2 mt-2 p-1 rounded-full ${
                      darkMode
                        ? "text-gray-400 hover:text-gray-300"
                        : "text-gray-500 hover:text-gray-700"
                    } focus:outline-none transition-colors duration-200`}
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    tabIndex="-1"
                  >
                    {showConfirmPassword ? (
                      <EyeOff size={18} />
                    ) : (
                      <Eye size={18} />
                    )}
                  </button>
                </div>
              </div>
              <p
                className={`text-xs mt-2 ${
                  darkMode ? "text-gray-400" : "text-gray-500"
                }`}
              >
                اترك الحقول فارغة إذا كنت لا ترغب في تغيير كلمة المرور
              </p>
            </div>

            <div className="flex justify-end">
              <Button
                type="submit"
                variant="primary"
                disabled={loading}
                className={`${
                  darkMode
                    ? "bg-gradient-to-r from-[#3730A3] to-[#4238C8] hover:from-[#322e92] hover:to-[#3b32b4]"
                    : "bg-gradient-to-r from-[#4238C8] to-[#3730A3] hover:from-[#3b32b4] hover:to-[#322e92]"
                } text-white transition-all duration-200 shadow-md hover:shadow-lg relative overflow-hidden group ${
                  loading ? "opacity-75 cursor-not-allowed" : ""
                }`}
              >
                <span className="relative z-10 flex items-center">
                  {loading && (
                    <svg
                      className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                    >
                      <circle
                        className="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        strokeWidth="4"
                      ></circle>
                      <path
                        className="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                      ></path>
                    </svg>
                  )}
                  {loading ? "جاري الحفظ..." : "حفظ التغييرات"}
                </span>
                <span className="absolute inset-0 bg-white opacity-20 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700"></span>
              </Button>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
};

export default EditProfileSection;
