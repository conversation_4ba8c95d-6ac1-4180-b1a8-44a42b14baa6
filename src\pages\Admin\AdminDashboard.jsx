import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { motion } from "framer-motion";
import {
  Users,
  Briefcase,
  Calendar,
  FileText,
  Settings,
  Wrench,
} from "lucide-react";
import useAdminStore from "../../store/adminStore";
import useThemeStore from "../../store/themeStore";
import Layout from "../../components/layout/Layout";

// استيراد المكونات الجديدة
import AdminHeader from "../../components/admin/AdminHeader";
import AdminSidebar from "../../components/admin/AdminSidebar";
import UsersSection from "../../components/admin/sections/UsersSection";
import CraftsmenSection from "../../components/admin/sections/CraftsmenSection";
import BookingsSection from "../../components/admin/sections/BookingsSection";
import ContentSection from "../../components/admin/sections/ContentSection";
import SettingsSection from "../../components/admin/sections/SettingsSection";
import ProfileSection from "../../components/admin/sections/ProfileSection";
import EditProfileSection from "../../components/admin/sections/EditProfileSection";
import ProfessionsSection from "../../components/admin/sections/ProfessionsSection";

const AdminDashboard = () => {
  const navigate = useNavigate();
  const { admin, isAuthenticated, logoutAdmin } = useAdminStore();
  const darkMode = useThemeStore((state) => state.darkMode);
  const [activeTab, setActiveTab] = useState("users");

  // التحقق من تسجيل الدخول
  useEffect(() => {
    const checkAdminAuth = async () => {
      console.log("AdminDashboard: بدء التحقق من مصادقة الأدمن");
      console.log("AdminDashboard: حالة المصادقة الحالية:", isAuthenticated);
      console.log(
        "AdminDashboard: بيانات الأدمن:",
        admin ? "موجودة" : "غير موجودة"
      );

      // إذا لم يكن هناك أدمن أو لم يكن مصادق عليه
      if (!isAuthenticated || !admin) {
        console.log("AdminDashboard: محاولة التحقق من المصادقة المحلية");

        try {
          const authResult = await useAdminStore.getState().checkAuth();

          if (!authResult) {
            console.log(
              "AdminDashboard: فشل التحقق من المصادقة، إعادة توجيه لصفحة تسجيل الدخول"
            );
            navigate("/admin/login");
          } else {
            console.log("AdminDashboard: تم التحقق من المصادقة بنجاح");
          }
        } catch (error) {
          console.error("AdminDashboard: خطأ في التحقق من المصادقة:", error);
          navigate("/admin/login");
        }
      } else {
        console.log("AdminDashboard: الأدمن مصادق عليه بالفعل");
      }
    };

    checkAdminAuth();
  }, [isAuthenticated, admin, navigate]);

  // أقسام لوحة التحكم
  const dashboardSections = [
    {
      id: "users",
      label: "إدارة المستخدمين",
      icon: <Users size={20} />,
    },
    {
      id: "craftsmen",
      label: "إدارة الحرفيين",
      icon: <Briefcase size={20} />,
    },
    {
      id: "professions",
      label: "إدارة المهن",
      icon: <Wrench size={20} />,
    },
    {
      id: "bookings",
      label: "إدارة الحجوزات",
      icon: <Calendar size={20} />,
    },
    {
      id: "content",
      label: "إدارة المحتوى",
      icon: <FileText size={20} />,
    },
    {
      id: "settings",
      label: "إعدادات النظام",
      icon: <Settings size={20} />,
    },
  ];

  // تسجيل الخروج
  const handleLogout = () => {
    logoutAdmin();
    navigate("/admin/login");
  };

  // محتوى القسم النشط
  const renderActiveTabContent = () => {
    switch (activeTab) {
      case "users":
        return <UsersSection />;
      case "craftsmen":
        return <CraftsmenSection />;
      case "professions":
        return <ProfessionsSection />;
      case "bookings":
        return <BookingsSection />;
      case "content":
        return <ContentSection />;
      case "settings":
        return <SettingsSection />;
      case "profile":
        return <ProfileSection admin={admin} />;
      case "edit-profile":
        return <EditProfileSection admin={admin} />;
      default:
        return null;
    }
  };

  // عرض شاشة تحميل إذا لم يتم التحقق من المصادقة بعد
  if (!isAuthenticated || !admin) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-indigo-600 mx-auto mb-4"></div>
          <p className="text-indigo-600 font-medium">
            جاري تحميل لوحة التحكم...
          </p>
        </div>
      </div>
    );
  }

  return (
    <Layout hideHeader>
      <div
        className={`min-h-screen ${
          darkMode
            ? "bg-gray-900 text-gray-100"
            : "bg-gradient-to-br from-blue-50 to-indigo-100 text-gray-800"
        } transition-colors duration-300`}
      >
        {/* شريط العنوان */}
        <AdminHeader admin={admin} onLogout={handleLogout} />

        {/* المحتوى الرئيسي */}
        <div className="container mx-auto py-6 px-4">
          <div className="flex flex-col md:flex-row gap-6">
            {/* القائمة الجانبية */}
            <AdminSidebar
              dashboardSections={dashboardSections}
              activeTab={activeTab}
              setActiveTab={setActiveTab}
              admin={admin}
            />

            {/* المحتوى الرئيسي */}
            <motion.div
              className={`flex-1 ${
                darkMode
                  ? "bg-gray-800 border border-gray-700"
                  : "bg-gradient-to-br from-indigo-50 to-indigo-50/60 border border-indigo-100"
              } rounded-lg shadow-md transition-colors duration-300 p-6`}
              key={activeTab}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.3 }}
            >
              {renderActiveTabContent()}
            </motion.div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default AdminDashboard;
