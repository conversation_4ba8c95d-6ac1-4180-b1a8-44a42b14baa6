import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';

/**
 * مكون ScrollToTop
 * يقوم بإعادة تعيين موضع التمرير إلى أعلى الصفحة عند الانتقال بين الصفحات
 */
const ScrollToTop = () => {
  const { pathname } = useLocation();

  useEffect(() => {
    // إعادة تعيين موضع التمرير إلى أعلى الصفحة
    window.scrollTo(0, 0);
  }, [pathname]);

  return null; // هذا المكون لا يعرض أي شيء
};

export default ScrollToTop;
