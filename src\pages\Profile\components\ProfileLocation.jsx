import React from "react"; // مطلوب لاستخدام JSX في هذا المكون، على الرغم من أن ESLint قد يشير إلى أنه غير مستخدم
import { MapPin, Navigation, Building, Compass, Home } from "lucide-react";
import Card from "../../../components/common/Card";
import ProfileMap from "../../../components/maps/ProfileMap";
import useThemeStore from "../../../store/themeStore";

const ProfileLocation = ({
  location,
  workRadius,
  isEditing,
  onLocationChange,
  onRadiusChange,
  streetsInWorkRange = [],
  hospitalsInWorkRange = [],
  mosquesInWorkRange = [],
  // neighborhoodsInWorkRange = [], // غير مستخدم حاليًا
}) => {
  const darkMode = useThemeStore((state) => state.darkMode);
  return (
    <Card
      className={`p-6 mb-6 rounded-xl ${
        darkMode
          ? "bg-gray-800 text-gray-200 border border-gray-700"
          : "bg-gradient-to-br from-blue-50/80 to-indigo-50 border border-indigo-100"
      } shadow-lg transition-colors duration-300`}
    >
      <div className="flex items-center mb-5">
        <div
          className={`p-2 rounded-full mr-3 ${
            darkMode ? "bg-indigo-900/50" : "bg-indigo-100"
          }`}
        >
          <MapPin
            size={22}
            className={`${
              darkMode ? "text-indigo-400" : "text-indigo-500"
            } transition-colors duration-300`}
          />
        </div>
        <h2
          className={`text-xl font-bold ${
            darkMode ? "text-indigo-300" : "text-indigo-800"
          } relative inline-block transition-colors duration-300`}
        >
          <span className="relative z-10">الموقع ونطاق العمل</span>
          <span
            className={`absolute bottom-0 left-0 right-0 h-2 ${
              darkMode ? "bg-indigo-500" : "bg-indigo-300"
            } opacity-40 transform -rotate-1 z-0`}
          ></span>
        </h2>
      </div>

      {isEditing ? (
        <div
          className={`rounded-lg overflow-hidden ${
            darkMode ? "border border-gray-600" : "border border-indigo-100"
          }`}
        >
          <ProfileMap
            location={location}
            setLocation={onLocationChange}
            radius={workRadius}
            setRadius={onRadiusChange}
            height="300px"
            isEditing={true}
            showRadius={true}
            streetsInWorkRange={streetsInWorkRange}
            hospitalsInWorkRange={hospitalsInWorkRange}
            mosquesInWorkRange={mosquesInWorkRange}
            // neighborhoodsInWorkRange={neighborhoodsInWorkRange}
            onLocationChange={onLocationChange}
            onRadiusChange={onRadiusChange}
          />
        </div>
      ) : (
        <div className={`${darkMode ? "text-gray-300" : "text-gray-700"}`}>
          {/* نطاق العمل */}
          <div
            className={`p-4 mb-4 rounded-lg ${
              darkMode
                ? "bg-gray-700/50 border border-gray-600"
                : "bg-gradient-to-r from-blue-100/80 to-indigo-100/80 border border-indigo-100"
            } transition-colors duration-300`}
          >
            <div className="flex items-center">
              <div
                className={`p-2 rounded-full mr-3 ${
                  darkMode ? "bg-indigo-900/70" : "bg-indigo-200"
                }`}
              >
                <Navigation
                  size={18}
                  className={`${
                    darkMode ? "text-indigo-300" : "text-indigo-600"
                  }`}
                />
              </div>
              <div>
                <div
                  className={`text-sm opacity-80 ${
                    darkMode ? "text-gray-400" : "text-gray-500"
                  }`}
                >
                  نطاق العمل
                </div>
                <div
                  className={`font-medium ${
                    darkMode ? "text-indigo-300" : "text-indigo-700"
                  }`}
                >
                  {workRadius} كم
                </div>
              </div>
            </div>
          </div>

          {/* الأماكن ضمن نطاق العمل */}
          <div
            className={`rounded-lg p-4 ${
              darkMode
                ? "bg-gray-700/50 border border-gray-600"
                : "bg-gradient-to-r from-blue-100/80 to-indigo-100/80 border border-indigo-100"
            } transition-colors duration-300`}
          >
            <div className="space-y-4">
              {/* عرض الشوارع ضمن نطاق العمل */}
              {streetsInWorkRange && streetsInWorkRange.length > 0 && (
                <div>
                  <div className="flex items-center mb-3">
                    <div
                      className={`p-1.5 rounded-full mr-2 ${
                        darkMode ? "bg-indigo-900/70" : "bg-indigo-200"
                      }`}
                    >
                      <Home
                        size={16}
                        className={`${
                          darkMode ? "text-indigo-300" : "text-indigo-600"
                        }`}
                      />
                    </div>
                    <h3
                      className={`text-base font-medium ${
                        darkMode ? "text-indigo-300" : "text-indigo-700"
                      }`}
                    >
                      الشوارع ضمن نطاق العمل
                    </h3>
                  </div>
                  <div className="flex flex-wrap gap-2 max-h-40 overflow-y-auto p-1 mr-8">
                    {streetsInWorkRange.slice(0, 20).map((street, index) => (
                      <span
                        key={index}
                        className={`px-3 py-1 rounded-full text-sm ${
                          darkMode
                            ? "bg-gray-600 text-gray-200 border border-gray-500"
                            : "bg-white text-gray-800 border border-indigo-200"
                        }`}
                      >
                        {street}
                      </span>
                    ))}
                    {streetsInWorkRange.length > 20 && (
                      <span
                        className={`px-3 py-1 rounded-full text-sm ${
                          darkMode
                            ? "bg-indigo-900/30 text-indigo-300 border border-indigo-800/50"
                            : "bg-indigo-100 text-indigo-700 border border-indigo-200"
                        }`}
                      >
                        +{streetsInWorkRange.length - 20} شارع آخر
                      </span>
                    )}
                  </div>
                </div>
              )}

              {/* عرض المستشفيات ضمن نطاق العمل */}
              {hospitalsInWorkRange && hospitalsInWorkRange.length > 0 && (
                <div>
                  <div className="flex items-center mb-3">
                    <div
                      className={`p-1.5 rounded-full mr-2 ${
                        darkMode ? "bg-red-900/70" : "bg-red-100"
                      }`}
                    >
                      <Building
                        size={16}
                        className={`${
                          darkMode ? "text-red-300" : "text-red-600"
                        }`}
                      />
                    </div>
                    <h3
                      className={`text-base font-medium ${
                        darkMode ? "text-indigo-300" : "text-indigo-700"
                      }`}
                    >
                      المستشفيات والعيادات ضمن نطاق العمل
                    </h3>
                  </div>
                  <div className="flex flex-wrap gap-2 max-h-40 overflow-y-auto p-1 mr-8">
                    {hospitalsInWorkRange
                      .slice(0, 20)
                      .map((hospital, index) => (
                        <span
                          key={index}
                          className={`px-3 py-1 rounded-full text-sm ${
                            darkMode
                              ? "bg-red-900/30 text-red-300 border border-red-800/50"
                              : "bg-red-50 text-red-800 border border-red-200"
                          }`}
                        >
                          {hospital}
                        </span>
                      ))}
                    {hospitalsInWorkRange.length > 20 && (
                      <span
                        className={`px-3 py-1 rounded-full text-sm ${
                          darkMode
                            ? "bg-indigo-900/30 text-indigo-300 border border-indigo-800/50"
                            : "bg-indigo-100 text-indigo-700 border border-indigo-200"
                        }`}
                      >
                        +{hospitalsInWorkRange.length - 20} مستشفى آخر
                      </span>
                    )}
                  </div>
                </div>
              )}

              {/* عرض المساجد ضمن نطاق العمل */}
              {mosquesInWorkRange && mosquesInWorkRange.length > 0 && (
                <div>
                  <div className="flex items-center mb-3">
                    <div
                      className={`p-1.5 rounded-full mr-2 ${
                        darkMode ? "bg-green-900/70" : "bg-green-100"
                      }`}
                    >
                      <Compass
                        size={16}
                        className={`${
                          darkMode ? "text-green-300" : "text-green-600"
                        }`}
                      />
                    </div>
                    <h3
                      className={`text-base font-medium ${
                        darkMode ? "text-indigo-300" : "text-indigo-700"
                      }`}
                    >
                      المساجد ضمن نطاق العمل
                    </h3>
                  </div>
                  <div className="flex flex-wrap gap-2 max-h-40 overflow-y-auto p-1 mr-8">
                    {mosquesInWorkRange.slice(0, 20).map((mosque, index) => (
                      <span
                        key={index}
                        className={`px-3 py-1 rounded-full text-sm ${
                          darkMode
                            ? "bg-green-900/30 text-green-300 border border-green-800/50"
                            : "bg-green-50 text-green-800 border border-green-200"
                        }`}
                      >
                        {mosque}
                      </span>
                    ))}
                    {mosquesInWorkRange.length > 20 && (
                      <span
                        className={`px-3 py-1 rounded-full text-sm ${
                          darkMode
                            ? "bg-indigo-900/30 text-indigo-300 border border-indigo-800/50"
                            : "bg-indigo-100 text-indigo-700 border border-indigo-200"
                        }`}
                      >
                        +{mosquesInWorkRange.length - 20} مسجد آخر
                      </span>
                    )}
                  </div>
                </div>
              )}

              {/* رسالة إذا لم تكن هناك بيانات */}
              {streetsInWorkRange.length === 0 &&
                hospitalsInWorkRange.length === 0 &&
                mosquesInWorkRange.length === 0 && (
                  <div className="flex flex-col items-center justify-center py-8">
                    <div
                      className={`w-16 h-16 rounded-full flex items-center justify-center mb-4 ${
                        darkMode ? "bg-gray-600" : "bg-indigo-100"
                      }`}
                    >
                      <MapPin
                        size={32}
                        className={`${
                          darkMode ? "text-indigo-400" : "text-indigo-500"
                        } opacity-70`}
                      />
                    </div>
                    <p
                      className={`text-center font-medium ${
                        darkMode ? "text-indigo-300" : "text-indigo-700"
                      } transition-colors duration-300 mb-2`}
                    >
                      لا توجد معلومات متاحة
                    </p>
                    <p
                      className={`text-center text-sm ${
                        darkMode ? "text-gray-400" : "text-gray-500"
                      }`}
                    >
                      لم يتم تحديد أي أماكن ضمن نطاق العمل بعد
                    </p>
                  </div>
                )}
            </div>
          </div>
        </div>
      )}
    </Card>
  );
};

export default ProfileLocation;
