import { create } from "zustand";
import { userService, authService } from "../services/api";

// ملاحظة: نظرًا لأن تفضيلات اللغة هي تفضيلات شخصية للمستخدم،
// سنستخدم localStorage لتخزينها مؤقتًا، ولكن سنقوم بمزامنتها مع الخادم عند تغييرها
// إذا كان المستخدم مسجل الدخول

const LOCAL_STORAGE_KEY = "jobscope-language";

// استرجاع اللغة من التخزين المحلي
const getInitialLanguage = () => {
  if (typeof window !== "undefined") {
    const savedLanguage = localStorage.getItem(LOCAL_STORAGE_KEY);
    if (savedLanguage) {
      return JSON.parse(savedLanguage).language || "ar";
    }

    // إذا لم يكن هناك تفضيل محفوظ، استخدم اللغة العربية كافتراضي
    return "ar";
  }
  return "ar";
};

const useLanguageStore = create((set, get) => ({
  language: getInitialLanguage(),
  loading: false,
  error: null,

  // تعيين اللغة
  setLanguage: async (lang) => {
    // تحديث الحالة المحلية
    set({ language: lang });

    // حفظ التفضيل في التخزين المحلي
    localStorage.setItem(LOCAL_STORAGE_KEY, JSON.stringify({ language: lang }));

    // مزامنة التفضيل مع الخادم إذا كان المستخدم مسجل الدخول
    try {
      const token = localStorage.getItem("token");
      if (token) {
        set({ loading: true });
        await userService.updateProfile({ language: lang });
        set({ loading: false });
      }
    } catch (error) {
      console.error("خطأ في مزامنة تفضيل اللغة مع الخادم:", error);
      set({
        loading: false,
        error: error.message || "حدث خطأ أثناء مزامنة تفضيل اللغة",
      });
    }
  },

  // تحميل تفضيل اللغة من الخادم
  loadLanguagePreference: async () => {
    try {
      const token = localStorage.getItem("token");
      if (token) {
        set({ loading: true });
        const userData = await authService.getCurrentUser();

        if (userData && userData.language) {
          // تحديث الحالة المحلية
          set({ language: userData.language, loading: false });

          // حفظ التفضيل في التخزين المحلي
          localStorage.setItem(
            LOCAL_STORAGE_KEY,
            JSON.stringify({ language: userData.language })
          );
        } else {
          set({ loading: false });
        }
      }
    } catch (error) {
      console.error("خطأ في تحميل تفضيل اللغة من الخادم:", error);
      set({
        loading: false,
        error: error.message || "حدث خطأ أثناء تحميل تفضيل اللغة",
      });
    }
  },
}));

// تحميل تفضيل اللغة من الخادم عند تحميل الصفحة
if (typeof window !== "undefined") {
  useLanguageStore.getState().loadLanguagePreference();
}

export default useLanguageStore;
