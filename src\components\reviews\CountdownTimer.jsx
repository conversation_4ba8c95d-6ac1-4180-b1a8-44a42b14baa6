import React, { useState, useEffect } from 'react';
import { Clock } from 'lucide-react';

/**
 * مكون العداد التنازلي
 * @param {Object} props - خصائص المكون
 * @param {Date|string} props.endTime - وقت انتهاء العداد (تاريخ أو سلسلة نصية)
 * @param {Function} props.onComplete - دالة تنفذ عند انتهاء العداد
 * @param {string} props.className - فئات CSS إضافية
 * @param {boolean} props.darkMode - وضع الألوان الداكنة
 */
const CountdownTimer = ({ endTime, onComplete, className = '', darkMode = false }) => {
  // حالة الوقت المتبقي
  const [timeLeft, setTimeLeft] = useState({
    minutes: 0,
    seconds: 0,
    totalSeconds: 0,
    isComplete: false
  });

  // حساب الوقت المتبقي
  useEffect(() => {
    // تحويل وقت الانتهاء إلى كائن Date
    const endTimeDate = typeof endTime === 'string' ? new Date(endTime) : endTime;
    
    // التحقق من صحة وقت الانتهاء
    if (!endTimeDate || isNaN(endTimeDate.getTime())) {
      console.error('وقت انتهاء غير صالح:', endTime);
      setTimeLeft({ minutes: 0, seconds: 0, totalSeconds: 0, isComplete: true });
      return;
    }

    // تحديث العداد كل ثانية
    const intervalId = setInterval(() => {
      const now = new Date();
      const diff = endTimeDate - now;
      
      // إذا انتهى الوقت
      if (diff <= 0) {
        clearInterval(intervalId);
        setTimeLeft({ minutes: 0, seconds: 0, totalSeconds: 0, isComplete: true });
        if (onComplete) onComplete();
        return;
      }
      
      // حساب الدقائق والثواني المتبقية
      const totalSeconds = Math.floor(diff / 1000);
      const minutes = Math.floor(totalSeconds / 60);
      const seconds = totalSeconds % 60;
      
      setTimeLeft({ minutes, seconds, totalSeconds, isComplete: false });
    }, 1000);
    
    // تنظيف الفاصل الزمني عند إزالة المكون
    return () => clearInterval(intervalId);
  }, [endTime, onComplete]);

  // إذا انتهى العداد
  if (timeLeft.isComplete) {
    return null;
  }

  // تنسيق الوقت المتبقي
  const formattedMinutes = String(timeLeft.minutes).padStart(2, '0');
  const formattedSeconds = String(timeLeft.seconds).padStart(2, '0');

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <Clock size={16} className={darkMode ? 'text-yellow-300' : 'text-yellow-600'} />
      <span className={`font-medium ${darkMode ? 'text-yellow-300' : 'text-yellow-600'}`}>
        {formattedMinutes}:{formattedSeconds}
      </span>
    </div>
  );
};

export default CountdownTimer;
