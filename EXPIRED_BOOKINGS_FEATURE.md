# ميزة الإلغاء التلقائي للطلبات المنتهية الصلاحية

## نظرة عامة
تم إضافة ميزة جديدة لإلغاء الطلبات تلقائياً عندما يتجاوز الوقت الحالي تاريخ ووقت انتهاء الطلب المحدد.

## الميزات الجديدة

### 1. حالة جديدة للطلبات
- **`cancelled_expired`**: حالة جديدة للطلبات الملغية بسبب انتهاء الوقت
- تظهر باللون البرتقالي في واجهة المستخدم
- رسالة واضحة: "ملغي بسبب انتهاء الوقت"

### 2. الإلغاء التلقائي
- يتم فحص الطلبات تلقائياً عند جلب قائمة الطلبات
- يتم إلغاء الطلبات التي تجاوز تاريخ ووقت انتهائها الوقت الحالي
- يتم إلغاء الطلبات ذات الحالة `pending` فقط

### 3. الإشعارات
- إشعار للعميل عند إلغاء طلبه تلقائياً
- إشعار للحرفي إذا كان الطلب مرئياً له

## التغييرات التقنية

### الباك إند

#### 1. نموذج الحجز (`booking.model.js`)
```javascript
status: {
  type: String,
  enum: ['pending', 'accepted', 'rejected', 'completed', 'cancelled', 'cancelled_expired'],
  default: 'pending',
}
```

#### 2. دالة الإلغاء التلقائي (`booking.controller.js`)
```javascript
const cancelExpiredBookings = async () => {
  // البحث عن الطلبات المنتهية الصلاحية
  // تحديث حالتها إلى cancelled_expired
  // إرسال إشعارات للمستخدمين
}
```

#### 3. Endpoints جديدة
- `POST /api/bookings/cancel-expired`: تشغيل عملية الإلغاء التلقائي يدوياً
- تم تحديث `GET /api/bookings/me`: يشغل الفحص التلقائي قبل جلب الطلبات

### الفرونت إند

#### 1. ثوابت جديدة (`config.js`)
```javascript
export const BOOKING_STATUS = {
  // ... الحالات الموجودة
  CANCELLED_EXPIRED: "cancelled_expired",
};

export const BOOKING_STATUS_MESSAGES = {
  // ... الرسائل الموجودة
  [BOOKING_STATUS.CANCELLED_EXPIRED]: "ملغي بسبب انتهاء الوقت",
};
```

#### 2. تحديث واجهة المستخدم
- عرض الحالة الجديدة بلون برتقالي مميز
- رسائل واضحة للمستخدمين
- دعم الحالة الجديدة في جميع صفحات الطلبات

## كيفية العمل

### 1. الفحص التلقائي
```javascript
// يتم تشغيل هذا الفحص عند جلب الطلبات
const cancelExpiredBookings = async () => {
  const now = new Date();
  
  // البحث عن الطلبات المنتهية
  const expiredBookings = await Booking.find({
    status: 'pending',
    endDate: { $exists: true, $ne: null },
    endTime: { $exists: true, $ne: null }
  });

  for (const booking of expiredBookings) {
    const endDateTime = new Date(booking.endDate);
    const [hours, minutes] = booking.endTime.split(':');
    endDateTime.setHours(parseInt(hours), parseInt(minutes), 0, 0);

    if (now > endDateTime) {
      booking.status = 'cancelled_expired';
      await booking.save();
      // إرسال إشعارات...
    }
  }
};
```

### 2. شروط الإلغاء
- الطلب يجب أن يكون في حالة `pending`
- يجب أن يحتوي على `endDate` و `endTime`
- الوقت الحالي تجاوز وقت انتهاء الطلب

### 3. الإشعارات
- **للعميل**: "تم إلغاء طلبك تلقائياً بسبب انتهاء الوقت المحدد"
- **للحرفي**: "تم إلغاء طلب [اسم العميل] تلقائياً بسبب انتهاء الوقت المحدد"

## الاختبار

### تشغيل اختبار الميزة
```bash
node test_expired_bookings.js
```

### اختبار يدوي
1. إنشاء طلب جديد بتاريخ ووقت انتهاء في الماضي
2. الانتقال إلى صفحة الطلبات
3. التحقق من إلغاء الطلب تلقائياً وظهور الحالة الجديدة

## الأمان والأداء

### الأمان
- الفحص يتم على مستوى الخادم
- لا يمكن للمستخدمين التلاعب بالعملية
- الإشعارات آمنة ومحمية

### الأداء
- الفحص يتم فقط عند الحاجة (عند جلب الطلبات)
- استعلامات قاعدة البيانات محسنة
- لا يؤثر على أداء التطبيق

## المتطلبات
- MongoDB مع دعم للتواريخ والأوقات
- Node.js مع Express
- React مع Zustand للحالة العامة

## الصيانة
- مراقبة سجلات الخادم للتأكد من عمل الميزة
- فحص دوري لقاعدة البيانات للطلبات المنتهية
- تحديث الرسائل والإشعارات حسب الحاجة
