import React, { useState } from "react";
import {
  Star,
  ChevronDown,
  ChevronUp,
  Calendar,
  User,
  ChevronLeft,
  ChevronRight,
} from "lucide-react";
import Card from "../common/Card";
import Button from "../common/Button";

const SimpleReviewsList = ({ reviews, darkMode }) => {
  const [expandedReviews, setExpandedReviews] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const reviewsPerPage = 3;

  // حساب عدد الصفحات
  const totalPages = Math.ceil(reviews.length / reviewsPerPage);



  // التأكد من أن التقييمات مرتبة من الأحدث إلى الأقدم
  // ملاحظة: هذا احتياطي فقط، لأن الترتيب يجب أن يتم في متجر التقييمات
  const sortedReviews = [...reviews].sort((a, b) => {
    // التأكد من أن التواريخ صالحة
    const dateA = new Date(a.createdAt || 0);
    const dateB = new Date(b.createdAt || 0);
    return dateB - dateA;
  });

  // الحصول على التقييمات المعروضة في الصفحة الحالية
  const indexOfLastReview = currentPage * reviewsPerPage;
  const indexOfFirstReview = indexOfLastReview - reviewsPerPage;
  const visibleReviews = sortedReviews.slice(indexOfFirstReview, indexOfLastReview);

  // الانتقال إلى الصفحة التالية
  const goToNextPage = () => {
    setCurrentPage((prev) => Math.min(prev + 1, totalPages));
  };

  // الانتقال إلى الصفحة السابقة
  const goToPrevPage = () => {
    setCurrentPage((prev) => Math.max(prev - 1, 1));
  };

  // الانتقال إلى صفحة محددة
  const goToPage = (pageNumber) => {
    setCurrentPage(pageNumber);
  };

  const toggleReviewExpand = (reviewId) => {
    if (expandedReviews.includes(reviewId)) {
      setExpandedReviews(expandedReviews.filter((id) => id !== reviewId));
    } else {
      setExpandedReviews([...expandedReviews, reviewId]);
    }
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("ar-SY");
  };

  // Componente para mostrar las estrellas de calificación
  const RatingStars = ({ rating }) => (
    <div className="flex">
      {[1, 2, 3, 4, 5].map((star) => (
        <Star
          key={star}
          size={16}
          className={`${
            star <= rating
              ? `${
                  darkMode
                    ? "text-yellow-400 fill-yellow-400"
                    : "text-yellow-500 fill-yellow-500"
                }`
              : `${darkMode ? "text-gray-600" : "text-gray-300"}`
          }`}
        />
      ))}
    </div>
  );

  // التحقق من وجود تقييمات
  if (!visibleReviews || visibleReviews.length === 0) {
    return (
      <div className="text-center py-8">
        <div className={`mb-4 ${darkMode ? "text-gray-300" : "text-gray-600"}`}>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-16 w-16 mx-auto mb-4 opacity-50"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={1.5}
              d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z"
            />
          </svg>
          <p className="text-lg font-medium">لا توجد تقييمات حتى الآن</p>
          <p className="mt-2 text-sm">سيظهر هنا تقييمات العملاء بمجرد إضافتها</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {visibleReviews.map((review) => {
        const isExpanded = expandedReviews.includes(review.id);

        // التحقق مما إذا كان التقييم افتراضيًا
        const isDefaultReview = review.isDefaultReview;

        return (
          <div
            key={review.id}
            className={`border-b ${darkMode ? "border-gray-700" : ""} pb-4 ${isDefaultReview ? "opacity-70" : ""}`}
          >
            <div className="flex items-start">
              <div className="w-10 h-10 rounded-full ml-3 bg-indigo-200 flex items-center justify-center">
                <User size={20} className="text-indigo-700" />
              </div>
              <div className="flex-1">
                <div className="flex items-center mb-1">
                  <h4
                    className={`font-bold ml-2 ${
                      darkMode ? "text-gray-200" : ""
                    }`}
                  >
                    عميل
                  </h4>
                  <div className="flex items-center">
                    <RatingStars rating={review.overallRating} />
                    <span className="mr-2 text-sm">
                      {review.overallRating}/5
                    </span>
                  </div>
                </div>

                <div className="flex items-center text-sm mb-2">
                  <Calendar size={14} className="ml-1" />
                  <span
                    className={`${
                      darkMode ? "text-gray-400" : "text-gray-500"
                    }`}
                  >
                    {formatDate(review.createdAt)}
                  </span>
                </div>

                <p
                  className={`${
                    darkMode ? "text-gray-300" : "text-gray-700"
                  } mb-2`}
                >
                  {review.comment}
                </p>

                {/* Calificaciones detalladas (expandibles) */}
                {isExpanded && (
                  <div
                    className={`mt-3 p-3 rounded-md ${
                      darkMode ? "bg-gray-700" : "bg-white/80"
                    }`}
                  >
                    <div className="grid grid-cols-2 gap-2 text-sm">
                      <div className="flex items-center">
                        <span
                          className={`ml-2 ${
                            darkMode ? "text-gray-400" : "text-gray-600"
                          }`}
                        >
                          جودة العمل:
                        </span>
                        <RatingStars rating={review.qualityRating} />
                      </div>
                      <div className="flex items-center">
                        <span
                          className={`ml-2 ${
                            darkMode ? "text-gray-400" : "text-gray-600"
                          }`}
                        >
                          الالتزام بالوقت:
                        </span>
                        <RatingStars rating={review.punctualityRating} />
                      </div>
                      <div className="flex items-center">
                        <span
                          className={`ml-2 ${
                            darkMode ? "text-gray-400" : "text-gray-600"
                          }`}
                        >
                          القيمة مقابل السعر:
                        </span>
                        <RatingStars rating={review.priceRating} />
                      </div>
                      <div className="flex items-center">
                        <span
                          className={`ml-2 ${
                            darkMode ? "text-gray-400" : "text-gray-600"
                          }`}
                        >
                          التواصل:
                        </span>
                        <RatingStars rating={review.communicationRating} />
                      </div>
                    </div>
                  </div>
                )}

                {/* Imágenes de la evaluación */}
                {review.images && review.images.length > 0 && (
                  <div className="mt-3 grid grid-cols-3 gap-2">
                    {review.images.map((image, index) => (
                      <div
                        key={index}
                        className="h-20 rounded-md overflow-hidden"
                      >
                        <img
                          src={image}
                          alt={`صورة التقييم ${index + 1}`}
                          className="w-full h-full object-cover"
                        />
                      </div>
                    ))}
                  </div>
                )}

                {/* Botón para expandir/colapsar */}
                <button
                  onClick={() => toggleReviewExpand(review.id)}
                  className={`mt-2 text-sm flex items-center ${
                    darkMode ? "text-indigo-400" : "text-indigo-600"
                  } hover:underline`}
                >
                  {isExpanded ? (
                    <>
                      <ChevronUp size={16} className="ml-1" />
                      عرض أقل
                    </>
                  ) : (
                    <>
                      <ChevronDown size={16} className="ml-1" />
                      عرض التفاصيل
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        );
      })}

      {/* شريط ترقيم الصفحات */}
      {totalPages > 1 && (
        <div className="flex justify-center items-center mt-6">
          {/* زر الصفحة السابقة */}
          <Button
            variant="secondary"
            onClick={goToPrevPage}
            disabled={currentPage === 1}
            className={`p-2 mx-1 ${
              currentPage === 1
                ? darkMode
                  ? "opacity-50 cursor-not-allowed"
                  : "opacity-50 cursor-not-allowed"
                : ""
            }`}
          >
            <ChevronRight size={16} />
          </Button>

          {/* أرقام الصفحات */}
          <div className="flex mx-2">
            {/* عرض أول صفحتين دائمًا */}
            {[...Array(Math.min(2, totalPages))].map((_, index) => (
              <Button
                key={index + 1}
                variant={currentPage === index + 1 ? "primary" : "secondary"}
                onClick={() => goToPage(index + 1)}
                className={`mx-1 w-8 h-8 flex items-center justify-center ${
                  currentPage === index + 1
                    ? darkMode
                      ? "bg-indigo-600"
                      : "bg-indigo-500"
                    : ""
                }`}
              >
                {index + 1}
              </Button>
            ))}

            {/* إذا كان هناك أكثر من 5 صفحات وكانت الصفحة الحالية > 3، أضف نقاط الحذف */}
            {totalPages > 5 && currentPage > 3 && (
              <span className={`mx-1 flex items-center ${darkMode ? "text-gray-400" : "text-gray-600"}`}>
                ...
              </span>
            )}

            {/* عرض الصفحات المحيطة بالصفحة الحالية */}
            {totalPages > 2 &&
              [...Array(totalPages)].map((_, index) => {
                const pageNumber = index + 1;
                // عرض الصفحة الحالية والصفحات المجاورة لها فقط
                if (
                  (pageNumber > 2 && pageNumber < totalPages - 1) && // ليست من أول صفحتين أو آخر صفحتين
                  (Math.abs(pageNumber - currentPage) <= 1) // الصفحة الحالية أو المجاورة لها
                ) {
                  return (
                    <Button
                      key={pageNumber}
                      variant={currentPage === pageNumber ? "primary" : "secondary"}
                      onClick={() => goToPage(pageNumber)}
                      className={`mx-1 w-8 h-8 flex items-center justify-center ${
                        currentPage === pageNumber
                          ? darkMode
                            ? "bg-indigo-600"
                            : "bg-indigo-500"
                          : ""
                      }`}
                    >
                      {pageNumber}
                    </Button>
                  );
                }
                return null;
              })}

            {/* إذا كان هناك أكثر من 5 صفحات وكانت الصفحة الحالية < totalPages - 2، أضف نقاط الحذف */}
            {totalPages > 5 && currentPage < totalPages - 2 && (
              <span className={`mx-1 flex items-center ${darkMode ? "text-gray-400" : "text-gray-600"}`}>
                ...
              </span>
            )}

            {/* عرض آخر صفحتين دائمًا إذا كان هناك أكثر من صفحتين */}
            {totalPages > 2 &&
              [...Array(Math.min(2, totalPages))].map((_, index) => {
                const pageNumber = totalPages - 1 + index;
                return (
                  <Button
                    key={pageNumber}
                    variant={currentPage === pageNumber ? "primary" : "secondary"}
                    onClick={() => goToPage(pageNumber)}
                    className={`mx-1 w-8 h-8 flex items-center justify-center ${
                      currentPage === pageNumber
                        ? darkMode
                          ? "bg-indigo-600"
                          : "bg-indigo-500"
                        : ""
                    }`}
                  >
                    {pageNumber}
                  </Button>
                );
              })}
          </div>

          {/* زر الصفحة التالية */}
          <Button
            variant="secondary"
            onClick={goToNextPage}
            disabled={currentPage === totalPages}
            className={`p-2 mx-1 ${
              currentPage === totalPages
                ? darkMode
                  ? "opacity-50 cursor-not-allowed"
                  : "opacity-50 cursor-not-allowed"
                : ""
            }`}
          >
            <ChevronLeft size={16} />
          </Button>
        </div>
      )}

      {/* عرض معلومات الصفحة الحالية */}
      {totalPages > 1 && (
        <div className={`text-center mt-2 text-sm ${darkMode ? "text-gray-400" : "text-gray-600"}`}>
          عرض {indexOfFirstReview + 1}-{Math.min(indexOfLastReview, reviews.length)} من {reviews.length} تقييم
        </div>
      )}
    </div>
  );
};

export default SimpleReviewsList;
