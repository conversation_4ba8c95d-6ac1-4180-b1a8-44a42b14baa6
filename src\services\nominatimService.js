/**
 * خدمة OpenStreetMap للحصول على بيانات الأماكن
 * تستخدم للحصول على بيانات الشوارع والمساجد والمستشفيات
 */

// كائن للتخزين المؤقت (cache)
const placesCache = {
  streets: {},
  hospitals: {},
  mosques: {},
  // دالة لمسح التخزين المؤقت
  clear: () => {
    placesCache.streets = {};
    placesCache.hospitals = {};
    placesCache.mosques = {};
    console.log("تم مسح التخزين المؤقت في nominatimService");
  },
};

// بيانات ثابتة للشوارع الرئيسية في دمشق وضواحيها
const mainStreets = [
  "شارع الثورة",
  "شارع بغداد",
  "شارع النصر",
  "شارع الحمراء",
  "شارع العابد",
  "شارع فيصل",
  "شارع 29 أيار",
  "شارع المزرعة",
  "شارع الجلاء",
  "شارع الملك فيصل",
  "شارع خالد بن الوليد",
  "شارع مدحت باشا",
  "شارع القوتلي",
  "شارع أبو رمانة",
  "شارع الفردوس",
  "شارع الباكستان",
  "شارع المهدي بن بركة",
  "شارع نزار قباني",
  "شارع عدنان المالكي",
  "شارع الشهبندر",
  "شارع الأمين",
  "شارع الزهراوي",
];

// بيانات ثابتة للمستشفيات الرئيسية في دمشق وضواحيها
const mainHospitals = [
  "مشفى المواساة",
  "مشفى الأسد الجامعي",
  "مشفى المجتهد",
  "مشفى التوليد",
  "مشفى الأطفال",
  "مشفى ابن النفيس",
  "مشفى الهلال الأحمر",
  "مشفى الشامي",
  "مشفى الرازي",
  "مشفى الفرنسي",
  "مشفى الأندلس",
  "مشفى الشرق",
];

// بيانات ثابتة للمساجد الرئيسية في دمشق وضواحيها
const mainMosques = [
  "الجامع الأموي",
  "جامع تنكز",
  "جامع السنانية",
  "جامع الدرويشية",
  "جامع الورد",
  "جامع زيد بن ثابت",
  "جامع السادات",
  "جامع الدقاق",
  "جامع الحنابلة",
  "جامع التوبة",
  "جامع الإيمان",
  "جامع الرفاعي",
];

/**
 * دالة للحصول على الشوارع والمستشفيات والمساجد ضمن نطاق معين
 * @param {number} lat - خط العرض
 * @param {number} lng - خط الطول
 * @param {number} radius - نصف القطر بالكيلومتر
 * @returns {Promise<Object>} - كائن يحتوي على قوائم الشوارع والمستشفيات والمساجد
 */
export const fetchPlacesFromNominatim = async (lat, lng, radius) => {
  try {
    console.log("جلب البيانات للموقع:", { lat, lng, radius });

    // التحقق من وجود البيانات في التخزين المؤقت
    const cacheKey = `${lat.toFixed(4)}_${lng.toFixed(4)}_${radius}`;

    if (placesCache.streets[cacheKey]) {
      console.log("استخدام البيانات المخزنة مؤقتًا");
      return {
        streets: placesCache.streets[cacheKey],
        hospitals: placesCache.hospitals[cacheKey],
        mosques: placesCache.mosques[cacheKey],
      };
    }

    // محاولة جلب البيانات من OpenStreetMap
    try {
      // جلب الشوارع والمستشفيات والمساجد من OpenStreetMap
      const [streets, hospitals, mosques] = await Promise.all([
        fetchStreets(lat, lng, radius),
        fetchHospitals(lat, lng, radius),
        fetchMosques(lat, lng, radius),
      ]);

      // تخزين البيانات في التخزين المؤقت
      placesCache.streets[cacheKey] = streets;
      placesCache.hospitals[cacheKey] = hospitals;
      placesCache.mosques[cacheKey] = mosques;

      return { streets, hospitals, mosques };
    } catch (error) {
      console.error("خطأ في جلب البيانات من OpenStreetMap:", error);

      // استخدام البيانات الثابتة مع تصفيتها حسب المسافة
      const filteredStreets = filterByDistance(mainStreets, lat, lng, radius);
      const filteredHospitals = filterByDistance(
        mainHospitals,
        lat,
        lng,
        radius
      );
      const filteredMosques = filterByDistance(mainMosques, lat, lng, radius);

      // تخزين البيانات في التخزين المؤقت
      placesCache.streets[cacheKey] = filteredStreets;
      placesCache.hospitals[cacheKey] = filteredHospitals;
      placesCache.mosques[cacheKey] = filteredMosques;

      return {
        streets: filteredStreets,
        hospitals: filteredHospitals,
        mosques: filteredMosques,
      };
    }
  } catch (error) {
    console.error("خطأ عام في جلب البيانات:", error);
    return { streets: [], hospitals: [], mosques: [] };
  }
};

// تم إزالة دالة calculateDistance لأنها غير مستخدمة

/**
 * دالة لتصفية البيانات حسب نصف القطر
 * @param {Array} items - قائمة العناصر
 * @param {number} radius - نصف القطر بالكيلومتر
 * @returns {Array} - قائمة العناصر المصفاة
 */
const filterByDistance = (items, radius) => {
  // تحديد عدد العناصر بناءً على نصف القطر
  // كلما زاد نصف القطر، زاد عدد العناصر
  const count = Math.min(Math.ceil(radius * 5), items.length);

  // اختيار عناصر عشوائية من القائمة
  const result = [];
  const usedIndices = new Set();

  while (result.length < count && usedIndices.size < items.length) {
    const randomIndex = Math.floor(Math.random() * items.length);
    if (!usedIndices.has(randomIndex)) {
      usedIndices.add(randomIndex);
      result.push(items[randomIndex]);
    }
  }

  return result;
};

/**
 * دالة لجلب الشوارع ضمن نطاق معين
 * @param {number} lat - خط العرض المركزي
 * @param {number} lng - خط الطول المركزي
 * @param {number} radius - نصف القطر بالكيلومتر
 * @returns {Promise<Array>} - قائمة بأسماء الشوارع
 */
const fetchStreets = async (lat, lng, radius) => {
  try {
    // نصف القطر بالمتر (يستخدم مباشرة في الاستعلام)

    // استخدام Overpass API بدلاً من Nominatim
    const query = `
      [out:json];
      (
        way["highway"]["name"](around:${radius * 1000},${lat},${lng});
      );
      out tags;
    `;

    const url = "https://overpass-api.de/api/interpreter";
    const response = await fetch(url, {
      method: "POST",
      body: query,
      headers: { "Content-Type": "text/plain" },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    // استخراج أسماء الشوارع من البيانات
    const streets = [];

    if (data && Array.isArray(data.elements)) {
      data.elements.forEach((el) => {
        if (el.tags && el.tags.name) {
          streets.push(el.tags.name.trim());
        }
      });
    }

    // إزالة التكرارات
    return [...new Set(streets)];
  } catch (error) {
    console.error("خطأ في جلب الشوارع:", error);

    // استخدام البيانات الثابتة مع تصفيتها حسب نصف القطر
    return filterByDistance(mainStreets, radius);
  }
};

/**
 * دالة لجلب المستشفيات ضمن نطاق معين
 * @param {number} lat - خط العرض المركزي
 * @param {number} lng - خط الطول المركزي
 * @param {number} radius - نصف القطر بالكيلومتر
 * @returns {Promise<Array>} - قائمة بأسماء المستشفيات
 */
const fetchHospitals = async (lat, lng, radius) => {
  try {
    // استخدام Overpass API بدلاً من Nominatim
    const query = `
      [out:json];
      (
        node["amenity"="hospital"](around:${radius * 1000},${lat},${lng});
        node["amenity"="clinic"](around:${radius * 1000},${lat},${lng});
        node["amenity"="doctors"](around:${radius * 1000},${lat},${lng});
      );
      out tags;
    `;

    const url = "https://overpass-api.de/api/interpreter";
    const response = await fetch(url, {
      method: "POST",
      body: query,
      headers: { "Content-Type": "text/plain" },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    // استخراج أسماء المستشفيات من البيانات
    const hospitals = [];

    if (data && Array.isArray(data.elements)) {
      data.elements.forEach((el) => {
        if (el.tags && el.tags.name) {
          hospitals.push(el.tags.name.trim());
        }
      });
    }

    // إزالة التكرارات
    return [...new Set(hospitals)];
  } catch (error) {
    console.error("خطأ في جلب المستشفيات:", error);

    // استخدام البيانات الثابتة مع تصفيتها حسب نصف القطر
    return filterByDistance(mainHospitals, radius);
  }
};

/**
 * دالة لجلب المساجد ضمن نطاق معين
 * @param {number} lat - خط العرض المركزي
 * @param {number} lng - خط الطول المركزي
 * @param {number} radius - نصف القطر بالكيلومتر
 * @returns {Promise<Array>} - قائمة بأسماء المساجد
 */
const fetchMosques = async (lat, lng, radius) => {
  try {
    // استخدام Overpass API بدلاً من Nominatim
    const query = `
      [out:json];
      (
        node["amenity"="place_of_worship"]["religion"="muslim"](around:${radius *
          1000},${lat},${lng});
        node["building"="mosque"](around:${radius * 1000},${lat},${lng});
      );
      out tags;
    `;

    const url = "https://overpass-api.de/api/interpreter";
    const response = await fetch(url, {
      method: "POST",
      body: query,
      headers: { "Content-Type": "text/plain" },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    // استخراج أسماء المساجد من البيانات
    const mosques = [];

    if (data && Array.isArray(data.elements)) {
      data.elements.forEach((el) => {
        if (el.tags && el.tags.name) {
          mosques.push(el.tags.name.trim());
        }
      });
    }

    // إزالة التكرارات
    return [...new Set(mosques)];
  } catch (error) {
    console.error("خطأ في جلب المساجد:", error);

    // استخدام البيانات الثابتة مع تصفيتها حسب نصف القطر
    return filterByDistance(mainMosques, radius);
  }
};

// تصدير دالة مسح التخزين المؤقت
export const clearPlacesCache = () => {
  placesCache.clear();
};

export default {
  fetchPlacesFromNominatim,
  clearPlacesCache,
};
