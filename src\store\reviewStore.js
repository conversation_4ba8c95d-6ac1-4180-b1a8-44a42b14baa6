import { create } from "zustand";
import { reviewService } from "../services/api";

const useReviewStore = create((set, get) => ({
  reviews: [], // بدء بقائمة فارغة بدون بيانات وهمية
  loading: false,
  error: null,

  // تحميل تقييم محدد من الخادم
  fetchReviewById: async (reviewId) => {
    if (!reviewId) {
      console.warn("fetchReviewById - معرف التقييم غير موجود");
      return null;
    }

    // استخراج المعرف الفعلي إذا كان كائنًا
    let actualReviewId = reviewId;

    // إذا كان reviewId كائنًا، نحاول استخراج المعرف منه
    if (typeof reviewId === "object" && reviewId !== null) {
      actualReviewId = reviewId.id || reviewId._id;
      
    }

    // إذا لم نتمكن من استخراج معرف صالح، نعود
    if (!actualReviewId) {
      console.warn(
        "fetchReviewById - لم يتم العثور على معرف صالح في الكائن:",
        reviewId
      );
      return null;
    }

    // تحويل معرف التقييم إلى نص للمقارنة
    const reviewIdStr = String(actualReviewId);

    set({ loading: true, error: null });
    try {
      const response = await reviewService.getReviewById(reviewIdStr);

      if (response) {

        // إضافة التقييم إلى القائمة المحلية إذا لم يكن موجودًا بالفعل
        set((state) => {
          // التحقق مما إذا كان التقييم موجودًا بالفعل
          const reviewExists = state.reviews.some(
            (review) =>
              (review.id &&
                String(review.id) === String(response.id || response._id)) ||
              (review._id &&
                String(review._id) === String(response.id || response._id))
          );

          if (!reviewExists) {
            return {
              reviews: [...state.reviews, response],
              loading: false,
            };
          }

          return { loading: false };
        });

        return response;
      }

      set({ loading: false });
      return null;
    } catch (error) {
      console.error("fetchReviewById - خطأ في تحميل التقييم:", error);
      set({
        error: error.message || "حدث خطأ أثناء تحميل التقييم",
        loading: false,
      });
      return null;
    }
  },

  // تحميل التقييمات من الخادم
  fetchReviews: async (craftsmanId) => {
    set({ loading: true, error: null });
    try {
      const response = await reviewService.getCraftsmanReviews(craftsmanId);

      // التعامل مع الاستجابة سواء كانت كائن به خاصية data أو مصفوفة مباشرة
      const responseData = response && response.data ? response.data : response;

      // التأكد من أن البيانات هي مصفوفة
      const reviewsArray = Array.isArray(responseData) ? responseData : [];

    

      // تنسيق التقييمات للتأكد من وجود معرف صحيح
      const formattedReviews = reviewsArray.map((review) => ({
        ...review,
        id: review._id || review.id,
      }));

      // ترتيب التقييمات من الأحدث إلى الأقدم
      const sortedReviews = [...formattedReviews].sort((a, b) => {
        const dateA = new Date(a.createdAt || 0);
        const dateB = new Date(b.createdAt || 0);
        return dateB - dateA;
      });

      // تحديث المتجر بالتقييمات المرتبة
      set({
        reviews: sortedReviews,
        loading: false,
      });

      return sortedReviews;
    } catch (error) {
      console.error("خطأ في تحميل التقييمات:", error);
      set({
        error: error.message || "حدث خطأ أثناء تحميل التقييمات",
        loading: false,
      });
      return [];
    }
  },

  // تحميل جميع التقييمات من الخادم
  fetchAllReviews: async () => {
    set({ loading: true, error: null });
    try {

      // بدلاً من استخدام نقطة النهاية /reviews التي ترجع خطأ 500،
      // نقوم بتحميل التقييمات من خلال الحجوزات
      const bookingStore = await import("./bookingStore").then(
        (module) => module.default
      );
      const bookings = bookingStore.getState().bookings;

    

      // جمع معرفات التقييمات من الحجوزات
      const reviewIds = bookings
        .filter((booking) => booking.reviewId)
        .map((booking) => booking.reviewId);

     

      // تحميل كل تقييم على حدة
      const reviewPromises = reviewIds.map((reviewId) => {
        // استخراج المعرف الفعلي إذا كان كائنًا
        let actualReviewId = reviewId;

        // إذا كان reviewId كائنًا، نحاول استخراج المعرف منه
        if (typeof reviewId === "object" && reviewId !== null) {
          actualReviewId = reviewId.id || reviewId._id;
        }

        // إذا لم نتمكن من استخراج معرف صالح، نعود
        if (!actualReviewId) {
          console.warn("لم يتم العثور على معرف صالح في الكائن:", reviewId);
          return Promise.resolve(null);
        }

        // تحويل معرف التقييم إلى نص للمقارنة
        const reviewIdStr = String(actualReviewId);

        return reviewService.getReviewById(reviewIdStr).catch((err) => {
          console.error(`خطأ في تحميل التقييم ${reviewIdStr}:`, err);
          return null;
        });
      });

      const reviewsResults = await Promise.all(reviewPromises);
      const validReviews = reviewsResults.filter((review) => review !== null);

     

      set({
        reviews: validReviews || [],
        loading: false,
      });
      return validReviews;
    } catch (error) {
      console.error("خطأ في تحميل جميع التقييمات:", error);
      // تخزين رسالة خطأ أكثر وضوحًا للمستخدم
      const errorMessage =
        error.response?.status === 500
          ? "حدث خطأ في الخادم أثناء تحميل التقييمات. يرجى المحاولة مرة أخرى لاحقًا."
          : "تعذر تحميل التقييمات. يرجى التحقق من اتصالك بالإنترنت والمحاولة مرة أخرى.";

      set({
        error: errorMessage,
        loading: false,
        // إضافة مصفوفة فارغة للتقييمات لتجنب الأخطاء
        reviews: [],
      });
      return [];
    }
  },

  // إضافة تقييم جديد
  addReview: async (review) => {
    set({ loading: true, error: null });

    try {
      // التأكد من وجود المعرفات المطلوبة
      if (!review.craftsman && !review.craftsmanId) {
        console.error("addReview - معرف الحرفي غير موجود");
        set({
          loading: false,
          error: "معرف الحرفي غير موجود",
        });
        return null;
      }

      if (!review.client && !review.clientId) {
        console.error("addReview - معرف العميل غير موجود");
        set({
          loading: false,
          error: "معرف العميل غير موجود",
        });
        return null;
      }

      if (!review.booking && !review.bookingId) {
        console.error("addReview - معرف الحجز غير موجود");
        set({
          loading: false,
          error: "معرف الحجز غير موجود",
        });
        return null;
      }

      // تحضير بيانات التقييم للإرسال إلى الخادم
      const reviewData = {
        // استخدام المعرفات بالصيغة المطلوبة للخادم
        craftsman: review.craftsman || review.craftsmanId,
        client: review.client || review.clientId,
        booking: review.booking || review.bookingId,
        // التقييمات
        overallRating: review.overallRating,
        qualityRating: review.qualityRating,
        punctualityRating: review.punctualityRating,
        priceRating: review.priceRating,
        communicationRating: review.communicationRating,
        // معلومات إضافية
        comment: review.comment,
        images: review.images || [],
      };

    

      // إرسال التقييم إلى الخادم
      const serverReview = await reviewService.createReview(reviewData);

      // إنشاء تقييم محلي باستخدام البيانات من الخادم
      const newReview = {
        ...review,
        ...serverReview,
        id: serverReview._id || serverReview.id || Date.now(),
        createdAt: serverReview.createdAt || new Date().toISOString(),
      };

      // طباعة التقييم الجديد للتصحيح

      // تحديث القائمة المحلية
      set((state) => ({
        reviews: [...state.reviews, newReview],
        loading: false,
      }));

      // طباعة جميع التقييمات بعد الإضافة
      const allReviews = get().reviews;
     

      return newReview;
    } catch (error) {
      console.error("خطأ في إضافة التقييم:", error);

      set({
        loading: false,
        error: error.message || "حدث خطأ أثناء إضافة التقييم",
      });

      return null;
    }
  },

  // Obtener evaluaciones para un artesano específico
  getCraftsmanReviews: (craftsmanId) => {
    const { reviews } = get();


    // التحقق من وجود معرف الحرفي
    if (!craftsmanId) {
      console.warn("getCraftsmanReviews - معرف الحرفي غير موجود");
      return [];
    }

    // تحويل معرف الحرفي إلى نص للمقارنة
    const craftsmanIdStr = String(craftsmanId);

    // التعامل مع حالة خاصة: إذا كان المعرف طويلاً (معرف MongoDB)، نتحقق أيضًا من المطابقة الجزئية
    const isMongoId = craftsmanIdStr.length > 10;


    // البحث عن التقييمات
    const craftsmanReviews = reviews.filter((review) => {
      // التحقق من وجود معرف الحرفي في التقييم
      if (!review.craftsmanId && !review.craftsman) return false;

      // تحويل معرف الحرفي في التقييم إلى نص
      let reviewCraftsmanIdStr = "";

      if (review.craftsmanId) {
        reviewCraftsmanIdStr = String(review.craftsmanId);
      } else if (review.craftsman) {
        // إذا كان الحرفي كائنًا، نستخدم معرفه
        if (typeof review.craftsman === "object" && review.craftsman !== null) {
          reviewCraftsmanIdStr = String(
            review.craftsman._id || review.craftsman.id
          );
        } else {
          // إذا كان الحرفي معرفًا مباشرًا
          reviewCraftsmanIdStr = String(review.craftsman);
        }
      }

      // إذا لم نتمكن من استخراج معرف الحرفي، نتخطى هذا التقييم
      if (!reviewCraftsmanIdStr) return false;

      // المقارنة المباشرة
      const exactMatch = reviewCraftsmanIdStr === craftsmanIdStr;

      // إذا كان المعرف طويلاً (معرف MongoDB)، نتحقق أيضًا من المطابقة الجزئية
      // على سبيل المثال، إذا كان المعرف الكامل هو "68177fde67a971b6d944c7ba" والمعرف المختصر هو "68177"
      let partialMatch = false;
      if (isMongoId && !exactMatch) {
        // التحقق مما إذا كان أحد المعرفين يحتوي على الآخر
        partialMatch =
          (reviewCraftsmanIdStr.length > craftsmanIdStr.length &&
            reviewCraftsmanIdStr.includes(craftsmanIdStr)) ||
          (craftsmanIdStr.length > reviewCraftsmanIdStr.length &&
            craftsmanIdStr.includes(reviewCraftsmanIdStr));
      }

      return exactMatch || partialMatch;
    });


    // ترتيب التقييمات من الأحدث إلى الأقدم
    const sortedReviews = [...craftsmanReviews].sort((a, b) => {
      // التأكد من أن التواريخ صالحة
      const dateA = new Date(a.createdAt || 0);
      const dateB = new Date(b.createdAt || 0);
      return dateB - dateA;
    });

   

    return sortedReviews;
  },

  // Obtener una evaluación específica por ID
  getReviewById: async (reviewId) => {
    const { reviews } = get();

    // التحقق من وجود معرف التقييم
    if (!reviewId) {
      console.warn("getReviewById - معرف التقييم غير موجود");
      return null;
    }

    // استخراج المعرف الفعلي إذا كان كائنًا
    let actualReviewId = reviewId;

    // إذا كان reviewId كائنًا، نحاول استخراج المعرف منه
    if (typeof reviewId === "object" && reviewId !== null) {
      actualReviewId = reviewId.id || reviewId._id;
     
    }

    // إذا لم نتمكن من استخراج معرف صالح، نعود
    if (!actualReviewId) {
      console.warn(
        "getReviewById - لم يتم العثور على معرف صالح في الكائن:",
        reviewId
      );
      return null;
    }

    // تحويل معرف التقييم إلى نص للمقارنة
    const reviewIdStr = String(actualReviewId);


    // البحث عن التقييم باستخدام المعرف
    const foundReview = reviews.find((review) => {
      const reviewId = review.id || review._id;
      return reviewId && String(reviewId) === reviewIdStr;
    });

    // طباعة نتيجة البحث
    if (foundReview) {
      return foundReview;
    } else {
      

      // محاولة تحميل التقييم من الخادم
      try {
       

        const review = await reviewService.getReviewById(reviewIdStr);

        if (review) {
          // تحويل التقييم إلى الصيغة المطلوبة
          const formattedReview = {
            ...review,
            id: review._id || review.id,
          };

          // إضافة التقييم إلى القائمة المحلية
          set((state) => ({
            reviews: [...state.reviews, formattedReview],
          }));

          return formattedReview;
        }

        // إذا لم يتم العثور على التقييم، إرجاع null
        return null;
      } catch (error) {
        console.error(
          "getReviewById - خطأ في محاولة تحميل التقييم من الخادم:",
          error
        );
        // إرجاع null في حالة الخطأ
        return null;
      }
    }
  },

  // جلب تقييمات الحرفي من الخادم مباشرة باستخدام معرف الحرفي
  getCraftsmanReviewsById: async (craftsmanId) => {
    if (!craftsmanId) return [];

    try {

      // استخدام API لجلب تقييمات الحرفي
      const response = await reviewService.getCraftsmanReviews(craftsmanId);

      if (response) {
        // التعامل مع الاستجابة سواء كانت كائن به خاصية data أو مصفوفة مباشرة
        const responseData = response.data ? response.data : response;

        if (Array.isArray(responseData)) {

          // إضافة التقييمات إلى المتجر
          const newReviews = responseData.map((review) => ({
            ...review,
            id: review._id || review.id,
          }));

          // تحديث المتجر بالتقييمات الجديدة
          set((state) => ({
            ...state,
            reviews: [
              ...state.reviews,
              ...newReviews.filter(
                (newReview) =>
                  !state.reviews.some(
                    (existingReview) =>
                      existingReview.id === newReview.id ||
                      existingReview._id === newReview._id
                  )
              ),
            ],
          }));

          // ترتيب التقييمات من الأحدث إلى الأقدم
          const sortedReviews = [...newReviews].sort((a, b) => {
            const dateA = new Date(a.createdAt || 0);
            const dateB = new Date(b.createdAt || 0);
            return dateB - dateA;
          });

          return sortedReviews;
        }
      }

      // إرجاع مصفوفة فارغة إذا لم يتم العثور على تقييمات
      return [];
    } catch (error) {
      console.error("خطأ في جلب تقييمات الحرفي من الخادم:", error);

      // إرجاع مصفوفة فارغة في حالة الخطأ
      return [];
    }
  },

  // تحديث تقييم موجود
  updateReview: async (reviewId, updatedData) => {
    set({ loading: true, error: null });

    try {
      const { reviews } = get();
      const reviewToUpdate = reviews.find(
        (review) => review.id === reviewId || review._id === reviewId
      );

      if (!reviewToUpdate) {
        console.error("لم يتم العثور على التقييم للتحديث:", reviewId);
        set({
          loading: false,
          error: "لم يتم العثور على التقييم للتحديث",
        });
        return null;
      }

      // التحقق من إمكانية تعديل التقييم (خلال 10 دقائق من إنشائه)
      const createdAt = new Date(reviewToUpdate.createdAt);
      const now = new Date();
      const diffInMinutes = (now - createdAt) / (1000 * 60);

      if (diffInMinutes > 10) {
        console.error("لا يمكن تعديل التقييم بعد مرور 10 دقائق من إنشائه");
        set({
          loading: false,
          error: "لا يمكن تعديل التقييم بعد مرور 10 دقائق من إنشائه",
        });
        return null;
      }

      // إرسال التحديث إلى الخادم
      const serverReview = await reviewService.updateReview(
        reviewId,
        updatedData
      );

      // تحديث التقييم محليًا
      const updatedReview = {
        ...reviewToUpdate,
        ...serverReview,
        ...updatedData,
        updatedAt: new Date().toISOString(),
      };

      // تحديث القائمة المحلية
      set((state) => ({
        reviews: state.reviews.map((review) =>
          review.id === reviewId || review._id === reviewId
            ? updatedReview
            : review
        ),
        loading: false,
      }));

      return updatedReview;
    } catch (error) {
      console.error("خطأ في تحديث التقييم:", error);

      set({
        loading: false,
        error: error.message || "حدث خطأ أثناء تحديث التقييم",
      });

      return null;
    }
  },

  // التحقق من إمكانية تعديل التقييم (خلال 10
  canEditReview: (reviewId) => {
    const { reviews } = get();
    const review = reviews.find((review) => review.id === reviewId);

    if (!review) {
      return false;
    }

    const createdAt = new Date(review.createdAt);
    const now = new Date();
    const diffInMinutes = (now - createdAt) / (1000 * 60);

    return diffInMinutes <= 10;
  },

  // Obtener la calificación promedio para un artesano
  getCraftsmanAverageRating: (craftsmanId) => {
    // استخدام معرف الحرفي كما هو (سلسلة نصية) دون تحويله
    const reviews = get().getCraftsmanReviews(craftsmanId);

    // طباعة معلومات التصحيح

    if (reviews.length === 0) return 0; // إرجاع 0 عندما لا توجد تقييمات


    // Calcular el promedio de la calificación general
    const sum = reviews.reduce(
      (acc, review) => acc + (review.overallRating || 0),
      0
    );

    const averageRating = (sum / reviews.length).toFixed(1);

   

    return parseFloat(averageRating);
  },

  // Obtener calificaciones detalladas para un artesano
  getCraftsmanDetailedRatings: (craftsmanId) => {
    // استخدام معرف الحرفي كما هو (سلسلة نصية) دون تحويله

    const reviews = get().getCraftsmanReviews(craftsmanId);

    if (reviews.length === 0) {
      // إرجاع قيم صفرية لجميع المعايير عندما لا توجد تقييمات
      return {
        quality: 0,
        punctuality: 0,
        price: 0,
        communication: 0,
        overall: 0,
      };
    }

    // Calcular promedios para cada criterio
    const quality =
      reviews.reduce((acc, review) => acc + review.qualityRating, 0) /
      reviews.length;
    const punctuality =
      reviews.reduce((acc, review) => acc + review.punctualityRating, 0) /
      reviews.length;
    const price =
      reviews.reduce((acc, review) => acc + review.priceRating, 0) /
      reviews.length;
    const communication =
      reviews.reduce((acc, review) => acc + review.communicationRating, 0) /
      reviews.length;
    const overall =
      reviews.reduce((acc, review) => acc + review.overallRating, 0) /
      reviews.length;

    return {
      quality: quality.toFixed(1),
      punctuality: punctuality.toFixed(1),
      price: price.toFixed(1),
      communication: communication.toFixed(1),
      overall: overall.toFixed(1),
    };
  },
}));

export default useReviewStore;
