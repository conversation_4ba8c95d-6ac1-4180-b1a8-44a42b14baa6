import React from "react";
import { Link } from "react-router-dom";
import { motion } from "framer-motion";
import { Calendar, FileText, Clock } from "lucide-react";
import Card from "../../../components/common/Card";
import Button from "../../../components/common/Button";

const RecentBookings = ({
  isLoadingBookings,
  userBookings,
  userType,
  darkMode,
  formatTime,
  onViewDetails,
}) => {
  // تحديد أيقونة الحالة ولونها
  const getStatusInfo = (status) => {
    if (status === "completed") {
      return {
        label: "مكتمل",
        bgClass: darkMode
          ? "bg-green-900/30 text-green-300 border border-green-800/50"
          : "bg-green-100 text-green-800 border border-green-200",
        icon: (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-4 w-4 ml-1"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M5 13l4 4L19 7"
            />
          </svg>
        ),
      };
    } else if (status === "accepted") {
      return {
        label: "تمت الموافقة عليه",
        bgClass: darkMode
          ? "bg-blue-900/30 text-blue-300 border border-blue-800/50"
          : "bg-blue-100 text-blue-800 border border-blue-200",
        icon: (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-4 w-4 ml-1"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 12l2 2 4-4"
            />
          </svg>
        ),
      };
    } else if (status === "pending") {
      return {
        label: "قيد الانتظار",
        bgClass: darkMode
          ? "bg-yellow-900/30 text-yellow-300 border border-yellow-800/50"
          : "bg-yellow-100 text-yellow-800 border border-yellow-200",
        icon: <Clock size={14} className="ml-1" />,
      };
    } else if (status === "rejected") {
      return {
        label: "مرفوض من الحرفي",
        bgClass: darkMode
          ? "bg-red-900/30 text-red-300 border border-red-800/50"
          : "bg-red-100 text-red-800 border border-red-200",
        icon: (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-4 w-4 ml-1"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        ),
      };
    } else if (status === "cancelled_expired") {
      return {
        label: "ملغى تلقائياً - الموعد قد انتهى",
        bgClass: darkMode
          ? "bg-orange-900/30 text-orange-300 border border-orange-800/50"
          : "bg-orange-100 text-orange-800 border border-orange-200",
        icon: (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-4 w-4 ml-1"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
        ),
      };
    } else {
      return {
        label: "ملغي من العميل",
        bgClass: darkMode
          ? "bg-gray-900/30 text-gray-300 border border-gray-800/50"
          : "bg-gray-100 text-gray-800 border border-gray-200",
        icon: (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-4 w-4 ml-1"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        ),
      };
    }
  };

  return (
    <section className="mb-10">
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center">
          <FileText
            size={24}
            className={`ml-2 ${
              darkMode ? "text-indigo-400" : "text-indigo-600"
            }`}
          />
          <h2
            className={`text-2xl font-bold ${
              darkMode ? "text-indigo-300" : "text-indigo-800"
            } relative inline-block transition-colors duration-300`}
          >
            <span className="relative z-10">طلباتي الأخيرة</span>
            <span
              className={`absolute bottom-0 left-0 right-0 h-2 ${
                darkMode ? "bg-indigo-500" : "bg-indigo-300"
              } opacity-40 transform -rotate-1 z-0`}
            ></span>
          </h2>
        </div>
        <Link
          to="/bookings"
          className={`${
            darkMode
              ? "text-indigo-400 hover:text-indigo-300"
              : "text-indigo-600 hover:text-indigo-800"
          } font-medium transition-colors duration-200 flex items-center`}
        >
          عرض الكل
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-4 w-4 mr-1"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M15 19l-7-7 7-7"
            />
          </svg>
        </Link>
      </div>

      {isLoadingBookings ? (
        <Card
          className={`p-8 text-center ${
            darkMode
              ? "bg-gray-800/90 text-gray-200 border border-gray-700"
              : "bg-white/90 border border-indigo-100/60"
          } shadow-lg transition-colors duration-300 w-full rounded-xl backdrop-blur-sm`}
        >
          <div className="flex flex-col justify-center items-center py-6">
            {/* Spinner animado mejorado */}
            <div className="relative w-16 h-16 mb-4">
              {/* Círculo exterior */}
              <div
                className={`absolute inset-0 rounded-full ${
                  darkMode
                    ? "border-2 border-indigo-700"
                    : "border-2 border-indigo-200"
                }`}
              ></div>

              {/* Spinner animado */}
              <div
                className={`absolute inset-0 rounded-full border-2 border-t-transparent border-l-transparent ${
                  darkMode
                    ? "border-r-indigo-400 border-b-indigo-500"
                    : "border-r-indigo-500 border-b-indigo-600"
                } animate-spin`}
              ></div>

              {/* Punto central */}
              <div
                className={`absolute inset-0 m-auto w-2 h-2 rounded-full ${
                  darkMode ? "bg-indigo-400" : "bg-indigo-500"
                }`}
              ></div>
            </div>

            <p
              className={`${
                darkMode ? "text-indigo-300" : "text-indigo-700"
              } font-medium text-lg transition-colors duration-300`}
            >
              جاري تحميل الطلبات...
            </p>
            <p
              className={`${
                darkMode ? "text-gray-400" : "text-gray-500"
              } text-sm mt-2 transition-colors duration-300 max-w-md`}
            >
              نحن نجمع معلومات طلباتك الأخيرة
            </p>
          </div>
        </Card>
      ) : userBookings && userBookings.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {userBookings.slice(0, 2).map((booking, index) => (
            <motion.div
              key={booking.id || booking._id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
            >
              <Card
                className={`overflow-hidden ${
                  darkMode
                    ? "bg-gray-800 text-gray-200 border border-gray-700"
                    : "bg-white/95 border border-indigo-100/60"
                } shadow-lg hover:shadow-xl transition-all duration-300 rounded-xl backdrop-blur-sm ${!darkMode &&
                  "hover:bg-gradient-to-b hover:from-white hover:to-indigo-50/30"}`}
              >
                {/* Header with status badge - Diseño mejorado */}
                <div
                  className={`px-5 py-4 flex justify-between items-center border-b ${
                    darkMode ? "border-gray-700/70" : "border-indigo-100/70"
                  } bg-gradient-to-r ${
                    darkMode
                      ? "from-gray-800 to-gray-800/80"
                      : "from-white to-indigo-50/30"
                  } rounded-t-xl`}
                >
                  <div className="flex items-center">
                    {/* Icono de usuario */}
                    <div
                      className={`w-10 h-10 rounded-full flex items-center justify-center mr-3 ${
                        darkMode ? "bg-indigo-900/30" : "bg-indigo-100"
                      } border ${
                        darkMode ? "border-indigo-800" : "border-indigo-200/70"
                      }`}
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className={`h-5 w-5  ${
                          darkMode ? "text-indigo-400" : "text-indigo-600"
                        }`}
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                        />
                      </svg>
                    </div>

                    <h3
                      className={`font-bold text-lg mr-2 mb-1  ${
                        darkMode ? "text-indigo-300" : "text-indigo-700"
                      } transition-colors duration-300`}
                    >
                      {userType === "client"
                        ? booking.craftsmanName ||
                          (booking.craftsman &&
                            booking.craftsman.user &&
                            booking.craftsman.user.name) ||
                          ""
                        : booking.clientName ||
                          (booking.client && booking.client.name) ||
                          ""}
                    </h3>
                  </div>

                  <div
                    className={`flex items-center px-3 py-1.5 rounded-full text-sm font-medium shadow-sm ${
                      getStatusInfo(booking.status).bgClass
                    }`}
                  >
                    {getStatusInfo(booking.status).icon}
                    {getStatusInfo(booking.status).label}
                  </div>
                </div>

                <div className="p-5">
                  {/* Profession info - Diseño mejorado */}
                  {userType === "client" && (
                    <div
                      className={`flex items-center mb-4  p-2 rounded-lg ${
                        darkMode ? "bg-gray-700/40" : "bg-indigo-50/60"
                      } border ${
                        darkMode ? "border-gray-600/50" : "border-indigo-100/50"
                      }`}
                    >
                      <div
                        className={`p-1.5 rounded-md ml-3 ${
                          darkMode ? "bg-indigo-900/50" : "bg-indigo-100/80"
                        }`}
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className={`h-5 w-5  ${
                            darkMode ? "text-indigo-300" : "text-indigo-600"
                          }`}
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                          />
                        </svg>
                      </div>
                      <div className="flex flex-col">
                        <span
                          className={`text-xs font-medium mb-0.5 ${
                            darkMode ? "text-gray-400" : "text-gray-500"
                          }`}
                        >
                          المهنة
                        </span>
                        <p
                          className={`${
                            darkMode ? "text-indigo-300" : "text-indigo-700"
                          } text-sm font-bold transition-colors duration-300`}
                        >
                          {booking.craftsmanProfession ||
                            (booking.craftsman?.professions &&
                              booking.craftsman.professions[0]) ||
                            ""}
                          {(booking.craftsmanSpecialization ||
                            (booking.craftsman?.specializations &&
                              booking.craftsman.specializations[0])) &&
                            ` - ${booking.craftsmanSpecialization ||
                              booking.craftsman.specializations[0]}`}
                        </p>
                      </div>
                    </div>
                  )}

                  {/* Date info - Diseño mejorado */}
                  <div
                    className={`flex items-center mb-4 p-2 rounded-lg ${
                      darkMode ? "bg-gray-700/40" : "bg-indigo-50/60"
                    } border ${
                      darkMode ? "border-gray-600/50" : "border-indigo-100/50"
                    }`}
                  >
                    <div
                      className={`p-1.5 rounded-md ml-3 ${
                        darkMode ? "bg-indigo-900/50" : "bg-indigo-100/80"
                      }`}
                    >
                      <Calendar
                        size={18}
                        className={`${
                          darkMode ? "text-indigo-300" : "text-indigo-600"
                        }`}
                      />
                    </div>
                    <div className="flex flex-col">
                      <span
                        className={`text-xs font-medium mb-0.5 ${
                          darkMode ? "text-gray-400" : "text-gray-500"
                        }`}
                      >
                        تاريخ انتهاء الطلب
                      </span>
                      <div className="flex items-center">
                        <p
                          className={`text-sm font-bold ${
                            darkMode ? "text-indigo-300" : "text-indigo-700"
                          } transition-colors duration-300`}
                        >
                          {booking.endDate
                            ? new Date(booking.endDate).toLocaleDateString(
                                "ar-SY"
                              )
                            : booking.date
                            ? new Date(booking.date).toLocaleDateString("ar-SY")
                            : "غير محدد"}
                          {/* طباعة معلومات التصحيح */}
                          {console.log("معلومات الطلب في RecentBookings:", {
                            bookingId: booking.id || booking._id,
                            date: booking.date,
                            endDate: booking.endDate,
                            hasEndDate: !!booking.endDate,
                            allKeys: Object.keys(booking),
                          })}
                        </p>
                        <span
                          className={`mx-2 text-xs ${
                            darkMode ? "text-gray-500" : "text-gray-400"
                          }`}
                        >
                          •
                        </span>
                        <p
                          className={`text-sm font-medium ${
                            darkMode ? "text-gray-300" : "text-gray-600"
                          } transition-colors duration-300 flex items-center`}
                        >
                          <Clock
                            size={14}
                            className={`ml-1 ${
                              darkMode ? "text-gray-400" : "text-gray-500"
                            }`}
                          />
                          {formatTime(booking.time)}
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Description - Diseño mejorado */}
                  <div className="mb-5">
                    <div
                      className={`flex items-start p-2 rounded-lg ${
                        darkMode ? "bg-gray-700/40" : "bg-indigo-50/60"
                      } border ${
                        darkMode ? "border-gray-600/50" : "border-indigo-100/50"
                      }`}
                    >
                      <div
                        className={`p-1.5 rounded-md ml-3 mt-1 ${
                          darkMode ? "bg-indigo-900/50" : "bg-indigo-100/80"
                        }`}
                      >
                        <FileText
                          size={16}
                          className={`flex-shrink-0 ${
                            darkMode ? "text-indigo-300" : "text-indigo-600"
                          }`}
                        />
                      </div>
                      <div className="flex flex-col flex-1">
                        <span
                          className={`text-xs font-medium mb-1 ${
                            darkMode ? "text-gray-400" : "text-gray-500"
                          }`}
                        >
                          وصف الطلب
                        </span>
                        <div
                          className={`p-2 rounded ${
                            darkMode ? "bg-gray-800/70" : "bg-white/80"
                          } max-h-20 overflow-y-auto`}
                        >
                          <p
                            className={`${
                              darkMode ? "text-gray-300" : "text-gray-700"
                            } break-words text-sm leading-relaxed`}
                          >
                            {booking.description || "لا يوجد وصف"}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Action button - Diseño mejorado */}
                  <Button
                    variant="secondary"
                    className={`w-full text-white flex items-center justify-center gap-2 shadow-lg hover:shadow-xl transition-all duration-300 relative overflow-hidden group py-3 px-4 rounded-lg ${
                      darkMode
                        ? "bg-gradient-to-r from-indigo-600 to-purple-700 hover:from-indigo-700 hover:to-purple-800"
                        : "bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700"
                    }`}
                    onClick={() => onViewDetails(booking)}
                  >
                    <span className="relative z-10 font-medium">
                      عرض التفاصيل
                    </span>

                    {/* Icono de flecha con animación mejorada */}
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-5 w-5 mr-1 relative z-10 transition-transform duration-300 group-hover:-translate-x-1"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M15 19l-7-7 7-7"
                      />
                    </svg>

                    {/* Efecto de brillo mejorado */}
                    <span className="absolute inset-0 bg-white opacity-20 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700"></span>
                  </Button>
                </div>
              </Card>
            </motion.div>
          ))}
        </div>
      ) : !isLoadingBookings && userBookings && userBookings.length === 0 ? (
        <Card
          className={`p-8 text-center ${
            darkMode
              ? "bg-gray-800/90 text-gray-200 border border-gray-700"
              : "bg-white/90 border border-indigo-100/60"
          } shadow-lg transition-colors duration-300 rounded-xl backdrop-blur-sm`}
        >
          {/* Ilustración mejorada */}
          <div className="flex justify-center mb-6">
            <div
              className={`w-24 h-24 rounded-full flex items-center justify-center ${
                darkMode ? "bg-gray-700" : "bg-indigo-50"
              } border ${darkMode ? "border-gray-600" : "border-indigo-100"}`}
            >
              <div className="relative">
                <FileText
                  size={36}
                  className={darkMode ? "text-indigo-400" : "text-indigo-500"}
                />
                {/* Efecto de resplandor */}
                <div className="absolute inset-0 blur-sm opacity-70">
                  <FileText
                    size={36}
                    className={darkMode ? "text-indigo-500" : "text-indigo-400"}
                  />
                </div>
              </div>
            </div>
          </div>

          <h3
            className={`text-xl font-bold mb-2 ${
              darkMode ? "text-indigo-300" : "text-indigo-700"
            }`}
          >
            ليس لديك أي طلبات حتى الآن
          </h3>

          <p
            className={`${
              darkMode ? "text-gray-400" : "text-gray-600"
            } mb-6 transition-colors duration-300 max-w-md mx-auto`}
          >
            يمكنك البحث عن حرفيين في منطقتك وتقديم طلبات جديدة
          </p>

          <Link to="/search" className="inline-block">
            <Button
              variant="primary"
              className={`text-white flex items-center justify-center gap-2 shadow-lg hover:shadow-xl transition-all duration-300 relative overflow-hidden group py-3 px-6 rounded-lg ${
                darkMode
                  ? "bg-gradient-to-r from-indigo-600 to-purple-700 hover:from-indigo-700 hover:to-purple-800"
                  : "bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700"
              }`}
            >
              <span className="relative z-10 font-medium">ابحث عن حرفي</span>

              {/* Icono de búsqueda */}
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5 mr-1 relative z-10 transition-transform duration-300 group-hover:-translate-x-1"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                />
              </svg>

              {/* Efecto de brillo */}
              <span className="absolute inset-0 bg-white opacity-20 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700"></span>
            </Button>
          </Link>
        </Card>
      ) : null}
    </section>
  );
};

export default RecentBookings;
