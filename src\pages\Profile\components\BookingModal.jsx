import React from "react";
import { Calendar, Clock, Info, X, CheckCircle } from "lucide-react";
import Button from "../../../components/common/Button";

const BookingModal = ({
  darkMode,
  bookingData,
  bookingErrors,
  onClose,
  onInputChange,
  onSubmit,
  isSubmitting = false,
}) => {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-60 backdrop-blur-sm flex items-center justify-center z-[100] p-4">
      <div
        className={`${
          darkMode ? "bg-gray-800/90" : "bg-white/95"
        } rounded-xl shadow-2xl w-full max-w-md transition-all duration-300 overflow-hidden`}
      >
        <div
          className={`p-6 rounded-lg ${
            darkMode
              ? "bg-gradient-to-br from-gray-800 to-gray-900 text-gray-200"
              : "bg-gradient-to-br from-blue-50 via-indigo-50 to-blue-50"
          } shadow-inner transition-all duration-300 border ${
            darkMode ? "border-indigo-800/30" : "border-indigo-200/70"
          }`}
        >
          <div className="flex justify-between items-center mb-6">
            <div className="flex items-center">
              <div
                className={`p-2 rounded-full mr-3 ${
                  darkMode
                    ? "bg-indigo-700/30 text-indigo-300"
                    : "bg-indigo-100 text-indigo-600"
                } transition-all duration-300`}
              >
                <Calendar size={22} strokeWidth={2} />
              </div>
              <h2
                className={`text-xl font-bold ${
                  darkMode ? "text-indigo-300" : "text-indigo-800"
                } relative inline-block transition-colors duration-300`}
              >
                <span className="relative z-10">حجز موعد</span>
                <span
                  className={`absolute bottom-0 left-0 right-0 h-2 ${
                    darkMode ? "bg-indigo-500" : "bg-indigo-300"
                  } opacity-40 transform -rotate-1 z-0`}
                ></span>
              </h2>
            </div>
            <button
              onClick={onClose}
              className={`p-2 rounded-full ${
                darkMode
                  ? "text-indigo-400 hover:text-indigo-300 hover:bg-indigo-800/50"
                  : "text-indigo-500 hover:text-indigo-700 hover:bg-indigo-100"
              } transition-all duration-200`}
            >
              <X size={20} strokeWidth={2} />
            </button>
          </div>

          <div
            className={`p-4 mb-6 rounded-lg ${
              darkMode
                ? "bg-gradient-to-r from-indigo-900/40 to-indigo-800/30 border border-indigo-800/50"
                : "bg-gradient-to-r from-blue-100/80 to-indigo-100/80 border border-indigo-200/50"
            } shadow-sm transition-all duration-300 hover:shadow-md group`}
          >
            <div className="flex items-start">
              <div
                className={`p-1 rounded-full mt-0.5 ml-2 ${
                  darkMode
                    ? "bg-indigo-700/30 text-indigo-300"
                    : "bg-indigo-100 text-indigo-600"
                } transition-all duration-300 group-hover:scale-110`}
              >
                <Info size={16} strokeWidth={2.5} />
              </div>
              <p
                className={`text-sm ${
                  darkMode ? "text-indigo-300" : "text-indigo-700"
                } leading-relaxed`}
              >
                <span className="font-bold">ملاحظة:</span> يمكنك تحديد نطاق من
                التواريخ والأوقات التي تناسبك. سيختار الحرفي الوقت المناسب ضمن
                هذا النطاق.
              </p>
            </div>
          </div>

          <form onSubmit={onSubmit}>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <label
                  className={`flex items-center ${
                    darkMode ? "text-indigo-300" : "text-indigo-800"
                  } font-medium mb-2 transition-colors duration-300`}
                >
                  <Calendar size={16} className="ml-1.5" />
                  تاريخ البداية <span className="text-red-500">*</span>
                </label>
                <div className="relative">
                  <input
                    type="date"
                    name="startDate"
                    value={bookingData.startDate}
                    onChange={onInputChange}
                    className={`input w-full py-2.5 px-3 ${
                      darkMode
                        ? "bg-gray-700/80 border-gray-600 text-gray-200 focus:border-indigo-400 focus:ring-indigo-400"
                        : "bg-white/90 border-indigo-200 focus:border-indigo-500 focus:ring-indigo-200"
                    } focus:ring focus:ring-opacity-50 rounded-lg shadow-sm hover:shadow transition-all duration-300 ${
                      bookingErrors.startDate
                        ? "border-red-500 focus:ring-red-500"
                        : ""
                    }`}
                    min={new Date().toISOString().split("T")[0]}
                    required
                  />
                </div>
                {bookingErrors.startDate && (
                  <p className="text-red-500 text-sm mt-1.5 flex items-center">
                    <span className="ml-1">⚠️</span>
                    {bookingErrors.startDate}
                  </p>
                )}
              </div>

              <div>
                <label
                  className={`flex items-center ${
                    darkMode ? "text-indigo-300" : "text-indigo-800"
                  } font-medium mb-2 transition-colors duration-300`}
                >
                  <Calendar size={16} className="ml-1.5" />
                  تاريخ النهاية <span className="text-red-500">*</span>
                </label>
                <div className="relative">
                  <input
                    type="date"
                    name="endDate"
                    value={bookingData.endDate}
                    onChange={onInputChange}
                    className={`input w-full py-2.5 px-3 ${
                      darkMode
                        ? "bg-gray-700/80 border-gray-600 text-gray-200 focus:border-indigo-400 focus:ring-indigo-400"
                        : "bg-white/90 border-indigo-200 focus:border-indigo-500 focus:ring-indigo-200"
                    } focus:ring focus:ring-opacity-50 rounded-lg shadow-sm hover:shadow transition-all duration-300 ${
                      bookingErrors.endDate
                        ? "border-red-500 focus:ring-red-500"
                        : ""
                    }`}
                    min={
                      bookingData.startDate ||
                      new Date().toISOString().split("T")[0]
                    }
                    required
                  />
                </div>
                {bookingErrors.endDate && (
                  <p className="text-red-500 text-sm mt-1.5 flex items-center">
                    <span className="ml-1">⚠️</span>
                    {bookingErrors.endDate}
                  </p>
                )}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <label
                  className={`flex items-center ${
                    darkMode ? "text-indigo-300" : "text-indigo-800"
                  } font-medium mb-2 transition-colors duration-300`}
                >
                  <Clock size={16} className="ml-1.5" />
                  وقت البداية <span className="text-red-500">*</span>
                </label>
                <div className="relative">
                  <input
                    type="time"
                    name="startTime"
                    value={bookingData.startTime}
                    onChange={onInputChange}
                    className={`input w-full py-2.5 px-3 ${
                      darkMode
                        ? "bg-gray-700/80 border-gray-600 text-gray-200 focus:border-indigo-400 focus:ring-indigo-400"
                        : "bg-white/90 border-indigo-200 focus:border-indigo-500 focus:ring-indigo-200"
                    } focus:ring focus:ring-opacity-50 rounded-lg shadow-sm hover:shadow transition-all duration-300 ${
                      bookingErrors.startTime
                        ? "border-red-500 focus:ring-red-500"
                        : ""
                    }`}
                    required
                  />
                </div>
                {bookingErrors.startTime && (
                  <p className="text-red-500 text-sm mt-1.5 flex items-center">
                    <span className="ml-1">⚠️</span>
                    {bookingErrors.startTime}
                  </p>
                )}
              </div>

              <div>
                <label
                  className={`flex items-center ${
                    darkMode ? "text-indigo-300" : "text-indigo-800"
                  } font-medium mb-2 transition-colors duration-300`}
                >
                  <Clock size={16} className="ml-1.5" />
                  وقت النهاية <span className="text-red-500">*</span>
                </label>
                <div className="relative">
                  <input
                    type="time"
                    name="endTime"
                    value={bookingData.endTime}
                    onChange={onInputChange}
                    className={`input w-full py-2.5 px-3 ${
                      darkMode
                        ? "bg-gray-700/80 border-gray-600 text-gray-200 focus:border-indigo-400 focus:ring-indigo-400"
                        : "bg-white/90 border-indigo-200 focus:border-indigo-500 focus:ring-indigo-200"
                    } focus:ring focus:ring-opacity-50 rounded-lg shadow-sm hover:shadow transition-all duration-300 ${
                      bookingErrors.endTime
                        ? "border-red-500 focus:ring-red-500"
                        : ""
                    }`}
                    required
                  />
                </div>
                {bookingErrors.endTime && (
                  <p className="text-red-500 text-sm mt-1.5 flex items-center">
                    <span className="ml-1">⚠️</span>
                    {bookingErrors.endTime}
                  </p>
                )}
              </div>
            </div>

            <div className="mb-6">
              <label
                className={`flex items-center ${
                  darkMode ? "text-indigo-300" : "text-indigo-800"
                } font-medium mb-2 transition-colors duration-300`}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="ml-1.5"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                  <polyline points="14 2 14 8 20 8"></polyline>
                  <line x1="16" y1="13" x2="8" y2="13"></line>
                  <line x1="16" y1="17" x2="8" y2="17"></line>
                  <polyline points="10 9 9 9 8 9"></polyline>
                </svg>
                وصف المشكلة <span className="text-red-500">*</span>
              </label>
              <div className="relative">
                <textarea
                  name="description"
                  value={bookingData.description}
                  onChange={onInputChange}
                  placeholder="اكتب وصفاً مختصراً للمشكلة أو الخدمة المطلوبة..."
                  className={`input min-h-[120px] w-full py-3 px-4 ${
                    darkMode
                      ? "bg-gray-700/80 border-gray-600 text-gray-200 focus:border-indigo-400 focus:ring-indigo-400"
                      : "bg-white/90 border-indigo-200 focus:border-indigo-500 focus:ring-indigo-200"
                  } focus:ring focus:ring-opacity-50 rounded-lg shadow-sm hover:shadow transition-all duration-300 ${
                    bookingErrors.description
                      ? "border-red-500 focus:ring-red-500"
                      : ""
                  }`}
                  required
                ></textarea>
              </div>
              {bookingErrors.description && (
                <p className="text-red-500 text-sm mt-1.5 flex items-center">
                  <span className="ml-1">⚠️</span>
                  {bookingErrors.description}
                </p>
              )}
            </div>

            <div className="flex justify-between mt-8">
              <Button
                type="submit"
                variant="primary"
                disabled={isSubmitting}
                className={`text-white flex items-center justify-center gap-2 shadow-lg hover:shadow-xl transition-all duration-300 relative overflow-hidden group py-3 px-6 rounded-lg ${
                  darkMode
                    ? "bg-gradient-to-r from-indigo-600 via-indigo-700 to-purple-700 hover:from-indigo-700 hover:via-indigo-800 hover:to-purple-800"
                    : "bg-gradient-to-r from-blue-500 via-indigo-500 to-indigo-600 hover:from-blue-600 hover:via-indigo-600 hover:to-indigo-700"
                } ${
                  isSubmitting ? "opacity-70 cursor-not-allowed" : ""
                } active:scale-[0.98]`}
              >
                <span className="relative z-10 flex items-center font-bold">
                  {isSubmitting ? (
                    <>
                      <svg
                        className="animate-spin -ml-1 mr-2 h-5 w-5 text-white"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                      >
                        <circle
                          className="opacity-25"
                          cx="12"
                          cy="12"
                          r="10"
                          stroke="currentColor"
                          strokeWidth="4"
                        ></circle>
                        <path
                          className="opacity-75"
                          fill="currentColor"
                          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                        ></path>
                      </svg>
                      جاري الحجز...
                    </>
                  ) : (
                    <>
                      <CheckCircle size={18} className="ml-1.5" />
                      تأكيد الحجز
                    </>
                  )}
                </span>
                <span className="absolute inset-0 bg-white opacity-20 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700"></span>
                <span className="absolute inset-0 opacity-0 group-hover:opacity-20 bg-gradient-to-r from-yellow-400 via-pink-500 to-indigo-500 transition-opacity duration-700"></span>
                <span className="absolute inset-0 opacity-0 group-hover:opacity-100 shadow-inner transition-opacity duration-300"></span>
              </Button>

              <Button
                type="button"
                variant="secondary"
                onClick={onClose}
                className={`text-white flex items-center justify-center gap-2 shadow-lg hover:shadow-xl transition-all duration-300 relative overflow-hidden group py-3 px-6 rounded-lg ${
                  darkMode
                    ? "bg-gradient-to-r from-red-600 via-red-700 to-red-800 hover:from-red-700 hover:via-red-800 hover:to-red-900"
                    : "bg-gradient-to-r from-red-500 via-red-600 to-red-700 hover:from-red-600 hover:via-red-700 hover:to-red-800"
                } active:scale-[0.98]`}
              >
                <span className="relative z-10 flex items-center font-bold">
                  <X size={18} className="ml-1.5" />
                  إلغاء
                </span>
                <span className="absolute inset-0 bg-white opacity-20 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700"></span>
                <span className="absolute inset-0 opacity-0 group-hover:opacity-10 bg-gradient-to-r from-yellow-400 via-pink-500 to-indigo-500 transition-opacity duration-700"></span>
                <span className="absolute inset-0 opacity-0 group-hover:opacity-100 shadow-inner transition-opacity duration-300"></span>
              </Button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default BookingModal;
