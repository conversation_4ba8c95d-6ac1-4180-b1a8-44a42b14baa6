import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import {
  Plus,
  Edit,
  Trash2,
  Search,
  Save,
  X,
  ChevronDown,
  ChevronUp,
  Settings,
  Briefcase,
  Filter,
} from "lucide-react";
import useThemeStore from "../../../store/themeStore";
import { professionService } from "../../../services/api";
import { professionsData } from "../../../data/professionsData";

const ProfessionsSection = () => {
  const darkMode = useThemeStore((state) => state.darkMode);
  const [professions, setProfessions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingProfession, setEditingProfession] = useState(null);
  const [expandedProfessions, setExpandedProfessions] = useState(new Set());

  // Pagination states
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // بيانات النموذج
  const [formData, setFormData] = useState({
    name: "",
    specializations: [],
    icon: "",
  });

  // تحميل المهن من الباك إند
  const loadProfessions = async () => {
    try {
      setLoading(true);
      const response = await professionService.getAllProfessions();
      setProfessions(response);
    } catch (error) {
      console.error("خطأ في تحميل المهن:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadProfessions();
  }, []);

  // تصفية المهن حسب البحث
  const filteredProfessions = professions.filter((profession) =>
    profession.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Pagination logic
  const totalItems = filteredProfessions.length;
  const totalPages = Math.ceil(totalItems / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentProfessions = filteredProfessions.slice(startIndex, endIndex);

  // Reset to first page when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm]);

  // إضافة/تعديل مهنة
  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      if (editingProfession) {
        // تعديل مهنة موجودة
        await professionService.updateProfession(
          editingProfession._id,
          formData
        );
      } else {
        // إضافة مهنة جديدة
        await professionService.createProfession(formData);
      }

      // إعادة تحميل المهن
      await loadProfessions();

      // إعادة تعيين النموذج
      setFormData({ name: "", specializations: [], icon: "" });
      setShowAddForm(false);
      setEditingProfession(null);
    } catch (error) {
      console.error("خطأ في حفظ المهنة:", error);
    }
  };

  // حذف مهنة
  const handleDelete = async (professionId) => {
    if (window.confirm("هل أنت متأكد من حذف هذه المهنة؟")) {
      try {
        await professionService.deleteProfession(professionId);
        await loadProfessions();
      } catch (error) {
        console.error("خطأ في حذف المهنة:", error);
      }
    }
  };

  // بدء تعديل مهنة
  const startEdit = (profession) => {
    setEditingProfession(profession);
    setFormData({
      name: profession.name,
      specializations: profession.specializations || [],
      icon: profession.icon || "",
    });
    setShowAddForm(true);
  };

  // إلغاء التعديل
  const cancelEdit = () => {
    setEditingProfession(null);
    setFormData({ name: "", specializations: [], icon: "" });
    setShowAddForm(false);
  };

  // توسيع/طي المهنة
  const toggleExpanded = (professionId) => {
    const newExpanded = new Set(expandedProfessions);
    if (newExpanded.has(professionId)) {
      newExpanded.delete(professionId);
    } else {
      newExpanded.add(professionId);
    }
    setExpandedProfessions(newExpanded);
  };

  // إضافة تخصص جديد
  const addSpecialization = () => {
    setFormData({
      ...formData,
      specializations: [...formData.specializations, ""],
    });
  };

  // تحديث تخصص
  const updateSpecialization = (index, value) => {
    const newSpecializations = [...formData.specializations];
    newSpecializations[index] = value;
    setFormData({ ...formData, specializations: newSpecializations });
  };

  // حذف تخصص
  const removeSpecialization = (index) => {
    const newSpecializations = formData.specializations.filter(
      (_, i) => i !== index
    );
    setFormData({ ...formData, specializations: newSpecializations });
  };

  // استيراد البيانات من الفرونت إند
  const importFromFrontend = async () => {
    if (
      window.confirm(
        "هل تريد استيراد جميع المهن من بيانات الفرونت إند؟ سيتم حذف البيانات الموجودة."
      )
    ) {
      try {
        setLoading(true);

        // حذف جميع المهن الموجودة
        for (const profession of professions) {
          await professionService.deleteProfession(profession._id);
        }

        // إضافة المهن من بيانات الفرونت إند
        for (const professionData of professionsData) {
          await professionService.createProfession({
            name: professionData.name,
            specializations: professionData.specializations,
            icon: "briefcase", // أيقونة افتراضية
          });
        }

        // إعادة تحميل المهن
        await loadProfessions();
        alert("تم استيراد البيانات بنجاح!");
      } catch (error) {
        console.error("خطأ في استيراد البيانات:", error);
        alert("حدث خطأ أثناء استيراد البيانات");
      } finally {
        setLoading(false);
      }
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* العنوان والأزرار */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div className="flex items-center gap-3">
          <div
            className={`p-2 rounded-lg ${
              darkMode ? "bg-indigo-900" : "bg-indigo-100"
            }`}
          >
            <Settings
              className={`w-6 h-6 ${
                darkMode ? "text-indigo-300" : "text-indigo-600"
              }`}
            />
          </div>
          <div>
            <h2
              className={`text-2xl font-bold ${
                darkMode ? "text-white" : "text-gray-900"
              }`}
            >
              إدارة المهن
            </h2>
            <p
              className={`text-sm ${
                darkMode ? "text-gray-400" : "text-gray-600"
              }`}
            >
              إدارة المهن والتخصصات المتاحة في النظام
            </p>
          </div>
        </div>

        <div className="flex gap-2">
          <button
            onClick={importFromFrontend}
            className={`px-4 py-2 rounded-lg font-medium transition-colors ${
              darkMode
                ? "bg-green-700 text-white hover:bg-green-600"
                : "bg-green-600 text-white hover:bg-green-700"
            }`}
          >
            استيراد من الفرونت إند
          </button>

          <button
            onClick={() => setShowAddForm(true)}
            className={`flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-colors ${
              darkMode
                ? "bg-indigo-700 text-white hover:bg-indigo-600"
                : "bg-indigo-600 text-white hover:bg-indigo-700"
            }`}
          >
            <Plus size={20} />
            إضافة مهنة
          </button>
        </div>
      </div>

      {/* شريط البحث */}
      <div className="relative">
        <Search
          className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400"
          size={20}
        />
        <input
          type="text"
          placeholder="البحث في المهن..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className={`w-full pr-10 pl-4 py-2 rounded-lg border ${
            darkMode
              ? "bg-gray-800 border-gray-700 text-white"
              : "bg-white border-gray-300 text-gray-900"
          } focus:ring-2 focus:ring-indigo-500 focus:border-transparent`}
        />
      </div>

      {/* إحصائيات */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div
          className={`p-4 rounded-lg ${
            darkMode ? "bg-gray-800" : "bg-white"
          } border ${darkMode ? "border-gray-700" : "border-gray-200"}`}
        >
          <div className="flex items-center gap-3">
            <Briefcase className="w-8 h-8 text-indigo-600" />
            <div>
              <p
                className={`text-2xl font-bold ${
                  darkMode ? "text-white" : "text-gray-900"
                }`}
              >
                {professions.length}
              </p>
              <p
                className={`text-sm ${
                  darkMode ? "text-gray-400" : "text-gray-600"
                }`}
              >
                إجمالي المهن
              </p>
            </div>
          </div>
        </div>

        <div
          className={`p-4 rounded-lg ${
            darkMode ? "bg-gray-800" : "bg-white"
          } border ${darkMode ? "border-gray-700" : "border-gray-200"}`}
        >
          <div className="flex items-center gap-3">
            <Settings className="w-8 h-8 text-green-600" />
            <div>
              <p
                className={`text-2xl font-bold ${
                  darkMode ? "text-white" : "text-gray-900"
                }`}
              >
                {professions.reduce(
                  (total, prof) => total + (prof.specializations?.length || 0),
                  0
                )}
              </p>
              <p
                className={`text-sm ${
                  darkMode ? "text-gray-400" : "text-gray-600"
                }`}
              >
                إجمالي التخصصات
              </p>
            </div>
          </div>
        </div>

        <div
          className={`p-4 rounded-lg ${
            darkMode ? "bg-gray-800" : "bg-white"
          } border ${darkMode ? "border-gray-700" : "border-gray-200"}`}
        >
          <div className="flex items-center gap-3">
            <Search className="w-8 h-8 text-blue-600" />
            <div>
              <p
                className={`text-2xl font-bold ${
                  darkMode ? "text-white" : "text-gray-900"
                }`}
              >
                {currentProfessions.length}
              </p>
              <p
                className={`text-sm ${
                  darkMode ? "text-gray-400" : "text-gray-600"
                }`}
              >
                معروض حالياً
              </p>
            </div>
          </div>
        </div>

        <div
          className={`p-4 rounded-lg ${
            darkMode ? "bg-gray-800" : "bg-white"
          } border ${darkMode ? "border-gray-700" : "border-gray-200"}`}
        >
          <div className="flex items-center gap-3">
            <Filter className="w-8 h-8 text-purple-600" />
            <div>
              <p
                className={`text-2xl font-bold ${
                  darkMode ? "text-white" : "text-gray-900"
                }`}
              >
                {filteredProfessions.length}
              </p>
              <p
                className={`text-sm ${
                  darkMode ? "text-gray-400" : "text-gray-600"
                }`}
              >
                نتائج البحث
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* نموذج إضافة/تعديل مهنة */}
      {showAddForm && (
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className={`p-6 rounded-lg border ${
            darkMode
              ? "bg-gray-800 border-gray-700"
              : "bg-white border-gray-200"
          }`}
        >
          <div className="flex justify-between items-center mb-4">
            <h3
              className={`text-lg font-semibold ${
                darkMode ? "text-white" : "text-gray-900"
              }`}
            >
              {editingProfession ? "تعديل المهنة" : "إضافة مهنة جديدة"}
            </h3>
            <button
              onClick={cancelEdit}
              className={`p-2 rounded-lg ${
                darkMode ? "hover:bg-gray-700" : "hover:bg-gray-100"
              }`}
            >
              <X size={20} />
            </button>
          </div>

          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label
                className={`block text-sm font-medium mb-2 ${
                  darkMode ? "text-gray-300" : "text-gray-700"
                }`}
              >
                اسم المهنة
              </label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) =>
                  setFormData({ ...formData, name: e.target.value })
                }
                required
                className={`w-full px-3 py-2 rounded-lg border ${
                  darkMode
                    ? "bg-gray-700 border-gray-600 text-white"
                    : "bg-white border-gray-300 text-gray-900"
                } focus:ring-2 focus:ring-indigo-500 focus:border-transparent`}
                placeholder="أدخل اسم المهنة"
              />
            </div>

            <div>
              <label
                className={`block text-sm font-medium mb-2 ${
                  darkMode ? "text-gray-300" : "text-gray-700"
                }`}
              >
                الأيقونة
              </label>
              <input
                type="text"
                value={formData.icon}
                onChange={(e) =>
                  setFormData({ ...formData, icon: e.target.value })
                }
                className={`w-full px-3 py-2 rounded-lg border ${
                  darkMode
                    ? "bg-gray-700 border-gray-600 text-white"
                    : "bg-white border-gray-300 text-gray-900"
                } focus:ring-2 focus:ring-indigo-500 focus:border-transparent`}
                placeholder="اسم الأيقونة (اختياري)"
              />
            </div>

            <div>
              <div className="flex justify-between items-center mb-2">
                <label
                  className={`block text-sm font-medium ${
                    darkMode ? "text-gray-300" : "text-gray-700"
                  }`}
                >
                  التخصصات
                </label>
                <button
                  type="button"
                  onClick={addSpecialization}
                  className={`flex items-center gap-1 px-3 py-1 rounded-lg text-sm ${
                    darkMode
                      ? "bg-indigo-700 text-white hover:bg-indigo-600"
                      : "bg-indigo-600 text-white hover:bg-indigo-700"
                  }`}
                >
                  <Plus size={16} />
                  إضافة تخصص
                </button>
              </div>

              <div className="space-y-2">
                {formData.specializations.map((spec, index) => (
                  <div key={index} className="flex gap-2">
                    <input
                      type="text"
                      value={spec}
                      onChange={(e) =>
                        updateSpecialization(index, e.target.value)
                      }
                      className={`flex-1 px-3 py-2 rounded-lg border ${
                        darkMode
                          ? "bg-gray-700 border-gray-600 text-white"
                          : "bg-white border-gray-300 text-gray-900"
                      } focus:ring-2 focus:ring-indigo-500 focus:border-transparent`}
                      placeholder="اسم التخصص"
                    />
                    <button
                      type="button"
                      onClick={() => removeSpecialization(index)}
                      className={`p-2 rounded-lg ${
                        darkMode
                          ? "bg-red-700 text-white hover:bg-red-600"
                          : "bg-red-600 text-white hover:bg-red-700"
                      }`}
                    >
                      <Trash2 size={16} />
                    </button>
                  </div>
                ))}
              </div>
            </div>

            <div className="flex gap-2 pt-4">
              <button
                type="submit"
                className={`flex items-center gap-2 px-4 py-2 rounded-lg font-medium ${
                  darkMode
                    ? "bg-indigo-700 text-white hover:bg-indigo-600"
                    : "bg-indigo-600 text-white hover:bg-indigo-700"
                }`}
              >
                <Save size={20} />
                {editingProfession ? "حفظ التعديلات" : "إضافة المهنة"}
              </button>
              <button
                type="button"
                onClick={cancelEdit}
                className={`px-4 py-2 rounded-lg font-medium ${
                  darkMode
                    ? "bg-gray-700 text-gray-300 hover:bg-gray-600"
                    : "bg-gray-200 text-gray-700 hover:bg-gray-300"
                }`}
              >
                إلغاء
              </button>
            </div>
          </form>
        </motion.div>
      )}

      {/* قائمة المهن */}
      <div className="space-y-4">
        {filteredProfessions.length === 0 ? (
          <div
            className={`text-center py-8 ${
              darkMode ? "text-gray-400" : "text-gray-600"
            }`}
          >
            {searchTerm ? "لا توجد مهن تطابق البحث" : "لا توجد مهن مضافة بعد"}
          </div>
        ) : (
          currentProfessions.map((profession) => (
            <motion.div
              key={profession._id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className={`border rounded-lg ${
                darkMode
                  ? "bg-gray-800 border-gray-700"
                  : "bg-white border-gray-200"
              }`}
            >
              <div className="p-4">
                <div className="flex justify-between items-center">
                  <div className="flex items-center gap-3">
                    <button
                      onClick={() => toggleExpanded(profession._id)}
                      className={`p-2 rounded-lg ${
                        darkMode ? "hover:bg-gray-700" : "hover:bg-gray-100"
                      }`}
                    >
                      {expandedProfessions.has(profession._id) ? (
                        <ChevronUp size={20} />
                      ) : (
                        <ChevronDown size={20} />
                      )}
                    </button>
                    <div>
                      <h3
                        className={`text-lg font-semibold ${
                          darkMode ? "text-white" : "text-gray-900"
                        }`}
                      >
                        {profession.name}
                      </h3>
                      <p
                        className={`text-sm ${
                          darkMode ? "text-gray-400" : "text-gray-600"
                        }`}
                      >
                        {profession.specializations?.length || 0} تخصص
                      </p>
                    </div>
                  </div>

                  <div className="flex gap-2">
                    <button
                      onClick={() => startEdit(profession)}
                      className={`p-2 rounded-lg ${
                        darkMode
                          ? "bg-blue-700 text-white hover:bg-blue-600"
                          : "bg-blue-600 text-white hover:bg-blue-700"
                      }`}
                    >
                      <Edit size={16} />
                    </button>
                    <button
                      onClick={() => handleDelete(profession._id)}
                      className={`p-2 rounded-lg ${
                        darkMode
                          ? "bg-red-700 text-white hover:bg-red-600"
                          : "bg-red-600 text-white hover:bg-red-700"
                      }`}
                    >
                      <Trash2 size={16} />
                    </button>
                  </div>
                </div>

                {/* التخصصات */}
                {expandedProfessions.has(profession._id) && (
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: "auto" }}
                    className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700"
                  >
                    <h4
                      className={`text-sm font-medium mb-2 ${
                        darkMode ? "text-gray-300" : "text-gray-700"
                      }`}
                    >
                      التخصصات:
                    </h4>
                    {profession.specializations &&
                    profession.specializations.length > 0 ? (
                      <div className="flex flex-wrap gap-2">
                        {profession.specializations.map((spec, index) => (
                          <span
                            key={index}
                            className={`px-3 py-1 rounded-full text-sm ${
                              darkMode
                                ? "bg-indigo-900 text-indigo-200"
                                : "bg-indigo-100 text-indigo-800"
                            }`}
                          >
                            {spec}
                          </span>
                        ))}
                      </div>
                    ) : (
                      <p
                        className={`text-sm ${
                          darkMode ? "text-gray-400" : "text-gray-600"
                        }`}
                      >
                        لا توجد تخصصات مضافة
                      </p>
                    )}
                  </motion.div>
                )}
              </div>
            </motion.div>
          ))
        )}
      </div>

      {/* Pagination Controls */}
      {!loading && filteredProfessions.length > 0 && (
        <div
          className={`mt-6 flex flex-col sm:flex-row justify-between items-center gap-4 p-4 rounded-lg ${
            darkMode ? "bg-gray-700" : "bg-white"
          } border ${darkMode ? "border-gray-600" : "border-gray-200"}`}
        >
          {/* Items per page selector */}
          <div className="flex items-center gap-2">
            <span
              className={`text-sm ${
                darkMode ? "text-gray-300" : "text-gray-600"
              }`}
            >
              عرض
            </span>
            <select
              value={itemsPerPage}
              onChange={(e) => {
                setItemsPerPage(Number(e.target.value));
                setCurrentPage(1);
              }}
              className={`px-3 py-1 rounded border ${
                darkMode
                  ? "bg-gray-800 border-gray-600 text-white"
                  : "bg-white border-gray-300 text-gray-900"
              } focus:ring-2 focus:ring-indigo-500 focus:border-transparent`}
            >
              <option value={5}>5</option>
              <option value={10}>10</option>
              <option value={20}>20</option>
              <option value={50}>50</option>
            </select>
            <span
              className={`text-sm ${
                darkMode ? "text-gray-300" : "text-gray-600"
              }`}
            >
              من {totalItems} {totalItems === 1 ? "مهنة" : "مهنة"}
            </span>
          </div>

          {/* Page info */}
          <div
            className={`text-sm ${
              darkMode ? "text-gray-300" : "text-gray-600"
            }`}
          >
            صفحة {currentPage} من {totalPages}
          </div>

          {/* Pagination buttons */}
          <div className="flex items-center gap-1">
            <button
              onClick={() => setCurrentPage(1)}
              disabled={currentPage === 1}
              className={`px-3 py-1 rounded ${
                currentPage === 1
                  ? "bg-gray-200 text-gray-400 cursor-not-allowed"
                  : darkMode
                  ? "bg-gray-600 text-white hover:bg-gray-500"
                  : "bg-gray-100 text-gray-700 hover:bg-gray-200"
              } transition-colors`}
            >
              الأولى
            </button>

            <button
              onClick={() => setCurrentPage(currentPage - 1)}
              disabled={currentPage === 1}
              className={`px-3 py-1 rounded ${
                currentPage === 1
                  ? "bg-gray-200 text-gray-400 cursor-not-allowed"
                  : darkMode
                  ? "bg-gray-600 text-white hover:bg-gray-500"
                  : "bg-gray-100 text-gray-700 hover:bg-gray-200"
              } transition-colors`}
            >
              السابق
            </button>

            {/* Page numbers */}
            {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
              let pageNum;
              if (totalPages <= 5) {
                pageNum = i + 1;
              } else if (currentPage <= 3) {
                pageNum = i + 1;
              } else if (currentPage >= totalPages - 2) {
                pageNum = totalPages - 4 + i;
              } else {
                pageNum = currentPage - 2 + i;
              }

              return (
                <button
                  key={pageNum}
                  onClick={() => setCurrentPage(pageNum)}
                  className={`px-3 py-1 rounded ${
                    currentPage === pageNum
                      ? "bg-indigo-600 text-white"
                      : darkMode
                      ? "bg-gray-600 text-white hover:bg-gray-500"
                      : "bg-gray-100 text-gray-700 hover:bg-gray-200"
                  } transition-colors`}
                >
                  {pageNum}
                </button>
              );
            })}

            <button
              onClick={() => setCurrentPage(currentPage + 1)}
              disabled={currentPage === totalPages}
              className={`px-3 py-1 rounded ${
                currentPage === totalPages
                  ? "bg-gray-200 text-gray-400 cursor-not-allowed"
                  : darkMode
                  ? "bg-gray-600 text-white hover:bg-gray-500"
                  : "bg-gray-100 text-gray-700 hover:bg-gray-200"
              } transition-colors`}
            >
              التالي
            </button>

            <button
              onClick={() => setCurrentPage(totalPages)}
              disabled={currentPage === totalPages}
              className={`px-3 py-1 rounded ${
                currentPage === totalPages
                  ? "bg-gray-200 text-gray-400 cursor-not-allowed"
                  : darkMode
                  ? "bg-gray-600 text-white hover:bg-gray-500"
                  : "bg-gray-100 text-gray-700 hover:bg-gray-200"
              } transition-colors`}
            >
              الأخيرة
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default ProfessionsSection;
