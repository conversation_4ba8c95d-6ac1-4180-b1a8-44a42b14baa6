import React from "react";
import { FileText, Award, Clock, CheckCircle } from "lucide-react";
import Card from "../../../components/common/Card";

const CraftsmanBio = ({ craftsman, darkMode }) => {
  // قائمة الخصائص المتاحة
  const availableFeatures = [
    { id: "highExperience", name: "خبرة مهنية عالية", icon: Award },
    { id: "punctual", name: "التزام بالمواعيد", icon: Clock },
    { id: "qualityWork", name: "جودة عمل مضمونة", icon: CheckCircle },
    { id: "fastService", name: "خدمة سريعة", icon: Clock },
    { id: "fairPrices", name: "أسعار عادلة", icon: Award },
    { id: "cleanWork", name: "نظافة في العمل", icon: CheckCircle },
  ];

  // طباعة بيانات الحرفي للتصحيح
  console.log("بيانات الحرفي في CraftsmanBio:", {
    id: craftsman.id || craftsman._id,
    features: craftsman.features,
    featuresType: craftsman.features ? typeof craftsman.features : "undefined",
    featuresIsArray: craftsman.features
      ? Array.isArray(craftsman.features)
      : false,
    featuresLength: craftsman.features ? craftsman.features.length : 0,
    craftsmanKeys: Object.keys(craftsman),
  });

  // تأكد من أن الخصائص مصفوفة
  if (!craftsman.features) {
    console.log("الخصائص غير موجودة، إنشاء مصفوفة فارغة");
    craftsman.features = [];
  } else if (!Array.isArray(craftsman.features)) {
    console.log("الخصائص موجودة ولكنها ليست مصفوفة، تحويلها إلى مصفوفة");
    craftsman.features = [craftsman.features];
  }

  // إزالة التكرارات من الخصائص
  if (Array.isArray(craftsman.features) && craftsman.features.length > 0) {
    craftsman.features = [...new Set(craftsman.features)];
    console.log("تم إزالة التكرارات من الخصائص:", craftsman.features);
  }

  // طباعة الخصائص بعد المعالجة
  console.log("الخصائص بعد المعالجة:", {
    features: craftsman.features,
    featuresType: typeof craftsman.features,
    featuresIsArray: Array.isArray(craftsman.features),
    featuresLength: craftsman.features.length,
  });
  return (
    <Card
      className={`p-6 mb-6 rounded-xl ${
        darkMode
          ? "bg-gray-800 text-gray-200 border border-gray-700"
          : "bg-gradient-to-br from-blue-50/80 to-indigo-50 border border-indigo-100"
      } shadow-lg transition-colors duration-300 hover:shadow-xl`}
    >
      {/* هيدر القسم مع تأثيرات متحركة */}
      <div className="flex items-center mb-6 relative">
        <div
          className={`p-3 rounded-full mr-3 ${
            darkMode ? "bg-indigo-900/50" : "bg-indigo-100"
          } transform transition-all duration-300 hover:scale-110 hover:rotate-3`}
        >
          <FileText
            size={24}
            className={`${
              darkMode ? "text-indigo-400" : "text-indigo-500"
            } transition-colors duration-300`}
          />
        </div>
        <div className="relative">
          <h2
            className={`text-xl font-bold ${
              darkMode ? "text-indigo-300" : "text-indigo-800"
            } relative inline-block transition-colors duration-300`}
          >
            <span className="relative z-10">نبذة عن الحرفي</span>
            <span
              className={`absolute bottom-0 left-0 right-0 h-2 ${
                darkMode ? "bg-indigo-500" : "bg-indigo-300"
              } opacity-40 transform -rotate-1 z-0 rounded-full`}
            ></span>
          </h2>
          <p
            className={`text-sm mt-1 ${
              darkMode ? "text-gray-400" : "text-gray-500"
            }`}
          >
            تعرف على خبرات ومهارات الحرفي
          </p>
        </div>

        {/* زخرفة جانبية */}
        <div className="absolute top-0 left-0 w-20 h-1 bg-gradient-to-r from-transparent via-indigo-500 to-transparent opacity-30"></div>
      </div>

      <div
        className={`p-5 rounded-xl ${
          darkMode
            ? "bg-gray-700/50 border border-gray-600"
            : "bg-gradient-to-r from-blue-100/80 to-indigo-100/80 border border-indigo-100"
        } transition-all duration-300 hover:shadow-md relative overflow-hidden`}
      >
        {/* زخرفة خلفية */}
        <div className="absolute top-0 right-0 w-40 h-40 bg-indigo-500/5 rounded-full -translate-y-20 translate-x-20"></div>
        <div className="absolute bottom-0 left-0 w-40 h-40 bg-indigo-500/5 rounded-full translate-y-20 -translate-x-20"></div>

        {/* محتوى النبذة */}
        <div className="relative z-10">
          <p
            className={`${
              darkMode ? "text-gray-300" : "text-indigo-700"
            } leading-relaxed transition-colors duration-300 text-lg`}
          >
            {craftsman.bio ||
              "خبرة 15 عام في مجال التمديدات الكهربائية المنزلية والصناعية. متخصص في تركيب وصيانة الأنظمة الكهربائية وحل المشاكل بسرعة واحترافية. حاصل على شهادات معتمدة في السلامة الكهربائية."}
          </p>

          {/* نقاط القوة - عرض الخصائص المختارة فقط */}
          {craftsman.features && craftsman.features.length > 0 && (
            <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
              {craftsman.features.map((featureIdOrName, index) => {
                // البحث عن الخاصية إما بالمعرف أو بالاسم
                let feature = availableFeatures.find(
                  (f) => f.id === featureIdOrName
                );

                // إذا لم يتم العثور على الخاصية بالمعرف، نبحث عنها بالاسم
                if (!feature) {
                  feature = availableFeatures.find(
                    (f) => f.name === featureIdOrName
                  );
                }

                // إذا لم يتم العثور على الخاصية، نستخدم قيمة افتراضية
                if (!feature) {
                  // تحديد أيقونة افتراضية بناءً على الفهرس
                  const defaultIcons = [Award, Clock, CheckCircle];
                  const DefaultIcon = defaultIcons[index % defaultIcons.length];

                  return (
                    <div
                      key={`feature-${index}`}
                      className={`flex items-center p-3 rounded-lg ${
                        darkMode ? "bg-gray-800/70" : "bg-white/70"
                      } border ${
                        darkMode ? "border-gray-700" : "border-indigo-100"
                      }`}
                    >
                      <DefaultIcon
                        size={20}
                        className={`ml-2 ${
                          darkMode ? "text-indigo-400" : "text-indigo-500"
                        }`}
                      />
                      <span
                        className={`text-sm font-medium ${
                          darkMode ? "text-indigo-300" : "text-indigo-700"
                        }`}
                      >
                        {featureIdOrName}
                      </span>
                    </div>
                  );
                }

                const Icon = feature.icon;
                return (
                  <div
                    key={`feature-${index}`}
                    className={`flex items-center p-3 rounded-lg ${
                      darkMode ? "bg-gray-800/70" : "bg-white/70"
                    } border ${
                      darkMode ? "border-gray-700" : "border-indigo-100"
                    }`}
                  >
                    <Icon
                      size={20}
                      className={`ml-2 ${
                        darkMode ? "text-indigo-400" : "text-indigo-500"
                      }`}
                    />
                    <span
                      className={`text-sm font-medium ${
                        darkMode ? "text-indigo-300" : "text-indigo-700"
                      }`}
                    >
                      {feature.name}
                    </span>
                  </div>
                );
              })}
            </div>
          )}
        </div>
      </div>
    </Card>
  );
};

export default CraftsmanBio;
