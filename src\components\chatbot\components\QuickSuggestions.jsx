import React from "react";
import { MessageCircle } from "lucide-react";

/**
 * مكون الاقتراحات السريعة
 * @param {Object} props - خصائص المكون
 * @param {Array<string>} props.suggestions - قائمة الاقتراحات
 * @param {Function} props.onSelect - دالة تنفذ عند اختيار اقتراح
 * @param {boolean} props.darkMode - وضع الألوان الداكنة
 */
const QuickSuggestions = ({ suggestions, onSelect, darkMode }) => {
  return (
    <div className="flex flex-wrap gap-2 my-3 justify-center">
      {suggestions.map((suggestion, index) => (
        <button
          key={index}
          onClick={() => onSelect(suggestion)}
          className={`px-3 py-1.5 text-sm rounded-full ${
            darkMode
              ? "bg-gray-700 text-white hover:bg-gray-600"
              : "bg-indigo-100 text-indigo-700 hover:bg-indigo-200"
          } transition-colors duration-200 text-right shadow-sm hover:shadow-md border ${
            darkMode ? "border-gray-600" : "border-indigo-200"
          } cursor-pointer flex items-center gap-1`}
        >
          <MessageCircle
            size={14}
            className={darkMode ? "text-gray-300" : "text-indigo-500"}
          />
          <span>{suggestion}</span>
        </button>
      ))}
    </div>
  );
};

export default QuickSuggestions;
