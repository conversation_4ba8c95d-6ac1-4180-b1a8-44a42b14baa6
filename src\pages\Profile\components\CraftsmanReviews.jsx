import React from "react";
import { MessageSquare } from "lucide-react";
import Card from "../../../components/common/Card";
import DetailedReviewsList from "../../../components/reviews/DetailedReviewsList";

const CraftsmanReviews = ({ reviews, darkMode }) => {
  return (
    <Card
      className={`p-6 rounded-xl ${
        darkMode
          ? "bg-gray-800 text-gray-200 border border-gray-700"
          : "bg-gradient-to-br from-blue-50/80 to-indigo-50 border border-indigo-100"
      } shadow-lg transition-colors duration-300`}
    >
      <div className="flex items-center mb-5">
        <div
          className={`p-2 rounded-full mr-3 ${
            darkMode ? "bg-indigo-900/50" : "bg-indigo-100"
          }`}
        >
          <MessageSquare
            size={22}
            className={`${
              darkMode ? "text-indigo-400" : "text-indigo-500"
            } transition-colors duration-300`}
          />
        </div>
        <h2
          className={`text-xl font-bold ${
            darkMode ? "text-indigo-300" : "text-indigo-800"
          } relative inline-block transition-colors duration-300`}
        >
          <span className="relative z-10">التقييمات</span>
          <span
            className={`absolute bottom-0 left-0 right-0 h-2 ${
              darkMode ? "bg-indigo-500" : "bg-indigo-300"
            } opacity-40 transform -rotate-1 z-0`}
          ></span>
        </h2>
      </div>

      {reviews && reviews.length > 0 ? (
        <div
          className={`rounded-lg overflow-hidden ${
            darkMode
              ? "bg-gray-700/50 border border-gray-600"
              : "bg-gradient-to-r from-blue-100/80 to-indigo-100/80 border border-indigo-100"
          } transition-colors duration-300`}
        >
          <DetailedReviewsList reviews={reviews} darkMode={darkMode} />
        </div>
      ) : (
        <div
          className={`flex flex-col items-center justify-center p-8 rounded-lg ${
            darkMode
              ? "bg-gray-700/50 border border-gray-600"
              : "bg-gradient-to-r from-blue-100/80 to-indigo-100/80 border border-indigo-100"
          } transition-colors duration-300`}
        >
          <div
            className={`w-16 h-16 rounded-full flex items-center justify-center mb-4 ${
              darkMode ? "bg-gray-600" : "bg-indigo-100"
            }`}
          >
            <MessageSquare
              size={32}
              className={`${
                darkMode ? "text-indigo-400" : "text-indigo-500"
              } opacity-70`}
            />
          </div>
          <p
            className={`text-center font-medium ${
              darkMode ? "text-indigo-300" : "text-indigo-700"
            } transition-colors duration-300 mb-2`}
          >
            لا توجد تقييمات بعد
          </p>
          <p
            className={`text-center text-sm ${
              darkMode ? "text-gray-400" : "text-gray-500"
            }`}
          >
            سيظهر هنا تقييمات العملاء بمجرد إضافتها
          </p>
        </div>
      )}
    </Card>
  );
};

export default CraftsmanReviews;
