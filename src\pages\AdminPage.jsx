import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import AdminLogin from './Admin/AdminLogin';
import useAdminStore from '../store/adminStore';

const AdminPage = () => {
  const navigate = useNavigate();
  const { isAuthenticated, admin } = useAdminStore();

  useEffect(() => {
    // إذا كان الأدمن مسجل دخوله بالفعل، انتقل للداشبورد
    if (isAuthenticated && admin) {
      navigate('/admin/dashboard', { replace: true });
    }
  }, [isAuthenticated, admin, navigate]);

  // إذا كان مسجل دخوله، لا نعرض شيء (سيتم التوجيه)
  if (isAuthenticated && admin) {
    return null;
  }

  // عرض صفحة تسجيل الدخول
  return <AdminLogin />;
};

export default AdminPage;
