import { useState, useEffect } from 'react';
import React from "react";
import { motion } from 'framer-motion';
import useThemeStore from '../../store/themeStore';
import adminSecurity from '../../utils/adminSecurity';
import { Clock, Shield, AlertTriangle } from 'lucide-react';

const AdminLockoutTimer = () => {
  const darkMode = useThemeStore((state) => state.darkMode);
  const [lockStatus, setLockStatus] = useState({ locked: false, timeRemaining: 0 });
  const [statusMessage, setStatusMessage] = useState(null);

  useEffect(() => {
    // التحقق من الحالة الأولية
    const initialStatus = adminSecurity.isLocked();
    setLockStatus(initialStatus);
    if (initialStatus.locked) {
      setStatusMessage(adminSecurity.getStatusMessage(initialStatus));
    }

    // إضافة مستمع للتحديثات
    const handleStatusUpdate = (newStatus) => {
      setLockStatus(newStatus);
      if (newStatus.locked) {
        setStatusMessage(adminSecurity.getStatusMessage(newStatus));
      } else {
        setStatusMessage(null);
      }
    };

    adminSecurity.addListener(handleStatusUpdate);

    // مؤقت لتحديث العداد كل ثانية
    const interval = setInterval(() => {
      const currentStatus = adminSecurity.isLocked();
      if (currentStatus.locked) {
        setLockStatus(currentStatus);
        setStatusMessage(adminSecurity.getStatusMessage(currentStatus));
      } else if (lockStatus.locked) {
        // تم انتهاء القفل
        setLockStatus(currentStatus);
        setStatusMessage(null);
      }
    }, 1000);

    // تنظيف عند إلغاء المكون
    return () => {
      adminSecurity.removeListener(handleStatusUpdate);
      clearInterval(interval);
    };
  }, [lockStatus.locked]);

  // إذا لم يكن مقفل، لا نعرض شيء
  if (!lockStatus.locked || !statusMessage) {
    return null;
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: -20, scale: 0.9 }}
      animate={{ opacity: 1, y: 0, scale: 1 }}
      exit={{ opacity: 0, y: -20, scale: 0.9 }}
      transition={{ duration: 0.5, ease: "easeOut" }}
      className={`w-full p-6 rounded-lg border-2 ${
        darkMode
          ? "bg-red-900/20 border-red-700 text-red-300"
          : "bg-red-50 border-red-300 text-red-700"
      } shadow-lg backdrop-blur-sm`}
    >
      {/* أيقونة وعنوان */}
      <div className="flex items-center justify-center mb-4">
        <motion.div
          animate={{ rotate: [0, 10, -10, 0] }}
          transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
          className="mr-3"
        >
          <Shield className="w-8 h-8" />
        </motion.div>
        <h3 className="text-xl font-bold text-center">
          {statusMessage.title}
        </h3>
      </div>

      {/* الرسالة */}
      <p className="text-center mb-6 leading-relaxed">
        {statusMessage.message}
      </p>

      {/* العداد الكبير */}
      <div className="text-center mb-6">
        <motion.div
          key={statusMessage.timeFormatted}
          initial={{ scale: 1.1 }}
          animate={{ scale: 1 }}
          transition={{ duration: 0.3 }}
          className={`inline-flex items-center justify-center px-8 py-4 rounded-lg ${
            darkMode
              ? "bg-red-800/30 border border-red-600"
              : "bg-red-100 border border-red-300"
          } shadow-inner`}
        >
          <Clock className="w-6 h-6 mr-3" />
          <span className="text-3xl font-mono font-bold">
            {statusMessage.timeFormatted}
          </span>
        </motion.div>
      </div>

      {/* شريط التقدم */}
      <div className="mb-6">
        <div
          className={`w-full h-3 rounded-full ${
            darkMode ? "bg-red-900/50" : "bg-red-200"
          } overflow-hidden`}
        >
          <motion.div
            className={`h-full ${
              darkMode
                ? "bg-gradient-to-r from-red-600 to-red-500"
                : "bg-gradient-to-r from-red-500 to-red-400"
            } rounded-full`}
            initial={{ width: "100%" }}
            animate={{
              width: `${(statusMessage.timeRemaining / (60 * 60)) * 100}%`
            }}
            transition={{ duration: 1, ease: "easeOut" }}
          />
        </div>
        <div className="flex justify-between text-sm mt-2 opacity-75">
          <span>0:00</span>
          <span>1:00:00</span>
        </div>
      </div>

      {/* معلومات إضافية */}
      <div
        className={`p-4 rounded-lg ${
          darkMode
            ? "bg-yellow-900/20 border border-yellow-700 text-yellow-300"
            : "bg-yellow-50 border border-yellow-300 text-yellow-700"
        }`}
      >
        <div className="flex items-start">
          <AlertTriangle className="w-5 h-5 mr-2 mt-0.5 flex-shrink-0" />
          <div className="text-sm">
            <p className="font-semibold mb-1">ملاحظة مهمة:</p>
            <ul className="space-y-1 text-xs">
              <li>• المؤقت يتوقف عند مغادرة الصفحة أو تغيير التبويب</li>
              <li>• يستمر العد عند العودة للصفحة</li>
              <li>• البيانات محفوظة حتى لو تم إعادة تحميل الصفحة</li>
            </ul>
          </div>
        </div>
      </div>

      {/* تأثير النبض للحدود */}
      <motion.div
        className={`absolute inset-0 rounded-lg border-2 ${
          darkMode ? "border-red-500" : "border-red-400"
        } opacity-30 pointer-events-none`}
        animate={{
          scale: [1, 1.02, 1],
          opacity: [0.3, 0.6, 0.3]
        }}
        transition={{
          duration: 2,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      />
    </motion.div>
  );
};

export default AdminLockoutTimer;
