import React, { useState, useEffect } from "react";
import Button from "../../common/Button";
import useThemeStore from "../../../store/themeStore";
import { adminService } from "../../../services/api";
import {
  Calendar,
  Clock,
  Search,
  Filter,
  Edit,
  Trash2,
  CheckCircle,
  XCircle,
  AlertCircle,
} from "lucide-react";
import toast from "react-hot-toast";

const BookingsSection = () => {
  const darkMode = useThemeStore((state) => state.darkMode);
  const [bookings, setBookings] = useState([]);
  const [stats, setStats] = useState({
    totalBookings: 0,
    pendingBookings: 0,
    completedBookings: 0,
    cancelledBookings: 0,
  });
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [filterStatus, setFilterStatus] = useState("all");
  const [selectedBooking, setSelectedBooking] = useState(null);
  const [showBookingModal, setShowBookingModal] = useState(false);

  // Pagination states
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // جلب البيانات عند تحميل المكون
  useEffect(() => {
    fetchBookings();
    fetchStats();
  }, []);

  // جلب قائمة الحجوزات
  const fetchBookings = async () => {
    try {
      setLoading(true);
      const bookingsData = await adminService.getAllBookings();
      setBookings(bookingsData);
    } catch (error) {
      console.error("خطأ في جلب الحجوزات:", error);
      toast.error("فشل في جلب قائمة الحجوزات");
    } finally {
      setLoading(false);
    }
  };

  // جلب الإحصائيات
  const fetchStats = async () => {
    try {
      const statsData = await adminService.getDashboardStats();
      setStats({
        totalBookings: statsData.totalBookings,
        pendingBookings: statsData.pendingBookings,
        completedBookings: statsData.completedBookings,
        cancelledBookings:
          statsData.totalBookings -
          statsData.pendingBookings -
          statsData.completedBookings,
      });
    } catch (error) {
      console.error("خطأ في جلب الإحصائيات:", error);
    }
  };

  // تصفية الحجوزات
  const filteredBookings = bookings.filter((booking) => {
    const matchesSearch =
      booking.client?.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      booking.craftsman?.user?.name
        ?.toLowerCase()
        .includes(searchTerm.toLowerCase()) ||
      booking.description?.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesFilter =
      filterStatus === "all" || booking.status === filterStatus;

    return matchesSearch && matchesFilter;
  });

  // Pagination logic
  const totalItems = filteredBookings.length;
  const totalPages = Math.ceil(totalItems / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentBookings = filteredBookings.slice(startIndex, endIndex);

  // Reset to first page when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm, filterStatus]);

  // تحديث حالة الحجز
  const updateBookingStatus = async (bookingId, newStatus) => {
    try {
      await adminService.updateBookingStatus(bookingId, newStatus);
      toast.success("تم تحديث حالة الحجز بنجاح");
      fetchBookings(); // إعادة جلب البيانات
    } catch (error) {
      console.error("خطأ في تحديث حالة الحجز:", error);
      toast.error("فشل في تحديث حالة الحجز");
    }
  };

  // معالجة الحجوزات المنتهية الصلاحية
  const processExpiredBookings = async () => {
    try {
      const result = await adminService.processExpiredBookings();
      toast.success(`تم معالجة ${result.count || 0} حجز منتهي الصلاحية`);
      fetchBookings(); // إعادة جلب البيانات
    } catch (error) {
      console.error("خطأ في معالجة الحجوزات المنتهية:", error);
      toast.error("فشل في معالجة الحجوزات المنتهية");
    }
  };

  // الحصول على لون الحالة
  const getStatusColor = (status) => {
    switch (status) {
      case "pending":
        return "bg-yellow-100 text-yellow-800";
      case "accepted":
        return "bg-blue-100 text-blue-800";
      case "completed":
        return "bg-green-100 text-green-800";
      case "cancelled":
      case "cancelled_expired":
        return "bg-red-100 text-red-800";
      case "rejected":
        return "bg-gray-100 text-gray-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  // الحصول على نص الحالة
  const getStatusText = (status) => {
    switch (status) {
      case "pending":
        return "معلق";
      case "accepted":
        return "مقبول";
      case "completed":
        return "مكتمل";
      case "cancelled":
        return "ملغي";
      case "cancelled_expired":
        return "ملغي (منتهي الصلاحية)";
      case "rejected":
        return "مرفوض";
      default:
        return status;
    }
  };

  // عرض تفاصيل الحجز
  const viewBooking = (booking) => {
    setSelectedBooking(booking);
    setShowBookingModal(true);
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h3
          className={`text-xl font-bold ${
            darkMode ? "text-indigo-300" : "text-indigo-800"
          } relative`}
        >
          <span className="relative z-10">إدارة الحجوزات</span>
          <span
            className={`absolute bottom-0 left-0 right-0 h-2 ${
              darkMode ? "bg-indigo-500" : "bg-indigo-300"
            } opacity-40 transform -rotate-1 z-0`}
          ></span>
        </h3>
        <Button
          variant="primary"
          onClick={processExpiredBookings}
          className={`${
            darkMode
              ? "bg-gradient-to-r from-[#3730A3] to-[#4238C8] hover:from-[#322e92] hover:to-[#3b32b4]"
              : "bg-gradient-to-r from-[#4238C8] to-[#3730A3] hover:from-[#3b32b4] hover:to-[#322e92]"
          } text-white transition-all duration-200 shadow-md hover:shadow-lg relative overflow-hidden group flex items-center gap-2`}
        >
          <Clock size={18} />
          <span className="relative z-10">معالجة الحجوزات المنتهية</span>
          <span className="absolute inset-0 bg-white opacity-20 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700"></span>
        </Button>
      </div>

      {/* إحصائيات الحجوزات */}
      <div
        className={`mb-6 p-4 rounded-lg ${
          darkMode
            ? "bg-gray-700"
            : "bg-gradient-to-r from-indigo-50 to-blue-50"
        } border ${darkMode ? "border-gray-600" : "border-indigo-100"}`}
      >
        <div className="flex justify-between items-center mb-4">
          <h4
            className={`font-bold ${
              darkMode ? "text-white" : "text-indigo-700"
            }`}
          >
            إحصائيات الحجوزات
          </h4>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div
            className={`p-4 rounded-lg ${
              darkMode
                ? "bg-gray-800"
                : "bg-gradient-to-br from-white to-indigo-50/50"
            } border ${
              darkMode ? "border-gray-700" : "border-indigo-100"
            } shadow-sm`}
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-500 text-sm mb-1">إجمالي الحجوزات</p>
                <p
                  className={`text-2xl font-bold ${
                    darkMode ? "text-indigo-300" : "text-indigo-600"
                  }`}
                >
                  {loading ? "..." : stats.totalBookings}
                </p>
              </div>
              <Calendar
                className={`${
                  darkMode ? "text-indigo-300" : "text-indigo-600"
                }`}
                size={24}
              />
            </div>
          </div>
          <div
            className={`p-4 rounded-lg ${
              darkMode
                ? "bg-gray-800"
                : "bg-gradient-to-br from-white to-yellow-50/50"
            } border ${
              darkMode ? "border-gray-700" : "border-yellow-100"
            } shadow-sm`}
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-500 text-sm mb-1">حجوزات معلقة</p>
                <p
                  className={`text-2xl font-bold ${
                    darkMode ? "text-yellow-300" : "text-yellow-600"
                  }`}
                >
                  {loading ? "..." : stats.pendingBookings}
                </p>
              </div>
              <AlertCircle
                className={`${
                  darkMode ? "text-yellow-300" : "text-yellow-600"
                }`}
                size={24}
              />
            </div>
          </div>
          <div
            className={`p-4 rounded-lg ${
              darkMode
                ? "bg-gray-800"
                : "bg-gradient-to-br from-white to-green-50/50"
            } border ${
              darkMode ? "border-gray-700" : "border-green-100"
            } shadow-sm`}
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-500 text-sm mb-1">حجوزات مكتملة</p>
                <p
                  className={`text-2xl font-bold ${
                    darkMode ? "text-green-300" : "text-green-600"
                  }`}
                >
                  {loading ? "..." : stats.completedBookings}
                </p>
              </div>
              <CheckCircle
                className={`${darkMode ? "text-green-300" : "text-green-600"}`}
                size={24}
              />
            </div>
          </div>
          <div
            className={`p-4 rounded-lg ${
              darkMode
                ? "bg-gray-800"
                : "bg-gradient-to-br from-white to-red-50/50"
            } border ${
              darkMode ? "border-gray-700" : "border-red-100"
            } shadow-sm`}
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-500 text-sm mb-1">حجوزات ملغية</p>
                <p
                  className={`text-2xl font-bold ${
                    darkMode ? "text-red-300" : "text-red-600"
                  }`}
                >
                  {loading ? "..." : stats.cancelledBookings}
                </p>
              </div>
              <XCircle
                className={`${darkMode ? "text-red-300" : "text-red-600"}`}
                size={24}
              />
            </div>
          </div>
        </div>
      </div>

      {/* أدوات البحث والتصفية */}
      <div
        className={`mb-6 p-4 rounded-lg ${
          darkMode ? "bg-gray-700" : "bg-white"
        } border ${darkMode ? "border-gray-600" : "border-gray-200"} shadow-sm`}
      >
        <div className="flex flex-col md:flex-row gap-4">
          {/* شريط البحث */}
          <div className="flex-1 relative">
            <Search
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400"
              size={20}
            />
            <input
              type="text"
              placeholder="البحث بالعميل أو الحرفي أو الوصف..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className={`w-full pr-10 pl-4 py-2 rounded-lg border ${
                darkMode
                  ? "bg-gray-800 border-gray-600 text-white placeholder-gray-400"
                  : "bg-gray-50 border-gray-300 text-gray-900 placeholder-gray-500"
              } focus:outline-none focus:ring-2 focus:ring-indigo-500`}
            />
          </div>

          {/* تصفية الحالة */}
          <div className="relative">
            <Filter
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400"
              size={20}
            />
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              className={`pr-10 pl-4 py-2 rounded-lg border ${
                darkMode
                  ? "bg-gray-800 border-gray-600 text-white"
                  : "bg-gray-50 border-gray-300 text-gray-900"
              } focus:outline-none focus:ring-2 focus:ring-indigo-500`}
            >
              <option value="all">جميع الحجوزات</option>
              <option value="pending">معلقة</option>
              <option value="accepted">مقبولة</option>
              <option value="completed">مكتملة</option>
              <option value="cancelled">ملغية</option>
              <option value="rejected">مرفوضة</option>
            </select>
          </div>
        </div>
      </div>

      {/* جدول الحجوزات */}
      <div
        className={`rounded-lg ${
          darkMode ? "bg-gray-700" : "bg-white"
        } border ${
          darkMode ? "border-gray-600" : "border-gray-200"
        } shadow-sm overflow-hidden`}
      >
        <div
          className={`px-6 py-4 border-b ${
            darkMode
              ? "border-gray-600 bg-gray-800"
              : "border-gray-200 bg-gray-50"
          }`}
        >
          <h4
            className={`font-semibold ${
              darkMode ? "text-white" : "text-gray-800"
            }`}
          >
            قائمة الحجوزات ({totalItems} حجز - عرض {startIndex + 1}-
            {Math.min(endIndex, totalItems)})
          </h4>
        </div>

        {loading ? (
          <div className="flex justify-center items-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
            <span
              className={`mr-3 ${darkMode ? "text-gray-300" : "text-gray-600"}`}
            >
              جاري التحميل...
            </span>
          </div>
        ) : filteredBookings.length === 0 ? (
          <div className="text-center py-12">
            <Calendar
              className={`mx-auto h-12 w-12 ${
                darkMode ? "text-gray-400" : "text-gray-300"
              } mb-4`}
            />
            <p className={`${darkMode ? "text-gray-300" : "text-gray-600"}`}>
              {searchTerm || filterStatus !== "all"
                ? "لا توجد نتائج مطابقة للبحث"
                : "لا يوجد حجوزات"}
            </p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className={darkMode ? "bg-gray-800" : "bg-gray-50"}>
                <tr>
                  <th
                    className={`px-6 py-3 text-right text-xs font-medium ${
                      darkMode ? "text-gray-300" : "text-gray-500"
                    } uppercase tracking-wider`}
                  >
                    العميل
                  </th>
                  <th
                    className={`px-6 py-3 text-right text-xs font-medium ${
                      darkMode ? "text-gray-300" : "text-gray-500"
                    } uppercase tracking-wider`}
                  >
                    الحرفي
                  </th>
                  <th
                    className={`px-6 py-3 text-right text-xs font-medium ${
                      darkMode ? "text-gray-300" : "text-gray-500"
                    } uppercase tracking-wider`}
                  >
                    الوصف
                  </th>
                  <th
                    className={`px-6 py-3 text-right text-xs font-medium ${
                      darkMode ? "text-gray-300" : "text-gray-500"
                    } uppercase tracking-wider`}
                  >
                    التاريخ والوقت
                  </th>
                  <th
                    className={`px-6 py-3 text-right text-xs font-medium ${
                      darkMode ? "text-gray-300" : "text-gray-500"
                    } uppercase tracking-wider`}
                  >
                    الحالة
                  </th>
                  <th
                    className={`px-6 py-3 text-right text-xs font-medium ${
                      darkMode ? "text-gray-300" : "text-gray-500"
                    } uppercase tracking-wider`}
                  >
                    الإجراءات
                  </th>
                </tr>
              </thead>
              <tbody
                className={`${darkMode ? "bg-gray-700" : "bg-white"} divide-y ${
                  darkMode ? "divide-gray-600" : "divide-gray-200"
                }`}
              >
                {currentBookings.map((booking) => (
                  <tr
                    key={booking._id}
                    className={`hover:${
                      darkMode ? "bg-gray-600" : "bg-gray-50"
                    } transition-colors`}
                  >
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div
                          className={`text-sm font-medium ${
                            darkMode ? "text-white" : "text-gray-900"
                          }`}
                        >
                          {booking.client?.name}
                        </div>
                        <div
                          className={`text-sm ${
                            darkMode ? "text-gray-300" : "text-gray-500"
                          }`}
                        >
                          {booking.client?.email}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div
                          className={`text-sm font-medium ${
                            darkMode ? "text-white" : "text-gray-900"
                          }`}
                        >
                          {booking.craftsman?.user?.name}
                        </div>
                        <div
                          className={`text-sm ${
                            darkMode ? "text-gray-300" : "text-gray-500"
                          }`}
                        >
                          {booking.craftsman?.user?.phone ||
                            booking.craftsman?.user?.email}
                        </div>
                      </div>
                    </td>
                    <td
                      className={`px-6 py-4 ${
                        darkMode ? "text-gray-300" : "text-gray-900"
                      }`}
                    >
                      <div className="text-sm max-w-xs truncate">
                        {booking.description}
                      </div>
                    </td>
                    <td
                      className={`px-6 py-4 whitespace-nowrap text-sm ${
                        darkMode ? "text-gray-300" : "text-gray-500"
                      }`}
                    >
                      <div>
                        {new Date(booking.date).toLocaleDateString("en-GB")}
                      </div>
                      <div className="text-xs">{booking.time}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span
                        className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(
                          booking.status
                        )}`}
                      >
                        {getStatusText(booking.status)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex items-center gap-2">
                        {booking.status === "pending" && (
                          <>
                            <button
                              onClick={() =>
                                updateBookingStatus(booking._id, "accepted")
                              }
                              className="p-2 text-green-600 hover:bg-green-50 rounded-lg transition-colors"
                              title="قبول"
                            >
                              <CheckCircle size={16} />
                            </button>
                            <button
                              onClick={() =>
                                updateBookingStatus(booking._id, "rejected")
                              }
                              className="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                              title="رفض"
                            >
                              <XCircle size={16} />
                            </button>
                          </>
                        )}
                        {booking.status === "accepted" && (
                          <button
                            onClick={() =>
                              updateBookingStatus(booking._id, "completed")
                            }
                            className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                            title="إكمال"
                          >
                            <CheckCircle size={16} />
                          </button>
                        )}
                        <button
                          onClick={() => viewBooking(booking)}
                          className="p-2 text-indigo-600 hover:bg-indigo-50 rounded-lg transition-colors"
                          title="عرض التفاصيل"
                        >
                          <Edit size={16} />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Pagination Controls */}
      {!loading && filteredBookings.length > 0 && (
        <div
          className={`mt-6 flex flex-col sm:flex-row justify-between items-center gap-4 p-4 rounded-lg ${
            darkMode ? "bg-gray-700" : "bg-white"
          } border ${
            darkMode ? "border-gray-600" : "border-gray-200"
          } shadow-sm`}
        >
          {/* Items per page selector */}
          <div className="flex items-center gap-2">
            <span
              className={`text-sm ${
                darkMode ? "text-gray-300" : "text-gray-600"
              }`}
            >
              عرض
            </span>
            <select
              value={itemsPerPage}
              onChange={(e) => {
                setItemsPerPage(Number(e.target.value));
                setCurrentPage(1);
              }}
              className={`px-3 py-1 rounded border ${
                darkMode
                  ? "bg-gray-800 border-gray-600 text-white"
                  : "bg-white border-gray-300 text-gray-900"
              } focus:outline-none focus:ring-2 focus:ring-indigo-500`}
            >
              <option value={5}>5</option>
              <option value={10}>10</option>
              <option value={20}>20</option>
              <option value={50}>50</option>
            </select>
            <span
              className={`text-sm ${
                darkMode ? "text-gray-300" : "text-gray-600"
              }`}
            >
              من {totalItems} حجز
            </span>
          </div>

          {/* Page info */}
          <div
            className={`text-sm ${
              darkMode ? "text-gray-300" : "text-gray-600"
            }`}
          >
            صفحة {currentPage} من {totalPages}
          </div>

          {/* Pagination buttons */}
          <div className="flex items-center gap-1">
            <button
              onClick={() => setCurrentPage(1)}
              disabled={currentPage === 1}
              className={`px-3 py-1 rounded ${
                currentPage === 1
                  ? "bg-gray-200 text-gray-400 cursor-not-allowed"
                  : darkMode
                  ? "bg-gray-600 text-white hover:bg-gray-500"
                  : "bg-gray-100 text-gray-700 hover:bg-gray-200"
              } transition-colors`}
            >
              الأولى
            </button>

            <button
              onClick={() => setCurrentPage(currentPage - 1)}
              disabled={currentPage === 1}
              className={`px-3 py-1 rounded ${
                currentPage === 1
                  ? "bg-gray-200 text-gray-400 cursor-not-allowed"
                  : darkMode
                  ? "bg-gray-600 text-white hover:bg-gray-500"
                  : "bg-gray-100 text-gray-700 hover:bg-gray-200"
              } transition-colors`}
            >
              السابق
            </button>

            {/* Page numbers */}
            {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
              let pageNum;
              if (totalPages <= 5) {
                pageNum = i + 1;
              } else if (currentPage <= 3) {
                pageNum = i + 1;
              } else if (currentPage >= totalPages - 2) {
                pageNum = totalPages - 4 + i;
              } else {
                pageNum = currentPage - 2 + i;
              }

              return (
                <button
                  key={pageNum}
                  onClick={() => setCurrentPage(pageNum)}
                  className={`px-3 py-1 rounded ${
                    currentPage === pageNum
                      ? "bg-indigo-600 text-white"
                      : darkMode
                      ? "bg-gray-600 text-white hover:bg-gray-500"
                      : "bg-gray-100 text-gray-700 hover:bg-gray-200"
                  } transition-colors`}
                >
                  {pageNum}
                </button>
              );
            })}

            <button
              onClick={() => setCurrentPage(currentPage + 1)}
              disabled={currentPage === totalPages}
              className={`px-3 py-1 rounded ${
                currentPage === totalPages
                  ? "bg-gray-200 text-gray-400 cursor-not-allowed"
                  : darkMode
                  ? "bg-gray-600 text-white hover:bg-gray-500"
                  : "bg-gray-100 text-gray-700 hover:bg-gray-200"
              } transition-colors`}
            >
              التالي
            </button>

            <button
              onClick={() => setCurrentPage(totalPages)}
              disabled={currentPage === totalPages}
              className={`px-3 py-1 rounded ${
                currentPage === totalPages
                  ? "bg-gray-200 text-gray-400 cursor-not-allowed"
                  : darkMode
                  ? "bg-gray-600 text-white hover:bg-gray-500"
                  : "bg-gray-100 text-gray-700 hover:bg-gray-200"
              } transition-colors`}
            >
              الأخيرة
            </button>
          </div>
        </div>
      )}

      {/* مودال عرض تفاصيل الحجز */}
      {showBookingModal && selectedBooking && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div
            className={`${
              darkMode ? "bg-gray-800" : "bg-white"
            } rounded-lg p-6 max-w-lg w-full mx-4 shadow-xl`}
          >
            <div className="flex justify-between items-center mb-4">
              <h3
                className={`text-lg font-bold ${
                  darkMode ? "text-white" : "text-gray-900"
                }`}
              >
                تفاصيل الحجز
              </h3>
              <button
                onClick={() => setShowBookingModal(false)}
                className={`text-gray-500 hover:text-gray-700 ${
                  darkMode ? "hover:text-gray-300" : ""
                }`}
              >
                ✕
              </button>
            </div>
            <div className="space-y-3">
              <div>
                <span className="font-semibold">العميل: </span>
                <span>{selectedBooking.client?.name}</span>
              </div>
              <div>
                <span className="font-semibold">بريد العميل: </span>
                <span>{selectedBooking.client?.email}</span>
              </div>
              <div>
                <span className="font-semibold">الحرفي: </span>
                <span>{selectedBooking.craftsman?.user?.name}</span>
              </div>
              <div>
                <span className="font-semibold">هاتف الحرفي: </span>
                <span>
                  {selectedBooking.craftsman?.user?.phone ||
                    selectedBooking.craftsman?.user?.email}
                </span>
              </div>
              <div>
                <span className="font-semibold">الوصف: </span>
                <p className="mt-1 text-sm">{selectedBooking.description}</p>
              </div>
              <div>
                <span className="font-semibold">التاريخ: </span>
                <span>
                  {new Date(selectedBooking.date).toLocaleDateString("en-GB")}
                </span>
              </div>
              <div>
                <span className="font-semibold">الوقت: </span>
                <span>{selectedBooking.time}</span>
              </div>
              <div>
                <span className="font-semibold">الحالة: </span>
                <span
                  className={`px-2 py-1 rounded text-xs ${getStatusColor(
                    selectedBooking.status
                  )}`}
                >
                  {getStatusText(selectedBooking.status)}
                </span>
              </div>
              {selectedBooking.price && (
                <div>
                  <span className="font-semibold">السعر: </span>
                  <span>{selectedBooking.price} ل.س</span>
                </div>
              )}
              {selectedBooking.notes && (
                <div>
                  <span className="font-semibold">ملاحظات: </span>
                  <p className="mt-1 text-sm">{selectedBooking.notes}</p>
                </div>
              )}
              <div>
                <span className="font-semibold">تاريخ الإنشاء: </span>
                <span>
                  {new Date(selectedBooking.createdAt).toLocaleDateString(
                    "en-GB"
                  )}
                </span>
              </div>
            </div>
            <div className="mt-6 flex justify-end gap-2">
              {selectedBooking.status === "pending" && (
                <>
                  <button
                    onClick={() => {
                      updateBookingStatus(selectedBooking._id, "accepted");
                      setShowBookingModal(false);
                    }}
                    className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                  >
                    قبول
                  </button>
                  <button
                    onClick={() => {
                      updateBookingStatus(selectedBooking._id, "rejected");
                      setShowBookingModal(false);
                    }}
                    className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                  >
                    رفض
                  </button>
                </>
              )}
              {selectedBooking.status === "accepted" && (
                <button
                  onClick={() => {
                    updateBookingStatus(selectedBooking._id, "completed");
                    setShowBookingModal(false);
                  }}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  إكمال
                </button>
              )}
              <button
                onClick={() => setShowBookingModal(false)}
                className="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors"
              >
                إغلاق
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default BookingsSection;
