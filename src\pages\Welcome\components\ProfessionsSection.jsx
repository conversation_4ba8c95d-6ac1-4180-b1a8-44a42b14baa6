import React from "react";
import { motion } from "framer-motion";
import useThemeStore from "../../../store/themeStore";
import {
  Lightbulb,
  Droplets,
  Hammer,
  PaintBucket,
  Sofa,
  Car,
  Scissors,
  Building,
  Tractor,
} from "lucide-react";
import { Link } from "react-router-dom";

// قائمة المهن مع الصور المتحركة
const professions = [
  {
    name: "ميكانيكي",
    description: "إصلاح وصيانة السيارات والآلات",
    image:
      "https://images.unsplash.com/photo-1581092160562-40aa08e78837?w=400&h=300&fit=crop&crop=center",
    color: "from-gray-600 to-gray-800",
    icon: "🔧",
  },
  {
    name: "مصمم ديكور",
    description: "تصميم وتنفيذ الديكورات الداخلية",
    image:
      "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=300&fit=crop&crop=center",
    color: "from-pink-500 to-rose-600",
    icon: "🎨",
  },
  {
    name: "دهان",
    description: "طلاء وتجديد الجدران والأسطح",
    image:
      "https://images.unsplash.com/photo-1562259949-e8e7689d7828?w=400&h=300&fit=crop&crop=center",
    color: "from-purple-500 to-indigo-600",
    icon: "🎨",
  },
  {
    name: "نجار",
    description: "صنع وإصلاح الأثاث الخشبي",
    image:
      "https://images.unsplash.com/photo-1504148455328-c376907d081c?w=400&h=300&fit=crop&crop=center",
    color: "from-orange-500 to-amber-600",
    icon: "🪚",
  },
  {
    name: "سباك",
    description: "تركيب وإصلاح أنظمة المياه والصرف",
    image:
      "https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=300&fit=crop&crop=center",
    color: "from-blue-500 to-cyan-600",
    icon: "🔧",
  },
  {
    name: "كهربائي",
    description: "تركيب وصيانة الأنظمة الكهربائية",
    image:
      "https://images.unsplash.com/photo-1621905251189-08b45d6a269e?w=400&h=300&fit=crop&crop=center",
    color: "from-yellow-500 to-orange-600",
    icon: "⚡",
  },
  {
    name: "مزارع",
    description: "الزراعة وإنتاج المحاصيل والخضروات",
    image:
      "https://images.unsplash.com/photo-1500937386664-56d1dfef3854?w=400&h=300&fit=crop&crop=center",
    color: "from-green-600 to-emerald-700",
    icon: "🌱",
  },
  {
    name: "بناء",
    description: "أعمال البناء والتشييد والإنشاءات",
    image:
      "https://images.unsplash.com/photo-1504307651254-35680f356dfd?w=400&h=300&fit=crop&crop=center",
    color: "from-stone-600 to-gray-700",
    icon: "🏗️",
  },
  {
    name: "حداد",
    description: "صناعة وإصلاح المنتجات المعدنية",
    image:
      "https://images.unsplash.com/photo-1504328345606-18bbc8c9d7d1?w=400&h=300&fit=crop&crop=center",
    color: "from-gray-700 to-slate-800",
    icon: "⚒️",
  },
];

const ProfessionsSection = () => {
  const darkMode = useThemeStore((state) => state.darkMode);

  // تكوين التأثيرات الحركية
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.05,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, scale: 0.8 },
    visible: {
      opacity: 1,
      scale: 1,
      transition: {
        duration: 0.4,
      },
    },
  };

  return (
    <section
      id="professions"
      className={`py-20 ${
        darkMode
          ? "bg-gradient-to-b from-gray-900 via-indigo-950/20 to-gray-900"
          : "bg-gradient-to-b from-blue-50 via-indigo-100/30 to-blue-50"
      } transition-colors duration-300 relative overflow-hidden border-t ${
        darkMode ? "border-indigo-900" : "border-indigo-200"
      }`}
      style={{
        boxShadow: darkMode
          ? "0 -8px 20px -5px rgba(0, 0, 0, 0.3)"
          : "0 -8px 20px -5px rgba(79, 70, 229, 0.1)",
      }}
    >
      {/* خط متدرج في الأعلى */}
      <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-indigo-500 to-transparent opacity-30"></div>

      {/* زخارف خلفية */}
      <div className="absolute inset-0 overflow-hidden bg-pattern"></div>

      <div className="container mx-auto px-4 relative z-10">
        <motion.div
          className="text-center mb-12"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <h2
            className={`text-3xl md:text-5xl font-bold ${
              darkMode ? "text-indigo-300" : "text-indigo-800"
            } relative inline-block transition-colors duration-300 mb-4`}
          >
            <span className="relative z-10">المهن المتوفرة</span>
            <span
              className={`absolute bottom-0 left-0 right-0 h-3 ${
                darkMode ? "bg-indigo-500" : "bg-indigo-300"
              } opacity-40 transform -rotate-1 z-0`}
            ></span>
          </h2>

          <p
            className={`max-w-3xl mx-auto text-lg ${
              darkMode ? "text-gray-300" : "text-gray-700"
            }`}
          >
            اكتشف مجموعة متنوعة من المهن والخدمات المتاحة على منصتنا
          </p>
        </motion.div>

        <motion.div
          className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4 md:gap-6"
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          {professions.map((profession) => (
            <motion.div
              key={profession.name}
              className="feature-card"
              variants={itemVariants}
            >
              <Link
                to={`/search?profession=${profession.name}`}
                className={`group flex flex-col items-center p-4 h-full rounded-xl ${
                  darkMode
                    ? "bg-gray-800/50 hover:bg-gray-800/80 border border-gray-700"
                    : "bg-white/80 hover:bg-white border border-indigo-100"
                } shadow-md hover:shadow-lg transition-all duration-300 overflow-hidden`}
              >
                {/* صورة المهنة */}
                <div className="relative w-full h-24 mb-3 rounded-lg overflow-hidden">
                  <img
                    src={profession.image}
                    alt={profession.name}
                    className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110"
                    loading="lazy"
                    onError={(e) => {
                      // في حالة فشل تحميل الصورة، استخدم لون خلفية مع الأيقونة
                      e.target.style.display = "none";
                      e.target.nextElementSibling.style.display = "flex";
                    }}
                  />
                  {/* بديل في حالة فشل تحميل الصورة */}
                  <div
                    className={`absolute inset-0 hidden items-center justify-center text-3xl ${
                      darkMode
                        ? "bg-gradient-to-br from-indigo-900 to-indigo-800"
                        : `bg-gradient-to-br ${profession.color}`
                    }`}
                  >
                    {profession.icon}
                  </div>
                </div>

                <span
                  className={`text-center font-bold px-3 py-1.5 rounded-lg mb-2 ${
                    darkMode ? "text-white" : "text-indigo-800"
                  }`}
                >
                  {profession.name}
                </span>
                <p
                  className={`text-xs text-center line-clamp-2 ${
                    darkMode ? "text-gray-400" : "text-gray-600"
                  }`}
                >
                  {profession.description}
                </p>
              </Link>
            </motion.div>
          ))}
        </motion.div>

        <motion.div
          className="text-center mt-12"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.8 }}
        >
          <Link
            to="/search"
            className={`inline-flex items-center gap-2 px-6 py-3 rounded-xl font-bold transition-all duration-300 ${
              darkMode
                ? "bg-indigo-700 text-white hover:bg-indigo-600"
                : "bg-indigo-600 text-white hover:bg-indigo-700"
            } shadow-md hover:shadow-lg`}
          >
            <span>استكشاف جميع المهن</span>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="20"
              height="20"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <circle cx="11" cy="11" r="8"></circle>
              <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
            </svg>
          </Link>
        </motion.div>
      </div>
    </section>
  );
};

export default ProfessionsSection;
