import React from "react";
import useThemeStore from "../../store/themeStore";

const Pagination = ({
  currentPage,
  totalPages,
  totalItems,
  itemsPerPage,
  startIndex,
  endIndex,
  onPageChange,
  onItemsPerPageChange,
  itemType = "عنصر"
}) => {
  const { darkMode } = useThemeStore();

  return (
    <div
      className={`mt-6 flex flex-col sm:flex-row justify-between items-center gap-4 p-4 rounded-lg ${
        darkMode ? "bg-gray-700" : "bg-white"
      } border ${darkMode ? "border-gray-600" : "border-gray-200"} shadow-sm`}
    >
      {/* Items per page selector */}
      <div className="flex items-center gap-2">
        <span
          className={`text-sm ${
            darkMode ? "text-gray-300" : "text-gray-600"
          }`}
        >
          عرض
        </span>
        <select
          value={itemsPerPage}
          onChange={(e) => onItemsPerPageChange(Number(e.target.value))}
          className={`px-3 py-1 rounded border ${
            darkMode
              ? "bg-gray-800 border-gray-600 text-white"
              : "bg-white border-gray-300 text-gray-900"
          } focus:outline-none focus:ring-2 focus:ring-indigo-500`}
        >
          <option value={5}>5</option>
          <option value={10}>10</option>
          <option value={20}>20</option>
          <option value={50}>50</option>
        </select>
        <span
          className={`text-sm ${
            darkMode ? "text-gray-300" : "text-gray-600"
          }`}
        >
          من {totalItems} {itemType}
        </span>
      </div>

      {/* Page info */}
      <div
        className={`text-sm ${
          darkMode ? "text-gray-300" : "text-gray-600"
        }`}
      >
        صفحة {currentPage} من {totalPages}
      </div>

      {/* Pagination buttons */}
      <div className="flex items-center gap-1">
        <button
          onClick={() => onPageChange(1)}
          disabled={currentPage === 1}
          className={`px-3 py-1 rounded ${
            currentPage === 1
              ? "bg-gray-200 text-gray-400 cursor-not-allowed"
              : darkMode
              ? "bg-gray-600 text-white hover:bg-gray-500"
              : "bg-gray-100 text-gray-700 hover:bg-gray-200"
          } transition-colors`}
        >
          الأولى
        </button>
        
        <button
          onClick={() => onPageChange(currentPage - 1)}
          disabled={currentPage === 1}
          className={`px-3 py-1 rounded ${
            currentPage === 1
              ? "bg-gray-200 text-gray-400 cursor-not-allowed"
              : darkMode
              ? "bg-gray-600 text-white hover:bg-gray-500"
              : "bg-gray-100 text-gray-700 hover:bg-gray-200"
          } transition-colors`}
        >
          السابق
        </button>

        {/* Page numbers */}
        {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
          let pageNum;
          if (totalPages <= 5) {
            pageNum = i + 1;
          } else if (currentPage <= 3) {
            pageNum = i + 1;
          } else if (currentPage >= totalPages - 2) {
            pageNum = totalPages - 4 + i;
          } else {
            pageNum = currentPage - 2 + i;
          }

          return (
            <button
              key={pageNum}
              onClick={() => onPageChange(pageNum)}
              className={`px-3 py-1 rounded ${
                currentPage === pageNum
                  ? "bg-indigo-600 text-white"
                  : darkMode
                  ? "bg-gray-600 text-white hover:bg-gray-500"
                  : "bg-gray-100 text-gray-700 hover:bg-gray-200"
              } transition-colors`}
            >
              {pageNum}
            </button>
          );
        })}

        <button
          onClick={() => onPageChange(currentPage + 1)}
          disabled={currentPage === totalPages}
          className={`px-3 py-1 rounded ${
            currentPage === totalPages
              ? "bg-gray-200 text-gray-400 cursor-not-allowed"
              : darkMode
              ? "bg-gray-600 text-white hover:bg-gray-500"
              : "bg-gray-100 text-gray-700 hover:bg-gray-200"
          } transition-colors`}
        >
          التالي
        </button>

        <button
          onClick={() => onPageChange(totalPages)}
          disabled={currentPage === totalPages}
          className={`px-3 py-1 rounded ${
            currentPage === totalPages
              ? "bg-gray-200 text-gray-400 cursor-not-allowed"
              : darkMode
              ? "bg-gray-600 text-white hover:bg-gray-500"
              : "bg-gray-100 text-gray-700 hover:bg-gray-200"
          } transition-colors`}
        >
          الأخيرة
        </button>
      </div>
    </div>
  );
};

export default Pagination;
