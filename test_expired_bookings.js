// ملف اختبار لميزة الإلغاء التلقائي للطلبات المنتهية الصلاحية
// يمكن تشغيله من خلال: node test_expired_bookings.js

const axios = require('axios');

// إعدادات الاختبار
const API_URL = 'https://jobscope-8t58.onrender.com/api';
const TEST_TOKEN = 'YOUR_TEST_TOKEN_HERE'; // ضع هنا token صالح للاختبار

// دالة لاختبار endpoint الإلغاء التلقائي
async function testCancelExpiredBookings() {
  try {
    console.log('🧪 اختبار ميزة الإلغاء التلقائي للطلبات المنتهية الصلاحية...');
    
    const response = await axios.post(
      `${API_URL}/bookings/cancel-expired`,
      {},
      {
        headers: {
          'Authorization': `Bearer ${TEST_TOKEN}`,
          'Content-Type': 'application/json'
        }
      }
    );

    console.log('✅ نجح الاختبار!');
    console.log('📊 النتائج:', response.data);
    
    if (response.data.count > 0) {
      console.log(`🔄 تم إلغاء ${response.data.count} طلب تلقائياً`);
    } else {
      console.log('ℹ️ لا توجد طلبات منتهية الصلاحية للإلغاء');
    }
    
  } catch (error) {
    console.error('❌ فشل الاختبار:', error.response?.data || error.message);
  }
}

// دالة لاختبار جلب الطلبات (يجب أن تشغل الفحص التلقائي)
async function testGetBookings() {
  try {
    console.log('🧪 اختبار جلب الطلبات مع الفحص التلقائي...');
    
    const response = await axios.get(
      `${API_URL}/bookings/me`,
      {
        headers: {
          'Authorization': `Bearer ${TEST_TOKEN}`,
          'Content-Type': 'application/json'
        }
      }
    );

    console.log('✅ نجح جلب الطلبات!');
    console.log(`📊 عدد الطلبات: ${response.data.length}`);
    
    // عرض حالات الطلبات
    const statusCounts = response.data.reduce((acc, booking) => {
      acc[booking.status] = (acc[booking.status] || 0) + 1;
      return acc;
    }, {});
    
    console.log('📈 إحصائيات الحالات:', statusCounts);
    
  } catch (error) {
    console.error('❌ فشل جلب الطلبات:', error.response?.data || error.message);
  }
}

// تشغيل الاختبارات
async function runTests() {
  console.log('🚀 بدء اختبار ميزة الإلغاء التلقائي للطلبات المنتهية الصلاحية\n');
  
  if (TEST_TOKEN === 'YOUR_TEST_TOKEN_HERE') {
    console.log('⚠️ تحذير: يرجى تعديل TEST_TOKEN في الملف قبل تشغيل الاختبار');
    return;
  }
  
  await testCancelExpiredBookings();
  console.log('\n' + '='.repeat(50) + '\n');
  await testGetBookings();
  
  console.log('\n✨ انتهى الاختبار');
}

// تشغيل الاختبارات إذا تم استدعاء الملف مباشرة
if (require.main === module) {
  runTests();
}

module.exports = {
  testCancelExpiredBookings,
  testGetBookings
};
