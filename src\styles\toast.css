/* تأثيرات الإشعارات المنبثقة (Toast Notifications) */
@keyframes toast-appear {
  0% {
    opacity: 0;
    transform: scale(0.9) translateY(20px);
  }
  20% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
  80% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
  100% {
    opacity: 0;
    transform: scale(0.9) translateY(0);
  }
}

/* حاوية الإشعارات */
#toast-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  pointer-events: none;
  z-index: 9999;
}

/* الإشعار المنبثق */
.animate-toast {
  animation: toast-appear 3.5s ease-in-out forwards;
  pointer-events: auto;
  max-width: 90%;
  width: 400px;
  margin: 0 auto;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
  border-radius: 8px;
  overflow: hidden;
  direction: rtl;
  text-align: center;
}

/* تأثير الانتقال للإشعار عند الإغلاق */
.animate-toast.opacity-0 {
  transition: opacity 0.3s ease-out, transform 0.3s ease-out;
  transform: scale(0.9);
}

/* أنماط الأيقونات في الإشعارات */
.animate-toast svg {
  width: 24px;
  height: 24px;
  stroke-width: 2;
}
