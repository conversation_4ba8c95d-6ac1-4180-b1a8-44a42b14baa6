const commonTranslations = {
  ar: {
    // Platform
    platformDescription: "منصة سورية لربط طالبي الخدمات بالحرفيين الأقرب إليك",
    quickLinks: "روابط سريعة",
    searchForCraftsman: "البحث عن حرفي",
    registerAsCraftsman: "تسجيل كحرفي",
    darkMode: "الوضع الداكن",
    lightMode: "الوضع النهاري",
    // Header/Navigation
    home: "الرئيسية",
    search: "البحث",
    profile: "الملف الشخصي",
    settings: "الإعدادات",
    login: "تسجيل الدخول",
    register: "تسجيل حساب جديد",
    logout: "تسجيل الخروج",

    // Common buttons
    save: "حفظ",
    cancel: "إلغاء",
    edit: "تعديل",
    delete: "حذف",
    back: "رجوع",
    next: "التالي",
    submit: "إرسال",

    // Common labels
    name: "الاسم",
    email: "البريد الإلكتروني",
    phone: "رقم الهاتف",
    address: "العنوان",
    password: "كلمة المرور",
    confirmPassword: "تأكيد كلمة المرور",

    // Status messages
    loading: "جاري التحميل...",
    success: "تمت العملية بنجاح",
    error: "حدث خطأ",

    // User types
    client: "عميل",
    craftsman: "حرفي",

    // Craftsman related
    profession: "المهنة",
    experience: "الخبرة",
    rating: "التقييم",
    workRange: "نطاق العمل",

    // Request related
    requests: "الطلبات",
    newRequest: "طلب جديد",
    myRequests: "طلباتي",
    requestDetails: "تفاصيل الطلب",
    requestStatus: "حالة الطلب",

    // Status
    pending: "قيد الانتظار",
    accepted: "مقبول",
    rejected: "مرفوض",
    completed: "مكتمل",

    // Time
    today: "اليوم",
    yesterday: "أمس",
    days: "أيام",
    hours: "ساعات",
    minutes: "دقائق",

    // Footer
    copyright: "جميع الحقوق محفوظة",
    terms: "الشروط والأحكام",
    privacy: "سياسة الخصوصية",
    contact: "اتصل بنا",
  },
  en: {
    // Platform
    platformDescription:
      "Syrian platform connecting service seekers with craftsmen nearest to you",
    quickLinks: "Quick Links",
    searchForCraftsman: "Search for Craftsman",
    registerAsCraftsman: "Register as Craftsman",
    darkMode: "Dark Mode",
    lightMode: "Light Mode",
    // Header/Navigation
    home: "Home",
    search: "Search",
    profile: "Profile",
    settings: "Settings",
    login: "Login",
    register: "Register",
    logout: "Logout",

    // Common buttons
    save: "Save",
    cancel: "Cancel",
    edit: "Edit",
    delete: "Delete",
    back: "Back",
    next: "Next",
    submit: "Submit",

    // Common labels
    name: "Name",
    email: "Email",
    phone: "Phone",
    address: "Address",
    password: "Password",
    confirmPassword: "Confirm Password",

    // Status messages
    loading: "Loading...",
    success: "Operation completed successfully",
    error: "An error occurred",

    // User types
    client: "Client",
    craftsman: "Craftsman",

    // Craftsman related
    profession: "Profession",
    experience: "Experience",
    rating: "Rating",
    workRange: "Work Range",

    // Request related
    requests: "Requests",
    newRequest: "New Request",
    myRequests: "My Requests",
    requestDetails: "Request Details",
    requestStatus: "Request Status",

    // Status
    pending: "Pending",
    accepted: "Accepted",
    rejected: "Rejected",
    completed: "Completed",

    // Time
    today: "Today",
    yesterday: "Yesterday",
    days: "days",
    hours: "hours",
    minutes: "minutes",

    // Footer
    copyright: "All Rights Reserved",
    terms: "Terms & Conditions",
    privacy: "Privacy Policy",
    contact: "Contact Us",
  },
};

export default commonTranslations;
