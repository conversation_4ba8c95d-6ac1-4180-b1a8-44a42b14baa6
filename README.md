# JobScope - منصة ربط طالبي الخدمة بالحرفيين

JobScope هي منصة عربية تربط بين طالبي الخدمة والحرفيين في سوريا، مبنية باستخدام React + Tailwind للواجهة الأمامية، و Node.js + Express + MongoDB للواجهة الخلفية.

## ميزات المشروع

- **نظام تسجيل وتسجيل دخول متكامل**: يدعم التسجيل بالبريد الإلكتروني والهاتف
- **ملفات شخصية للحرفيين**: تتضمن المهن، التخصصات، معرض الأعمال، والتقييمات
- **نظام حجز متقدم**: يتيح للمستخدمين حجز خدمات الحرفيين وتتبع حالة الطلبات
- **نظام تقييم متعدد المعايير**: يشمل الجودة، الالتزام بالمواعيد، السعر، والتواصل
- **خرائط تفاعلية**: تعرض الحرفيين ضمن نطاق محدد مع تحديد مناطق الخدمة
- **نظام بحث متقدم**: يدعم البحث بالمهنة، التخصص، التقييم، والموقع
- **الوضع المظلم**: واجهة مستخدم متوافقة مع الوضع المظلم
- **تعدد اللغات**: دعم اللغة العربية والإنجليزية
- **لوحة تحكم للمدير**: لإدارة المستخدمين، الحرفيين، والمحتوى

## هيكل المشروع

```
jobscope/
├── src/                  # الواجهة الأمامية (React)
│   ├── components/       # مكونات React
│   ├── pages/            # صفحات التطبيق
│   ├── store/            # إدارة الحالة (Zustand)
│   ├── services/         # خدمات API
│   ├── utils/            # أدوات مساعدة
│   └── App.jsx           # مكون التطبيق الرئيسي
├── backend/              # الواجهة الخلفية (Node.js + Express)
│   ├── src/              # مصدر الكود
│   │   ├── config/       # ملفات التكوين
│   │   ├── controllers/  # وحدات التحكم
│   │   ├── middleware/   # الوسائط
│   │   ├── models/       # نماذج البيانات
│   │   ├── routes/       # مسارات API
│   │   ├── services/     # الخدمات
│   │   ├── utils/        # الأدوات المساعدة
│   │   └── server.js     # نقطة الدخول للخادم
│   └── uploads/          # مجلد الملفات المحملة
├── public/               # الملفات العامة
└── package.json          # تبعيات المشروع
```

## التقنيات المستخدمة

### الواجهة الأمامية
- **React**: مكتبة JavaScript لبناء واجهات المستخدم
- **Tailwind CSS**: إطار عمل CSS للتصميم
- **Zustand**: مكتبة لإدارة الحالة
- **React Router**: للتنقل بين الصفحات
- **Leaflet**: مكتبة خرائط تفاعلية
- **Vite**: أداة بناء سريعة

### الواجهة الخلفية
- **Node.js**: بيئة تشغيل JavaScript
- **Express.js**: إطار عمل لبناء واجهات برمجة التطبيقات (API)
- **MongoDB**: قاعدة بيانات NoSQL
- **Mongoose**: مكتبة نمذجة البيانات لـ MongoDB
- **JWT**: JSON Web Tokens للمصادقة
- **Multer**: لتحميل الملفات

## متطلبات التشغيل

- Node.js (الإصدار 14 أو أحدث)
- MongoDB (محلي أو Atlas)

## التثبيت والتشغيل

### الواجهة الأمامية

1. قم بتثبيت الاعتماديات:
   ```bash
   npm install
   ```

2. قم بتشغيل خادم التطوير:
   ```bash
   npm run dev
   ```

### الواجهة الخلفية

1. انتقل إلى مجلد الواجهة الخلفية:
   ```bash
   cd backend
   ```

2. قم بتثبيت الاعتماديات:
   ```bash
   npm install
   ```

3. قم بإنشاء ملف `.env` وأضف المتغيرات البيئية:
   ```
   PORT=5000
   MONGODB_URI=mongodb+srv://<username>:<password>@cluster0.mongodb.net/jobscope
   JWT_SECRET=your_jwt_secret_key
   NODE_ENV=development
   ```

4. قم بإضافة البيانات الافتراضية (اختياري):
   ```bash
   npm run seed
   ```

5. قم بتشغيل خادم التطوير:
   ```bash
   npm run dev
   ```

## الاستخدام

- الواجهة الأمامية: http://localhost:5173
- الواجهة الخلفية: http://localhost:5000

## المساهمة

نرحب بالمساهمات! يرجى اتباع الخطوات التالية:

1. قم بعمل fork للمشروع
2. قم بإنشاء فرع جديد (`git checkout -b feature/amazing-feature`)
3. قم بإجراء التغييرات
4. قم بعمل commit للتغييرات (`git commit -m 'Add some amazing feature'`)
5. قم بدفع التغييرات إلى الفرع (`git push origin feature/amazing-feature`)
6. قم بفتح طلب سحب (Pull Request)
