import React, { useEffect, useState } from "react";
import { Star, MapPin, Clock, Calendar } from "lucide-react";
import Card from "../../../components/common/Card";
import useThemeStore from "../../../store/themeStore";
import SimpleLazyImage from "../../../components/common/SimpleLazyImage";
import { isAvailableNow } from "../../../utils/availabilityUtils";
import { getRelatedSpecializations } from "../../../data/professionsData";

// دالة لتحويل الوقت من نظام 24 ساعة إلى نظام 12 ساعة
const formatTime12Hour = (timeString) => {
  if (!timeString) return "-";

  const [hours, minutes] = timeString.split(":");
  const hour = parseInt(hours, 10);
  const ampm = hour >= 12 ? "مساءً" : "صباحاً";
  const hour12 = hour % 12 || 12;

  return `${hour12}:${minutes} ${ampm}`;
};

// تعريف تأثيرات النبض للحالة
const pulseStyles = `
@keyframes pulse-green {
  0% {
    box-shadow: 0 0 0 0 rgba(34, 197, 94, 0.7);
  }
  70% {
    box-shadow: 0 0 0 8px rgba(34, 197, 94, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(34, 197, 94, 0);
  }
}

@keyframes pulse-red {
  0% {
    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7);
  }
  70% {
    box-shadow: 0 0 0 8px rgba(239, 68, 68, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0);
  }
}
`;

const ProfileHeader = ({ user, userType }) => {
  const darkMode = useThemeStore((state) => state.darkMode);
  const [isAvailable, setIsAvailable] = useState(false);
  const [currentTime, setCurrentTime] = useState(new Date());

  // ترتيب أيام الأسبوع للعرض
  const orderedDays = [
    ["saturday", "السبت"],
    ["sunday", "الأحد"],
    ["monday", "الإثنين"],
    ["tuesday", "الثلاثاء"],
    ["wednesday", "الأربعاء"],
    ["thursday", "الخميس"],
    ["friday", "الجمعة"],
  ];

  // إضافة تأثيرات النبض إلى الصفحة
  useEffect(() => {
    const styleElement = document.createElement("style");
    styleElement.textContent = pulseStyles;
    document.head.appendChild(styleElement);

    return () => {
      document.head.removeChild(styleElement);
    };
  }, []);

  // تحديث الوقت الحالي كل 10 ثوانٍ للتحقق من توفر الحرفي
  useEffect(() => {
    // دالة للتحقق من توفر الحرفي
    const checkAvailability = () => {
      console.log("التحقق من توفر الحرفي...");
      if (user) {
        if (user.workingHours) {
          const available = isAvailableNow(user.workingHours);
          console.log("نتيجة التحقق من التوفر (workingHours):", available);
          setIsAvailable(available);
        } else if (user.workingHoursArray) {
          const available = isAvailableNow(user.workingHoursArray);
          console.log("نتيجة التحقق من التوفر (workingHoursArray):", available);
          setIsAvailable(available);
        } else {
          console.log("لا توجد بيانات ساعات عمل للحرفي");
          setIsAvailable(false);
        }
      }
    };

    // التحقق من توفر الحرفي عند تحميل المكون
    checkAvailability();

    // تحديث الوقت كل 10 ثوانٍ
    const intervalId = setInterval(() => {
      const now = new Date();
      setCurrentTime(now);
      checkAvailability();
    }, 10000); // 10000 مللي ثانية = 10 ثوانٍ

    return () => clearInterval(intervalId);
  }, [user]);

  return (
    <Card
      className={`overflow-hidden rounded-xl shadow-xl ${
        darkMode
          ? "bg-gray-800 text-gray-200 border border-gray-700"
          : "bg-gradient-to-br from-blue-50/80 to-indigo-50 border border-indigo-100"
      } transition-colors duration-300`}
    >
      {/* هيدر البطاقة مع خلفية متدرجة */}
      <div className="relative">
        {/* صورة الخلفية الضبابية */}
        <div
          className={`absolute inset-0 h-48 ${
            darkMode ? "bg-gray-900" : "bg-gray-100"
          } overflow-hidden`}
        >
          {/* صورة الخلفية الضبابية */}
          <div
            className="absolute inset-0 bg-center bg-cover filter blur-md opacity-50"
            style={{
              backgroundImage: darkMode
                ? `url('https://images.unsplash.com/photo-1478760329108-5c3ed9d495a0?q=80&w=1000&auto=format&fit=crop')`
                : `url('https://images.unsplash.com/photo-1557682224-5b8590cd9ec5?q=80&w=1000&auto=format&fit=crop')`,
              transform: "scale(1.1)",
            }}
          ></div>
          {/* طبقة تراكب للتحكم في الألوان */}
          <div
            className={`absolute inset-0 ${
              darkMode
                ? "bg-gradient-to-br from-black/90 via-indigo-950/80 to-black/90"
                : "bg-gradient-to-br from-indigo-950/80 via-blue-900/70 to-indigo-950/80"
            }`}
          ></div>
          {/* أشكال زخرفية */}
          <div className="absolute inset-0 overflow-hidden">
            {/* دوائر زخرفية متحركة */}
            <div className="absolute top-5 right-10 w-24 h-24 rounded-full bg-white opacity-5 animate-pulse"></div>
            <div
              className="absolute top-20 right-20 w-16 h-16 rounded-full bg-white opacity-5 animate-pulse"
              style={{ animationDelay: "1s" }}
            ></div>
            <div
              className="absolute bottom-5 left-10 w-20 h-20 rounded-full bg-white opacity-5 animate-pulse"
              style={{ animationDelay: "1.5s" }}
            ></div>

            {/* خطوط زخرفية */}
            <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-white to-transparent opacity-20"></div>
            <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-white to-transparent opacity-20"></div>
          </div>

          {/* تأثير التدرج */}
          <div
            className={`absolute inset-0 ${
              darkMode
                ? "bg-gradient-to-t from-gray-900/90 via-gray-900/50 to-transparent"
                : "bg-gradient-to-t from-indigo-500 via-indigo-800 to-transparent"
            }`}
          ></div>

          {/* نص حالة التوفر للحرفي */}
          {userType === "craftsman" && (
            <div
              className={`absolute bottom-2 right-2 px-4 py-1.5 rounded-full text-center text-xs font-bold shadow-md border border-white/30 flex items-center justify-center ${
                isAvailable
                  ? "bg-gradient-to-r from-green-600 to-green-500 text-white"
                  : "bg-gradient-to-r from-red-600 to-red-500 text-white"
              }`}
              style={{
                zIndex: 20,
                minWidth: "80px",
                animation: isAvailable
                  ? "pulse-green 2s infinite"
                  : "pulse-red 2s infinite",
              }}
            >
              {isAvailable ? <>متاح الآن</> : <>غير متاح</>}
            </div>
          )}
        </div>

        {/* صورة الملف الشخصي - موضوعة في المنتصف وإلى الأعلى قليلاً */}
        <div
          className="relative px-6 py-4 flex justify-center"
          style={{ height: "180px" }}
        >
          <div className="absolute z-10 top-6 w-40 h-40 flex items-center justify-center">
            {/* حلقات خارجية متحركة */}
            <div className="absolute inset-0 rounded-full border-4 border-blue-300/40 animate-pulse"></div>
            <div
              className="absolute inset-0 rounded-full border-2 border-indigo-400/30 animate-pulse"
              style={{ animationDuration: "3s" }}
            ></div>

            {/* تأثير الهالة */}
            <div className="absolute inset-0 rounded-full bg-indigo-500/20 filter blur-md"></div>

            {/* الصورة الرئيسية */}
            <div
              className="relative w-36 h-36 rounded-full overflow-hidden border-4 transform hover:scale-105 transition-all duration-500 bg-white"
              style={{
                borderColor: darkMode ? "#4338C8" : "#6366f1",
                boxShadow: darkMode
                  ? "0 0 25px rgba(67, 56, 200, 0.5), inset 0 0 15px rgba(79, 70, 229, 0.2)"
                  : "0 0 30px rgba(79, 70, 229, 0.4), inset 0 0 10px rgba(99, 102, 241, 0.1)",
              }}
            >
              {/* تأثير الإضاءة */}
              <div className="absolute inset-0 bg-gradient-to-tr from-indigo-500/10 to-transparent"></div>

              <SimpleLazyImage
                src={
                  user.tempImage || // استخدام الصورة المؤقتة إذا كانت موجودة
                  user.image ||
                  user.profilePicture ||
                  (user.id || user._id
                    ? `/uploads/profile-${user.id || user._id}.png`
                    : null) ||
                  "/img/default-avatar-2-modified.svg"
                }
                alt={user.name}
                className="w-full h-full object-cover"
                placeholderClassName="w-full h-full bg-gray-200 animate-pulse"
              />

              {/* تأثير التوهج عند التحويم */}
              <div className="absolute inset-0 bg-indigo-500/0 hover:bg-indigo-500/10 transition-colors duration-300"></div>
            </div>
          </div>
        </div>
      </div>
      {/* محتوى البطاقة */}
      <div className="relative mt-16 px-6 pb-6">
        <div className="text-center mb-6">
          {/* الاسم مع تأثير خط تحته */}
          <div className="relative inline-block">
            <h1
              className={`text-2xl font-bold mb-1 ${
                darkMode ? "text-indigo-300" : "text-indigo-800"
              } transition-colors duration-300 relative z-10`}
            >
              {user.name}
            </h1>
            <span
              className={`absolute bottom-0 left-0 right-0 h-1 ${
                darkMode ? "bg-indigo-600" : "bg-indigo-400"
              } opacity-40 transform -rotate-1 z-0 rounded-full`}
            ></span>
          </div>

          {userType === "craftsman" && (
            <>
              {/* المهن والتخصصات في بطاقة مميزة */}
              <div
                className={`mt-4 mx-auto max-w-xs rounded-lg p-3 ${
                  darkMode
                    ? "bg-indigo-900/30 border border-indigo-800/50"
                    : "bg-indigo-50/80 border border-indigo-200"
                } transition-all duration-300 transform hover:scale-105 relative`}
              >
                {user.professions && user.specializations ? (
                  <div className="space-y-2">
                    {user.professions.map((profession, index) => {
                      // الحصول على التخصصات المرتبطة بهذه المهنة
                      // تم إزالة رسائل التصحيح المتكررة

                      // استخدام الدالة المشتركة للحصول على التخصصات المرتبطة
                      const relatedSpecializations = getRelatedSpecializations(
                        profession,
                        user.specializations
                      );

                      // إذا لم يتم العثور على تخصصات مرتبطة، عرض رسالة "لا توجد تخصصات"
                      // لا نستخدم جميع التخصصات لأننا نريد عرض التخصصات المرتبطة بكل مهنة فقط

                      return (
                        <div key={index} className="flex flex-col">
                          <div
                            className={`font-bold ${
                              darkMode ? "text-indigo-300" : "text-indigo-700"
                            }`}
                          >
                            {profession}
                          </div>
                          <div
                            className={`text-sm mt-1 ${
                              darkMode
                                ? "text-indigo-400/80"
                                : "text-indigo-600/80"
                            }`}
                          >
                            {relatedSpecializations.length > 0
                              ? relatedSpecializations.join(" • ")
                              : "لا توجد تخصصات"}
                          </div>
                        </div>
                      );
                    })}
                  </div>
                ) : (
                  <div className="flex flex-col">
                    <div
                      className={`font-bold ${
                        darkMode ? "text-indigo-300" : "text-indigo-700"
                      }`}
                    >
                      {user.profession}
                    </div>
                    {user.specialization && (
                      <div
                        className={`text-sm mt-1 ${
                          darkMode ? "text-indigo-400/80" : "text-indigo-600/80"
                        }`}
                      >
                        {user.specialization}
                      </div>
                    )}
                  </div>
                )}
              </div>

              {/* معلومات إضافية */}
              <div className="mt-6">
                {/* التقييمات */}
                <div
                  className={`flex flex-col items-center justify-center p-4 rounded-xl ${
                    darkMode
                      ? "bg-gray-800/80 border border-indigo-900/50"
                      : "bg-gradient-to-br from-indigo-50 to-blue-50 border border-indigo-100"
                  } transition-all duration-300 transform hover:scale-105 shadow-md hover:shadow-lg`}
                >
                  <div className="text-sm font-medium mb-2 text-center">
                    <span
                      className={
                        darkMode ? "text-indigo-300" : "text-indigo-700"
                      }
                    >
                      التقييم العام
                    </span>
                  </div>

                  <div className="flex items-center justify-center mb-1">
                    {[1, 2, 3, 4, 5].map((star) => (
                      <Star
                        key={star}
                        size={20}
                        className={`${
                          star <= Math.round(user.rating || 0)
                            ? darkMode
                              ? "text-yellow-400 fill-yellow-400"
                              : "text-yellow-500 fill-yellow-500"
                            : darkMode
                            ? "text-gray-600"
                            : "text-gray-300"
                        } transition-all duration-300 transform ${
                          star <= Math.round(user.rating || 0)
                            ? "scale-110"
                            : ""
                        }`}
                      />
                    ))}
                  </div>

                  <div className="flex items-center justify-center">
                    <span
                      className={`font-bold text-xl ${
                        darkMode ? "text-yellow-400" : "text-yellow-600"
                      } transition-colors duration-300`}
                    >
                      {user.rating ? parseFloat(user.rating).toFixed(1) : "0.0"}
                    </span>
                    <span
                      className={`mr-1 text-sm ${
                        darkMode ? "text-gray-400" : "text-gray-500"
                      } transition-colors duration-300`}
                    >
                      /5
                    </span>
                    <span
                      className={`mr-2 ${
                        darkMode ? "text-indigo-400" : "text-indigo-500"
                      } transition-colors duration-300 text-sm`}
                    >
                      ({user.reviewCount || 0} تقييم)
                    </span>
                  </div>
                </div>
              </div>

              {/* أوقات الدوام */}
              {(user.workingHours || user.workingHoursArray) && (
                <div
                  className={`mt-6 rounded-xl overflow-hidden ${
                    darkMode
                      ? "bg-gray-800 border border-gray-700"
                      : "bg-white border border-indigo-200"
                  } shadow-md`}
                >
                  {/* العنوان */}
                  <div
                    className={`py-3 px-4 flex items-center justify-center border-b ${
                      darkMode
                        ? "bg-indigo-900/30 border-indigo-800/50"
                        : "bg-indigo-100/50 border-indigo-200"
                    }`}
                  >
                    <Calendar
                      size={18}
                      className={`${
                        darkMode ? "text-indigo-300" : "text-indigo-600"
                      } ml-2`}
                    />
                    <div
                      className={`text-base font-bold ${
                        darkMode ? "text-indigo-300" : "text-indigo-700"
                      }`}
                    >
                      أوقات الدوام
                    </div>
                  </div>

                  {/* محتوى القسم */}
                  <div className="p-0">
                    {/* عرض أيام الأسبوع مع ساعات العمل */}
                    {user.workingHoursArray &&
                    Array.isArray(user.workingHoursArray) &&
                    user.workingHoursArray.length > 0 ? (
                      <div
                        className={`p-4 ${
                          darkMode ? "bg-indigo-900/10" : "bg-blue-50"
                        }`}
                      >
                        <div className="grid grid-cols-1 gap-2">
                          {orderedDays
                            .map(([dayKey, arabicDay]) => {
                              // البحث عن اليوم في workingHoursArray
                              const hours = user.workingHoursArray.find(
                                (item) => item.day === dayKey && item.isWorking
                              );

                              // عرض فقط الأيام التي تم اختيارها كأيام عمل
                              if (!hours || !hours.isWorking) {
                                return null; // لا تعرض هذا اليوم إذا لم يكن يوم عمل
                              }

                              return (
                                <div
                                  key={dayKey}
                                  className={`flex items-center justify-between py-3 ${
                                    darkMode
                                      ? "border-b border-indigo-900/20"
                                      : "border-b border-indigo-100"
                                  } last:border-b-0`}
                                >
                                  <span
                                    className={`font-medium text-right ${
                                      darkMode
                                        ? "text-indigo-300"
                                        : "text-indigo-700"
                                    }`}
                                  >
                                    {arabicDay}
                                  </span>

                                  <div
                                    className={`px-3 py-1 rounded-md text-center ${
                                      darkMode
                                        ? "bg-gray-800 text-indigo-300"
                                        : "bg-white text-indigo-700"
                                    } text-sm shadow-sm`}
                                  >
                                    {/* عرض ساعات العمل */}
                                    {hours.start || hours.from
                                      ? `${formatTime12Hour(
                                          hours.start || hours.from
                                        )} - ${formatTime12Hour(
                                          hours.end || hours.to
                                        )}`
                                      : "ساعات غير محددة"}
                                  </div>
                                </div>
                              );
                            })
                            .filter((day) => day !== null)}
                        </div>
                      </div>
                    ) : user.workingHours ? (
                      <div
                        className={`p-4 ${
                          darkMode ? "bg-indigo-900/20" : "bg-blue-50"
                        }`}
                      >
                        <div className="grid grid-cols-1 gap-2">
                          {orderedDays
                            .filter(
                              ([day]) =>
                                user.workingHours[day] &&
                                user.workingHours[day].isWorking
                            )
                            .map(([day, arabicDay]) => {
                              const data = user.workingHours[day];

                              return (
                                <div
                                  key={day}
                                  className={`flex items-center justify-between py-3 ${
                                    darkMode
                                      ? "border-b border-indigo-900/20"
                                      : "border-b border-indigo-100"
                                  } last:border-b-0`}
                                >
                                  <span
                                    className={`font-medium text-right ${
                                      darkMode
                                        ? "text-indigo-300"
                                        : "text-indigo-700"
                                    }`}
                                  >
                                    {arabicDay}
                                  </span>

                                  <div
                                    className={`px-3 py-1 rounded-md text-center ${
                                      darkMode
                                        ? "bg-gray-800 text-indigo-300"
                                        : "bg-white text-indigo-700"
                                    } text-sm shadow-sm`}
                                  >
                                    {data.start || data.from
                                      ? `${formatTime12Hour(
                                          data.start || data.from
                                        )} - ${formatTime12Hour(
                                          data.end || data.to
                                        )}`
                                      : "ساعات غير محددة"}
                                  </div>
                                </div>
                              );
                            })}
                        </div>
                      </div>
                    ) : (
                      <div
                        className={`text-center p-3 ${
                          darkMode ? "text-gray-400" : "text-gray-600"
                        }`}
                      >
                        لم يتم تحديد أيام أو ساعات العمل
                      </div>
                    )}

                    {/* ملاحظة توضيحية */}
                    <div
                      className={`py-2 px-4 text-center border-t ${
                        darkMode
                          ? "text-gray-400 border-indigo-900/20"
                          : "text-gray-500 border-indigo-100"
                      } text-xs`}
                    >
                      <Clock size={12} className="inline-block ml-1" />
                      يتم تحديث حالة التوفر تلقائياً بناءً على أوقات الدوام
                    </div>
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </Card>
  );
};

// استخدام React.memo لتحسين الأداء
export default React.memo(ProfileHeader);
