import axios from "axios";
import { API_URL } from "./config";

// إنشاء نسخة من axios مع الإعدادات الافتراضية
const api = axios.create({
  baseURL: API_URL,
  headers: {
    "Content-Type": "application/json",
  },
  // إضافة مهلة للطلبات
  timeout: 100000, //
});

// إضافة معترض للطلبات لإضافة الرمز المميز إلى الرؤوس
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem("token");
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// خدمة المصادقة
const authService = {
  // إرسال رمز التحقق إلى رقم الهاتف
  sendOtpToPhone: async (phone) => {
    try {
      const response = await api.post("/auth/send-otp-phone", { phone });
      return response.data;
    } catch (error) {
      throw error.response?.data || { message: "فشل في إرسال رمز التحقق" };
    }
  },

  // التحقق من رمز التحقق (OTP) باستخدام رقم الهاتف فقط
  verifyOtp: async (phone, otp) => {
    try {
      const response = await api.post("/auth/verify-otp", { phone, otp });
      return response.data;
    } catch (error) {
      throw error.response?.data || { message: "رمز التحقق غير صحيح" };
    }
  },
  // تسجيل مستخدم جديد
  register: async (userData) => {
    try {
      console.log("Registering user:", userData);
      const response = await api.post("/auth/register", userData);
      console.log("Registration response:", response.data);

      if (response.data.token) {
        localStorage.setItem("token", response.data.token);
        localStorage.setItem("user", JSON.stringify(response.data.user));

        // إذا تم تحديد "تذكرني"، قم بتخزين وقت انتهاء الصلاحية
        if (userData.rememberMe) {
          const expiryDate = new Date();
          expiryDate.setDate(expiryDate.getDate() + 30); // 30 يوم
          localStorage.setItem("tokenExpiry", expiryDate.toISOString());
        }
      }
      return response.data;
    } catch (error) {
      console.error("Registration error:", error);
      throw error.response?.data || { message: "حدث خطأ أثناء التسجيل" };
    }
  },

  // تسجيل الدخول باستخدام Google
  googleLogin: async (accountType) => {
    try {
      console.log("Google login attempt for account type:", accountType);

      // في الوضع الحقيقي، سنقوم بفتح نافذة منبثقة لتسجيل الدخول باستخدام Google
      // ثم نتلقى رمز المصادقة ونرسله إلى الخادم
      // لكن في الوضع الوهمي، سنقوم بمحاكاة الاستجابة

      // محاكاة تأخير الشبكة
      await new Promise((resolve) => setTimeout(resolve, 1500));

      // التحقق من وجود حساب مرتبط بحساب Google
      const mockGoogleAccounts = {
        client: {
          exists: true,
          user: {
            id: 101,
            name: "رامي سعيد",
            phone: "+************",
            email: "<EMAIL>",
            userType: "client",
            googleId: "g-12345",
            image: "https://randomuser.me/api/portraits/men/6.jpg",
          },
        },
        craftsman: {
          exists: true,
          user: {
            id: 102,
            name: "محمود علي",
            phone: "+************",
            email: "<EMAIL>",
            userType: "craftsman",
            googleId: "g-67890",
            profession: "سباك",
            specialization: "صيانة وتركيب",
            location: { lng: 36.28, lat: 33.51 },
            workRadius: 8,
            rating: 4.5,
            image: "https://randomuser.me/api/portraits/men/2.jpg",
          },
        },
      };

      const accountInfo = mockGoogleAccounts[accountType];

      if (accountInfo.exists) {
        // إذا كان الحساب موجودًا، قم بتسجيل الدخول
        // لا ننشئ توكن وهمي لأنه يسبب مشاكل مع الـ backend
        localStorage.setItem("user", JSON.stringify(accountInfo.user));
        console.log("تم تسجيل دخول Google محلياً بدون توكن وهمي");

        return {
          success: true,
          isNewAccount: false,
          token: null, // لا نرجع توكن وهمي
          user: accountInfo.user,
        };
      } else {
        // إذا لم يكن الحساب موجودًا، قم بإنشاء حساب جديد
        return {
          success: true,
          isNewAccount: true,
          googleProfile: {
            email: "<EMAIL>",
            name: "مستخدم جديد",
            picture: "https://randomuser.me/api/portraits/men/22.jpg",
            googleId:
              "g-" +
              Math.random()
                .toString(36)
                .substring(2),
          },
        };
      }
    } catch (error) {
      console.error("Google login error:", error);
      throw error.response?.data || {
        message: "حدث خطأ أثناء تسجيل الدخول باستخدام Google",
      };
    }
  },

  // ربط حساب Google بحساب موجود
  linkGoogleAccount: async (userId, googleToken) => {
    try {
      console.log("Linking Google account for user:", userId);

      // في الوضع الحقيقي، سنقوم بإرسال طلب إلى الخادم لربط الحساب
      // لكن في الوضع الوهمي، سنقوم بمحاكاة الاستجابة

      // محاكاة تأخير الشبكة
      await new Promise((resolve) => setTimeout(resolve, 1000));

      return {
        success: true,
        message: "تم ربط حساب Google بنجاح",
      };
    } catch (error) {
      console.error("Link Google account error:", error);
      throw error.response?.data || {
        message: "حدث خطأ أثناء ربط حساب Google",
      };
    }
  },

  // تسجيل الدخول
  login: async (credentials) => {
    try {
      console.log("تسجيل دخول بسيط:", credentials.email || credentials.phone);

      const response = await api.post("/auth/login", credentials);
      console.log("استجابة تسجيل الدخول:", response.data);

      if (response.data.token && response.data.user) {
        // حفظ التوكن فقط - باقي البيانات ستتم معالجتها في userStore
        localStorage.setItem("token", response.data.token);

        if (credentials.rememberMe) {
          const expiryDate = new Date();
          expiryDate.setDate(expiryDate.getDate() + 30);
          localStorage.setItem("tokenExpiry", expiryDate.toISOString());
        }

        return response.data;
      }

      throw { message: "بيانات الاعتماد غير صالحة" };
    } catch (error) {
      console.error("خطأ تسجيل الدخول:", error);
      throw error.response?.data || { message: "بيانات الاعتماد غير صالحة" };
    }
  },

  // إنشاء حساب أدمن جديد
  createAdminAccount: async (adminData) => {
    try {
      console.log("Creating admin account with:", adminData);
      const response = await api.post("/auth/admin/create", adminData);
      console.log("Admin account creation response:", response.data);
      return response.data;
    } catch (error) {
      console.error("Create admin account error:", error);
      if (error.response?.data?.message) {
        throw new Error(error.response.data.message);
      }
      throw new Error("حدث خطأ أثناء إنشاء حساب الأدمن");
    }
  },

  // تسجيل الدخول كمدير
  adminLogin: async (credentials) => {
    try {
      console.log("Admin login with:", credentials);
      const response = await api.post("/auth/admin/login", credentials);
      console.log("Admin login response:", response.data);

      if (response.data.token) {
        localStorage.setItem("adminToken", response.data.token);
        localStorage.setItem("admin", JSON.stringify(response.data.admin));

        // إذا تم تحديد "تذكرني"، قم بتخزين وقت انتهاء الصلاحية
        if (credentials.rememberMe) {
          const expiryDate = new Date();
          expiryDate.setDate(expiryDate.getDate() + 30); // 30 يوم
          localStorage.setItem("adminTokenExpiry", expiryDate.toISOString());
        }
      }
      return response.data;
    } catch (error) {
      console.error("Admin login error:", error);
      throw error.response?.data || {
        message: "حدث خطأ أثناء تسجيل الدخول كمدير",
      };
    }
  },

  // تغيير كلمة مرور الأدمن
  changeAdminPassword: async (passwordData) => {
    try {
      console.log("Changing admin password...");
      const token = localStorage.getItem("adminToken");
      const response = await api.put(
        "/auth/admin/change-password",
        passwordData,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      console.log("Admin password change response:", response.data);
      return response.data;
    } catch (error) {
      console.error("Change admin password error:", error);
      if (error.response?.data?.message) {
        throw new Error(error.response.data.message);
      }
      throw new Error("حدث خطأ أثناء تغيير كلمة المرور");
    }
  },

  // تسجيل الخروج
  logout: () => {
    // إزالة جميع البيانات المتعلقة بالمستخدم من التخزين المحلي
    localStorage.removeItem("token");
    localStorage.removeItem("user");
    localStorage.removeItem("userData"); // إضافة حذف userData
    localStorage.removeItem("userType"); // إضافة حذف userType
    localStorage.removeItem("userId"); // إضافة حذف userId
    localStorage.removeItem("tokenExpiry");
    localStorage.removeItem("authVerified"); // حذف علامة التحقق من المصادقة
    localStorage.removeItem("auth_error"); // حذف أي أخطاء مصادقة

    // إزالة أي بيانات إضافية قد تكون مخزنة
    localStorage.removeItem("jobscope-user-storage");

    // تنظيف ذاكرة التخزين المؤقت للجلسة
    sessionStorage.clear();

    console.log("تم تنظيف بيانات المستخدم من authService");

    // لا نقوم بإعادة توجيه المستخدم تلقائيًا، بل نترك ذلك للمكون الذي يستدعي هذه الدالة
    // window.location.href = "/login";
  },

  // تسجيل الخروج كمدير
  adminLogout: () => {
    localStorage.removeItem("adminToken");
    localStorage.removeItem("admin");
    localStorage.removeItem("adminTokenExpiry");
    window.location.href = "/admin/login";
  },

  // الحصول على المستخدم الحالي
  getCurrentUser: async () => {
    try {
      console.log("جلب بيانات المستخدم الحالي");

      const response = await api.get("/auth/me");
      console.log("استجابة المستخدم الحالي:", response.data);

      return response.data;
    } catch (error) {
      console.error("خطأ في جلب بيانات المستخدم:", error);
      throw error;
    }
  },

  // التحقق مما إذا كان المستخدم مسجل الدخول
  isLoggedIn: () => {
    const token = localStorage.getItem("token");
    return !!token;
  },

  // التحقق مما إذا كان المدير مسجل الدخول
  isAdminLoggedIn: () => {
    const token = localStorage.getItem("adminToken");
    const admin = localStorage.getItem("admin");
    return !!(token && admin);
  },

  // الحصول على المستخدم من التخزين المحلي
  getUser: () => {
    const user = localStorage.getItem("user");
    return user ? JSON.parse(user) : null;
  },

  // الحصول على المدير من التخزين المحلي
  getAdmin: () => {
    const admin = localStorage.getItem("admin");
    return admin ? JSON.parse(admin) : null;
  },

  // التحقق من وجود حساب بنفس البريد الإلكتروني
  checkEmailExists: async (email) => {
    try {
      console.log("Checking if email exists:", email);

      // إرسال طلب إلى الخادم للتحقق من وجود البريد الإلكتروني
      const response = await api.get(
        `/auth/check-email?email=${encodeURIComponent(email)}`
      );
      console.log("Check email response:", response.data);
      return response.data;
    } catch (error) {
      console.error("Check email error:", error);

      // في حالة فشل الاتصال بالخادم، نفترض أن البريد الإلكتروني موجود
      // هذا يسمح للمستخدم بمحاولة تسجيل الدخول على أي حال
      console.log("Falling back to default behavior - assuming email exists");

      return {
        exists: true,
        message: "تعذر التحقق من البريد الإلكتروني، سنفترض أنه موجود",
      };
    }
  },

  // التحقق من وجود حساب بنفس رقم الهاتف
  checkPhoneExists: async (phone) => {
    try {
      console.log("Checking if phone exists:", phone);
      const response = await api.get(
        `/auth/check-phone?phone=${encodeURIComponent(phone)}`
      );
      console.log("Check phone response:", response.data);
      return response.data;
    } catch (error) {
      console.error("Check phone error:", error);
      throw error.response?.data || {
        message: "حدث خطأ أثناء التحقق من رقم الهاتف",
      };
    }
  },

  // تسجيل مستخدم تم إنشاؤه باستخدام Firebase
  registerFirebaseUser: async (userData) => {
    try {
      console.log("Registering Firebase user:", userData);

      // التأكد من وجود جميع البيانات المطلوبة
      if (!userData.uid) {
        throw new Error("معرف المستخدم (uid) مطلوب");
      }

      // إضافة معلومات إضافية للتشخيص
      console.log("API URL:", API_URL);

      // تخطي محاولة النقطة النهائية الجديدة والانتقال مباشرة إلى النقطة النهائية القديمة
      console.log("Using legacy endpoint directly: /auth/register-firebase");

      try {
        const response = await api.post("/auth/register-firebase", userData, {
          timeout: 15000,
          headers: {
            "Content-Type": "application/json",
          },
        });

        console.log("Firebase registration response (legacy):", response.data);

        if (response.data.token) {
          // حفظ التوكن الحقيقي من الـ backend
          localStorage.setItem("token", response.data.token);
          localStorage.setItem("user", JSON.stringify(response.data.user));

          // تعيين وقت انتهاء الصلاحية (30 يوم)
          const expiryDate = new Date();
          expiryDate.setDate(expiryDate.getDate() + 30);
          localStorage.setItem("tokenExpiry", expiryDate.toISOString());
          console.log("تم حفظ توكن حقيقي من الـ backend");
        }

        return response.data;
      } catch (error) {
        console.error("Legacy endpoint failed:", error);
        throw error; // إعادة رمي الخطأ للتعامل معه في الوضع الطارئ
      }
    } catch (error) {
      console.error("Firebase registration error:", error);
      console.error("Error details:", {
        message: error.message,
        response: error.response
          ? {
              status: error.response.status,
              data: error.response.data,
            }
          : "No response",
        request: error.request
          ? "Request sent but no response received"
          : "Request setup failed",
      });

      // في حالة الفشل، نستخدم وضع الطوارئ
      console.log("Using emergency mode for user registration");

      // إضافة معرف للمستخدم إذا لم يكن موجودًا
      const userWithId = {
        ...userData,
        // استخدام معرف Firebase كمعرف للمستخدم
        _id: userData.uid || `temp-${Date.now()}`,
      };

      console.log("User data with ID in emergency mode:", userWithId);

      // تخزين بيانات المستخدم محليًا
      localStorage.setItem("user", JSON.stringify(userWithId));

      // لا ننشئ توكن وهمي لأنه يسبب مشاكل مع الـ backend
      // سيتم التعامل مع المصادقة من خلال الـ backend فقط
      console.log("تم تسجيل المستخدم محلياً بدون توكن وهمي");

      return {
        success: true,
        user: userWithId,
        token: null, // لا نرجع توكن وهمي
        message: "تم تسجيل المستخدم بنجاح (وضع الطوارئ)",
      };
    }
  },

  // إعادة تعيين كلمة المرور
  resetPassword: async (phone, newPassword) => {
    try {
      console.log("Resetting password for phone:", phone);
      const response = await api.post("/auth/reset-password", {
        phone,
        newPassword,
      });
      console.log("Reset password response:", response.data);
      return response.data;
    } catch (error) {
      console.error("Reset password error:", error);
      throw error.response?.data || {
        message: "حدث خطأ أثناء إعادة تعيين كلمة المرور",
      };
    }
  },
};

export default authService;
