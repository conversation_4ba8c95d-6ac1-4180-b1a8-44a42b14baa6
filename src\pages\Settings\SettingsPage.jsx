import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { useNavigate } from "react-router-dom";
import Layout from "../../components/layout/Layout";
import Card from "../../components/common/Card";
import Button from "../../components/common/Button";
import ConfirmationToast from "../../components/common/ConfirmationToast";
import useUserStore from "../../store/userStore";
import useThemeStore from "../../store/themeStore";
import useLanguageStore from "../../store/languageStore";
import LoginRedirect from "../../components/auth/LoginRedirect";
import userService from "../../services/userService";
import { showToast } from "../../utils/toastUtils";
import {
  Lock,
  LogOut,
  Trash2,
  Bell,
  Moon,
  AlertCircle,
  Info,
  Eye,
  EyeOff,
} from "lucide-react";
import settingsTranslations from "../../translations/settingsTranslations";

const SettingsPage = () => {
  const navigate = useNavigate();
  const user = useUserStore((state) => state.user);
  const logout = useUserStore((state) => state.logout);

  const language = useLanguageStore((state) => state.language);
  const [notifications, setNotifications] = useState(true);
  const darkMode = useThemeStore((state) => state.darkMode);
  const toggleDarkMode = useThemeStore((state) => state.toggleDarkMode);

  // التحقق من حالة المصادقة عند تحميل الصفحة
  useEffect(() => {
    // التحقق مما إذا كان قد تم التحقق من المصادقة بالفعل
    const authVerified = localStorage.getItem("authVerified");
    const token = localStorage.getItem("token");

    // إذا لم يكن هناك توكن أو لم يتم التحقق من المصادقة بعد، لا نقوم بتحميل البيانات
    if (!token || authVerified !== "true") {
      console.log(
        "لم يتم التحقق من المصادقة بعد، تخطي تحميل بيانات صفحة الإعدادات"
      );
      return;
    }

    console.log("تم التحقق من المصادقة مسبقًا، تحميل بيانات صفحة الإعدادات");
  }, []);

  // Get translations based on current language
  const t = settingsTranslations[language];
  const [showPasswordModal, setShowPasswordModal] = useState(false);
  const [showDeleteConfirmation, setShowDeleteConfirmation] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [isChangingPassword, setIsChangingPassword] = useState(false);
  const [passwordData, setPasswordData] = useState({
    currentPassword: "",
    newPassword: "",
    confirmPassword: "",
  });
  const [passwordErrors, setPasswordErrors] = useState({});
  const [showPasswords, setShowPasswords] = useState({
    currentPassword: false,
    newPassword: false,
    confirmPassword: false,
  });

  // التحقق من نوع المستخدم (Supabase أم عادي)
  const isSupabaseUser = user?.firebaseUid || user?.supabaseUid;

  const handleNotificationsToggle = () => {
    setNotifications(!notifications);
  };

  const handleDarkModeToggle = () => {
    toggleDarkMode();
  };

  const handlePasswordInputChange = (e) => {
    const { name, value } = e.target;
    setPasswordData({
      ...passwordData,
      [name]: value,
    });

    // Clear error when user types
    if (passwordErrors[name]) {
      setPasswordErrors({
        ...passwordErrors,
        [name]: "",
      });
    }
  };

  const togglePasswordVisibility = (field) => {
    setShowPasswords({
      ...showPasswords,
      [field]: !showPasswords[field],
    });
  };

  const validatePasswordForm = () => {
    let isValid = true;
    const newErrors = {};

    if (!passwordData.currentPassword) {
      newErrors.currentPassword = t.currentPasswordRequired;
      isValid = false;
    }

    if (!passwordData.newPassword) {
      newErrors.newPassword = t.newPasswordRequired;
      isValid = false;
    } else if (passwordData.newPassword.length < 6) {
      newErrors.newPassword = t.passwordMinLength;
      isValid = false;
    }

    if (!passwordData.confirmPassword) {
      newErrors.confirmPassword = t.confirmPasswordRequired;
      isValid = false;
    } else if (passwordData.newPassword !== passwordData.confirmPassword) {
      newErrors.confirmPassword = t.passwordsNotMatch;
      isValid = false;
    }

    setPasswordErrors(newErrors);
    return isValid;
  };

  // وظيفة التحقق الخاصة بمستخدمي Supabase (لا تتطلب كلمة المرور الحالية)
  const validatePasswordFormForSupabase = () => {
    const newErrors = {};
    let isValid = true;

    if (!passwordData.newPassword) {
      newErrors.newPassword = t.newPasswordRequired;
      isValid = false;
    } else if (passwordData.newPassword.length < 6) {
      newErrors.newPassword = t.passwordMinLength;
      isValid = false;
    }

    if (!passwordData.confirmPassword) {
      newErrors.confirmPassword = t.confirmPasswordRequired;
      isValid = false;
    } else if (passwordData.newPassword !== passwordData.confirmPassword) {
      newErrors.confirmPassword = t.passwordsNotMatch;
      isValid = false;
    }

    setPasswordErrors(newErrors);
    return isValid;
  };

  const handlePasswordSubmit = async (e) => {
    e.preventDefault();

    // للمستخدمين المسجلين عبر Supabase، لا نحتاج للتحقق من كلمة المرور الحالية
    const isValidForm = isSupabaseUser
      ? validatePasswordFormForSupabase()
      : validatePasswordForm();

    if (isValidForm) {
      try {
        setIsChangingPassword(true);

        // إرسال طلب تغيير كلمة المرور إلى الخادم
        const requestData = isSupabaseUser
          ? { newPassword: passwordData.newPassword } // لا نرسل كلمة المرور الحالية لمستخدمي Supabase
          : {
              currentPassword: passwordData.currentPassword,
              newPassword: passwordData.newPassword,
            };

        const response = await userService.changePassword(requestData);

        // Close modal and reset form
        setShowPasswordModal(false);
        setPasswordData({
          currentPassword: "",
          newPassword: "",
          confirmPassword: "",
        });

        // Show success message using Toast
        showToast("تم تغيير كلمة المرور بنجاح", "success", 2000);

        // إذا كانت الاستجابة تتطلب إعادة المصادقة، قم بتسجيل الخروج
        if (response.requireReauth) {
          console.log(
            "Password changed successfully, logging out for re-authentication"
          );

          // انتظار قليل لإظهار Toast قبل تسجيل الخروج
          setTimeout(() => {
            // تسجيل الخروج من المخزن المحلي
            logout();

            // الانتقال إلى صفحة تسجيل الدخول مع رسالة
            navigate("/login", {
              state: {
                message:
                  "تم تغيير كلمة المرور بنجاح. يرجى تسجيل الدخول مرة أخرى بكلمة المرور الجديدة.",
              },
            });
          }, 2500); // انتظار أطول قليلاً من مدة Toast
        }
      } catch (error) {
        console.error("Error changing password:", error);
        showToast(
          error.message || "حدث خطأ أثناء تغيير كلمة المرور",
          "error",
          3000
        );
      } finally {
        setIsChangingPassword(false);
      }
    }
  };

  // وظيفة حذف الحساب
  const handleDeleteAccount = async () => {
    try {
      setIsDeleting(true);

      // استدعاء خدمة حذف الحساب
      await userService.deleteMyAccount();

      // تسجيل الخروج وتنظيف البيانات
      logout();

      // إعادة توجيه إلى الصفحة الرئيسية
      window.location.href = "/";
    } catch (error) {
      console.error("Error deleting account:", error);
      showToast(error.message || "حدث خطأ أثناء حذف الحساب", "error", 3000);
    } finally {
      setIsDeleting(false);
    }
  };

  if (!user) {
    return <LoginRedirect />;
  }

  return (
    <Layout user={user} onLogout={logout}>
      <div
        className={`${
          darkMode
            ? "bg-gray-900 text-white"
            : "bg-gradient-to-b from-indigo-50 to-white text-black"
        } min-h-screen py-12 transition-colors duration-300`}
      >
        <div className="container mx-auto px-4">
          <motion.h1
            className="text-4xl font-bold mb-8 relative inline-block"
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <span className="relative z-10 text-indigo-800">{t.pageTitle}</span>
            <span className="absolute bottom-0 left-0 right-0 h-3 bg-indigo-500 opacity-30 transform -rotate-1 z-0"></span>
          </motion.h1>

          <motion.div
            className="grid grid-cols-1 md:grid-cols-2 gap-8"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            {/* Notification Settings */}
            <Card
              className={`p-6 rounded-xl ${
                darkMode
                  ? "bg-gray-800 text-gray-200 border border-gray-700"
                  : "bg-gradient-to-br from-blue-50/80 to-indigo-50 border border-indigo-100"
              } shadow-lg hover:shadow-xl transition-all duration-300`}
            >
              <div className="flex items-center gap-3 mb-4">
                <div
                  className={`w-12 h-12 rounded-full ${
                    darkMode
                      ? "bg-indigo-900/80 text-indigo-300"
                      : "bg-gradient-to-br from-yellow-400/20 to-blue-400/20 text-indigo-600 border border-indigo-200"
                  } flex items-center justify-center shadow-md`}
                >
                  <Bell size={20} />
                </div>
                <h2 className="text-xl font-bold text-indigo-800">
                  {t.notificationsTitle}
                </h2>
              </div>
              <div className="flex items-center justify-between mb-4">
                <span className={darkMode ? "text-gray-300" : "text-gray-700"}>
                  {t.enableNotifications}
                </span>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={notifications}
                    onChange={handleNotificationsToggle}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-indigo-300/20 rounded-full peer peer-checked:after:translate-x-[0.4rem] peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:right-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600"></div>
                </label>
              </div>
              <div
                className={`p-3 rounded-lg flex items-start gap-2 mt-2 ${
                  darkMode
                    ? "bg-gray-700/50 border border-gray-600"
                    : "bg-blue-50/70 border border-blue-100"
                }`}
              >
                <Info
                  className={`${
                    darkMode ? "text-blue-400" : "text-blue-500"
                  } mt-0.5 flex-shrink-0`}
                  size={18}
                />
                <p
                  className={`${
                    darkMode ? "text-gray-300" : "text-gray-600"
                  } text-sm`}
                >
                  {t.notificationsNote}
                </p>
              </div>
            </Card>

            {/* Appearance Settings */}
            <Card
              className={`p-6 rounded-xl ${
                darkMode
                  ? "bg-gray-800 text-gray-200 border border-gray-700"
                  : "bg-gradient-to-br from-blue-50/80 to-indigo-50 border border-indigo-100"
              } shadow-lg hover:shadow-xl transition-all duration-300`}
            >
              <div className="flex items-center gap-3 mb-4">
                <div
                  className={`w-12 h-12 rounded-full ${
                    darkMode
                      ? "bg-indigo-900/80 text-indigo-300"
                      : "bg-gradient-to-br from-purple-400/20 to-blue-400/20 text-indigo-600 border border-indigo-200"
                  } flex items-center justify-center shadow-md`}
                >
                  <Moon size={20} />
                </div>
                <h2 className="text-xl font-bold text-indigo-800">
                  {t.appearanceTitle}
                </h2>
              </div>
              <div className="flex items-center justify-between mb-4">
                <span className={darkMode ? "text-gray-300" : "text-gray-700"}>
                  {t.darkMode}
                </span>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={darkMode}
                    onChange={handleDarkModeToggle}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-indigo-300/20 rounded-full peer peer-checked:after:translate-x-[0.4rem] peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:right-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600"></div>
                </label>
              </div>
              <div
                className={`p-3 rounded-lg flex items-start gap-2 mt-2 ${
                  darkMode
                    ? "bg-gray-700/50 border border-gray-600"
                    : "bg-blue-50/70 border border-blue-100"
                }`}
              >
                <Info
                  className={`${
                    darkMode ? "text-blue-400" : "text-blue-500"
                  } mt-0.5 flex-shrink-0`}
                  size={18}
                />
                <p
                  className={`${
                    darkMode ? "text-gray-300" : "text-gray-600"
                  } text-sm`}
                >
                  {t.appearanceNote}
                </p>
              </div>
            </Card>

            {/* Security Settings */}
            <Card
              className={`p-6 rounded-xl ${
                darkMode
                  ? "bg-gray-800 text-gray-200 border border-gray-700"
                  : "bg-gradient-to-br from-blue-50/80 to-indigo-50 border border-indigo-100"
              } shadow-lg hover:shadow-xl transition-all duration-300`}
            >
              <div className="flex items-center gap-3 mb-4">
                <div
                  className={`w-12 h-12 rounded-full ${
                    darkMode
                      ? "bg-indigo-900/80 text-indigo-300"
                      : "bg-gradient-to-br from-blue-400/20 to-indigo-400/20 text-indigo-600 border border-indigo-200"
                  } flex items-center justify-center shadow-md`}
                >
                  <Lock size={20} />
                </div>
                <h2 className="text-xl font-bold text-indigo-800">
                  {t.securityTitle}
                </h2>
              </div>
              <div className="grid grid-cols-1 gap-4 mb-4">
                <Button
                  variant="primary"
                  className="w-full bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white flex items-center justify-center gap-2 shadow-md hover:shadow-lg transition-all duration-200 relative overflow-hidden group text-lg py-3 px-6 rounded-lg"
                  onClick={() => setShowPasswordModal(true)}
                >
                  <span className="relative z-10 flex items-center justify-center gap-2 font-medium">
                    <Lock size={18} />
                    {t.changePassword}
                  </span>
                  <span className="absolute inset-0 bg-white opacity-20 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700"></span>
                </Button>

                <Button
                  variant="primary"
                  className="w-full bg-gradient-to-r from-red-500 to-red-700 hover:from-red-600 hover:to-red-800 text-white flex items-center justify-center gap-2 shadow-md hover:shadow-lg transition-all duration-200 relative overflow-hidden group text-lg py-3 px-6 rounded-lg"
                  onClick={logout}
                >
                  <span className="relative z-10 flex items-center justify-center gap-2 font-medium">
                    <LogOut size={18} />
                    {t.logout}
                  </span>
                  <span className="absolute inset-0 bg-white opacity-20 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700"></span>
                </Button>
              </div>
              <div
                className={`p-3 rounded-lg flex items-start gap-2 mt-2 ${
                  darkMode
                    ? "bg-gray-700/50 border border-gray-600"
                    : "bg-blue-50/70 border border-blue-100"
                }`}
              >
                <Info
                  className={`${
                    darkMode ? "text-blue-400" : "text-blue-500"
                  } mt-0.5 flex-shrink-0`}
                  size={18}
                />
                <p
                  className={`${
                    darkMode ? "text-gray-300" : "text-gray-600"
                  } text-sm`}
                >
                  {isSupabaseUser
                    ? "هذا الحساب مسجل عبر Supabase. يمكنك تغيير كلمة المرور مباشرة من هنا دون الحاجة لإدخال كلمة المرور الحالية."
                    : t.securityNote}
                </p>
              </div>
            </Card>

            {/* Account Settings */}
            <Card
              className={`p-6 rounded-xl ${
                darkMode
                  ? "bg-gray-800 text-gray-200 border border-gray-700"
                  : "bg-gradient-to-br from-blue-50/80 to-indigo-50 border border-indigo-100"
              } shadow-lg hover:shadow-xl transition-all duration-300`}
            >
              <div className="flex items-center gap-3 mb-4">
                <div
                  className={`w-12 h-12 rounded-full ${
                    darkMode
                      ? "bg-red-900/80 text-red-300"
                      : "bg-gradient-to-br from-red-400/20 to-orange-400/20 text-red-600 border border-red-200"
                  } flex items-center justify-center shadow-md`}
                >
                  <Trash2 size={20} />
                </div>
                <h2 className="text-xl font-bold text-indigo-800">
                  {t.accountSettingsTitle}
                </h2>
              </div>
              <div className="grid grid-cols-1 gap-4 mb-4">
                <Button
                  variant="primary"
                  className="w-full bg-gradient-to-r from-red-500 to-red-700 hover:from-red-600 hover:to-red-800 text-white flex items-center justify-center gap-2 shadow-md hover:shadow-lg transition-all duration-200 relative overflow-hidden group text-lg py-3 px-6 rounded-lg"
                  onClick={() => setShowDeleteConfirmation(true)}
                  disabled={isDeleting}
                >
                  <span className="relative z-10 flex items-center justify-center gap-2 font-medium">
                    <Trash2 size={18} />
                    {isDeleting ? "جاري الحذف..." : t.deleteAccount}
                  </span>
                  <span className="absolute inset-0 bg-white opacity-20 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700"></span>
                </Button>
              </div>
              <div
                className={`p-3 rounded-lg flex items-start gap-2 mt-2 ${
                  darkMode
                    ? "bg-red-900/30 border border-red-800/50"
                    : "bg-red-50 border border-red-200"
                }`}
              >
                <AlertCircle
                  className={`${
                    darkMode ? "text-red-400" : "text-red-500"
                  } mt-0.5 flex-shrink-0`}
                  size={18}
                />
                <p
                  className={`${
                    darkMode ? "text-red-300" : "text-red-600"
                  } text-sm`}
                >
                  {t.deleteAccountWarning}
                </p>
              </div>
            </Card>
          </motion.div>
        </div>
      </div>

      {/* Change Password Modal */}
      {showPasswordModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[100] p-4">
          <motion.div
            className={`${
              darkMode
                ? "bg-gray-800 text-white border border-gray-700"
                : "bg-gradient-to-b from-indigo-50 to-white border border-indigo-100"
            } rounded-xl shadow-xl w-full max-w-md`}
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
          >
            <div className="p-6">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-bold text-indigo-800">
                  {t.passwordModalTitle}
                </h2>
                <button
                  onClick={() => setShowPasswordModal(false)}
                  className="text-gray-500 hover:text-gray-700"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-6 w-6"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M6 18L18 6M6 6l12 12"
                    />
                  </svg>
                </button>
              </div>

              <form onSubmit={handlePasswordSubmit}>
                {/* إخفاء حقل كلمة المرور الحالية للمستخدمين المسجلين عبر Supabase */}
                {!isSupabaseUser && (
                  <div className="mb-4">
                    <label className="block text-gray-700 font-medium mb-2">
                      {t.currentPassword}{" "}
                      <span className="text-red-500">*</span>
                    </label>
                    <div className="relative">
                      <input
                        type={
                          showPasswords.currentPassword ? "text" : "password"
                        }
                        name="currentPassword"
                        value={passwordData.currentPassword}
                        onChange={handlePasswordInputChange}
                        className={`input pl-10 text-left ${
                          passwordErrors.currentPassword
                            ? "border-red-500 focus:ring-red-500"
                            : ""
                        }`}
                        style={{ direction: "ltr" }}
                        required
                      />
                      <button
                        type="button"
                        onClick={() =>
                          togglePasswordVisibility("currentPassword")
                        }
                        className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
                      >
                        {showPasswords.currentPassword ? (
                          <EyeOff size={20} />
                        ) : (
                          <Eye size={20} />
                        )}
                      </button>
                    </div>
                    {passwordErrors.currentPassword && (
                      <p className="text-red-500 text-sm mt-1">
                        {passwordErrors.currentPassword}
                      </p>
                    )}
                  </div>
                )}

                {/* رسالة توضيحية لمستخدمي Supabase */}
                {isSupabaseUser && (
                  <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                    <p className="text-blue-700 text-sm">
                      <strong>ملاحظة:</strong> هذا الحساب مسجل عبر Supabase. لا
                      تحتاج لإدخال كلمة المرور الحالية.
                    </p>
                  </div>
                )}

                <div className="mb-4">
                  <label className="block text-gray-700 font-medium mb-2">
                    {t.newPassword} <span className="text-red-500">*</span>
                  </label>
                  <div className="relative">
                    <input
                      type={showPasswords.newPassword ? "text" : "password"}
                      name="newPassword"
                      value={passwordData.newPassword}
                      onChange={handlePasswordInputChange}
                      className={`input pl-10 text-left ${
                        passwordErrors.newPassword
                          ? "border-red-500 focus:ring-red-500"
                          : ""
                      }`}
                      style={{ direction: "ltr" }}
                      required
                    />
                    <button
                      type="button"
                      onClick={() => togglePasswordVisibility("newPassword")}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
                    >
                      {showPasswords.newPassword ? (
                        <EyeOff size={20} />
                      ) : (
                        <Eye size={20} />
                      )}
                    </button>
                  </div>
                  {passwordErrors.newPassword && (
                    <p className="text-red-500 text-sm mt-1">
                      {passwordErrors.newPassword}
                    </p>
                  )}
                </div>

                <div className="mb-4">
                  <label className="block text-gray-700 font-medium mb-2">
                    {t.confirmNewPassword}{" "}
                    <span className="text-red-500">*</span>
                  </label>
                  <div className="relative">
                    <input
                      type={showPasswords.confirmPassword ? "text" : "password"}
                      name="confirmPassword"
                      value={passwordData.confirmPassword}
                      onChange={handlePasswordInputChange}
                      className={`input pl-10 text-left ${
                        passwordErrors.confirmPassword
                          ? "border-red-500 focus:ring-red-500"
                          : ""
                      }`}
                      style={{ direction: "ltr" }}
                      required
                    />
                    <button
                      type="button"
                      onClick={() =>
                        togglePasswordVisibility("confirmPassword")
                      }
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
                    >
                      {showPasswords.confirmPassword ? (
                        <EyeOff size={20} />
                      ) : (
                        <Eye size={20} />
                      )}
                    </button>
                  </div>
                  {passwordErrors.confirmPassword && (
                    <p className="text-red-500 text-sm mt-1">
                      {passwordErrors.confirmPassword}
                    </p>
                  )}
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <Button
                    type="button"
                    variant="primary"
                    className="w-full bg-gradient-to-r from-red-500 to-red-700 hover:from-red-600 hover:to-red-800 text-white flex items-center justify-center gap-2 shadow-md hover:shadow-lg transition-all duration-200 relative overflow-hidden group py-3 px-4 rounded-lg"
                    onClick={() => setShowPasswordModal(false)}
                  >
                    <span className="relative z-10 font-medium">
                      {t.cancel}
                    </span>
                    <span className="absolute inset-0 bg-white opacity-20 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700"></span>
                  </Button>
                  <Button
                    type="submit"
                    variant="primary"
                    className="w-full bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white flex items-center justify-center gap-2 shadow-md hover:shadow-lg transition-all duration-200 relative overflow-hidden group py-3 px-4 rounded-lg"
                    disabled={isChangingPassword}
                  >
                    <span className="relative z-10 flex items-center justify-center gap-2 font-medium">
                      <Lock size={16} />
                      {isChangingPassword
                        ? "جاري التغيير..."
                        : t.changePassword}
                    </span>
                    <span className="absolute inset-0 bg-white opacity-20 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700"></span>
                  </Button>
                </div>
              </form>
            </div>
          </motion.div>
        </div>
      )}

      {/* رسالة تأكيد حذف الحساب */}
      <ConfirmationToast
        isOpen={showDeleteConfirmation}
        onClose={() => setShowDeleteConfirmation(false)}
        onConfirm={handleDeleteAccount}
        title="تأكيد حذف الحساب"
        message="هل أنت متأكد من حذف حسابك نهائياً؟ سيتم حذف جميع بياناتك ولا يمكن التراجع عن هذه العملية."
        confirmText="حذف الحساب نهائياً"
        cancelText="إلغاء"
        type="error"
      />
    </Layout>
  );
};

export default SettingsPage;
