/**
 * أدوات لإدارة عنوان الصفحة ديناميكياً
 */

import useSiteSettingsStore from '../store/siteSettingsStore';

/**
 * تحديث عنوان الصفحة بناءً على إعدادات الموقع
 * @param {string} pageTitle - عنوان الصفحة الفرعي (اختياري)
 */
export const updatePageTitle = (pageTitle = '') => {
  const settings = useSiteSettingsStore.getState().settings;
  const siteName = settings?.siteName || 'JobScope';
  
  if (pageTitle) {
    document.title = `${pageTitle} - ${siteName}`;
  } else {
    document.title = siteName;
  }
};

/**
 * تحديث عنوان الصفحة عند تغيير إعدادات الموقع
 */
export const initializeTitleUpdater = () => {
  // الاشتراك في تغييرات إعدادات الموقع
  useSiteSettingsStore.subscribe((state) => {
    if (state.settings?.siteName) {
      // تحديث العنوان الأساسي فقط إذا كان العنوان الحالي هو اسم الموقع فقط
      const currentTitle = document.title;
      const oldSiteName = currentTitle.includes(' - ') ? currentTitle.split(' - ').pop() : currentTitle;
      
      if (oldSiteName === 'JobScope' || oldSiteName === state.settings.siteName) {
        if (currentTitle.includes(' - ')) {
          const pageTitle = currentTitle.split(' - ')[0];
          document.title = `${pageTitle} - ${state.settings.siteName}`;
        } else {
          document.title = state.settings.siteName;
        }
      }
    }
  });
};

/**
 * الحصول على اسم الموقع الحالي
 * @returns {string} اسم الموقع
 */
export const getSiteName = () => {
  const settings = useSiteSettingsStore.getState().settings;
  return settings?.siteName || 'JobScope';
};
