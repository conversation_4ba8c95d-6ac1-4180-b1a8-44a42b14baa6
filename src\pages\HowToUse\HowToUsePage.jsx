import { useState, useEffect } from "react";
import React from "react"; // لا تحذف هذا السطر
import { motion } from "framer-motion";
import Layout from "../../components/layout/Layout";
import useThemeStore from "../../store/themeStore";
import useSiteSettingsStore from "../../store/siteSettingsStore";
import { updatePageTitle } from "../../utils/titleUtils";
import {
  User,
  Briefcase,
  Search,
  MapPin,
  Calendar,
  Star,
  MessageCircle,
  CheckCircle,
  ArrowRight,
  Camera,
  Shield,
  Zap,
  TrendingUp,
  Award,
} from "lucide-react";

const HowToUsePage = () => {
  const darkMode = useThemeStore((state) => state.darkMode);
  const { settings, fetchSettings } = useSiteSettingsStore();
  const [activeTab, setActiveTab] = useState("client");
  const [activeFeature, setActiveFeature] = useState(0);

  // وظيفة فتح الواتساب
  const handleWhatsApp = () => {
    // ضع رقمك هنا - مثال: 0945364616
    const phoneNumber = "963945364616"; // تم تحويل الرقم للصيغة الدولية
    const message = `مرحباً! أحتاج مساعدة في استخدام منصة ${settings?.siteName ||
      "JobScope"}`;
    const whatsappUrl = `https://wa.me/${phoneNumber}?text=${encodeURIComponent(
      message
    )}`;
    window.open(whatsappUrl, "_blank");
  };

  useEffect(() => {
    fetchSettings();
    updatePageTitle("دليل استخدام الموقع");
  }, [fetchSettings]);

  // تأثير تلقائي لتغيير المميزات
  useEffect(() => {
    const interval = setInterval(() => {
      setActiveFeature((prev) => (prev + 1) % features.length);
    }, 4000);
    return () => clearInterval(interval);
  }, []);

  // بيانات المميزات الرئيسية
  const features = [
    {
      icon: <Shield className="w-8 h-8" />,
      title: "أمان وموثوقية",
      description: "نظام تحقق متقدم وحماية كاملة لبياناتك",
      color: "from-green-500 to-emerald-600",
    },
    {
      icon: <Zap className="w-8 h-8" />,
      title: "سرعة في الاستجابة",
      description: "ربط فوري بين طالبي الخدمة والحرفيين",
      color: "from-yellow-500 to-orange-600",
    },
    {
      icon: <TrendingUp className="w-8 h-8" />,
      title: "نمو مستمر",
      description: "شبكة متنامية من الحرفيين المهرة",
      color: "from-blue-500 to-indigo-600",
    },
    {
      icon: <Award className="w-8 h-8" />,
      title: "جودة مضمونة",
      description: "نظام تقييم شامل وضمان الجودة",
      color: "from-purple-500 to-pink-600",
    },
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5,
      },
    },
  };

  const clientSteps = [
    {
      id: 1,
      title: "إنشاء حساب جديد",
      description:
        "سجل حسابك كطالب خدمة باستخدام البريد الإلكتروني أو رقم الهاتف",
      icon: <User className="w-8 h-8" />,
      details: [
        "اختر 'تسجيل كطالب خدمة' من الصفحة الرئيسية",
        "أدخل اسمك الكامل وبيانات التواصل",
        "تأكد من بريدك الإلكتروني أو رقم هاتفك",
        "اكمل عملية التسجيل وسجل دخولك",
      ],
    },
    {
      id: 2,
      title: "البحث عن حرفيين",
      description: "استخدم خريطة البحث للعثور على الحرفيين في منطقتك",
      icon: <Search className="w-8 h-8" />,
      details: [
        "انتقل إلى صفحة 'البحث عن حرفي'",
        "حدد موقعك على الخريطة",
        "اختر نوع المهنة المطلوبة",
        "تصفح الحرفيين المتاحين في منطقتك",
      ],
    },
    {
      id: 3,
      title: "عرض ملفات الحرفيين",
      description: "تصفح ملفات الحرفيين وشاهد أعمالهم السابقة والتقييمات",
      icon: <Star className="w-8 h-8" />,
      details: [
        "اضغط على الحرفي لعرض ملفه الشخصي",
        "شاهد معرض أعماله السابقة",
        "اقرأ التقييمات من العملاء السابقين",
        "تحقق من ساعات العمل ونطاق الخدمة",
      ],
    },
    {
      id: 4,
      title: "حجز موعد",
      description: "احجز موعداً مع الحرفي المناسب لاحتياجاتك",
      icon: <Calendar className="w-8 h-8" />,
      details: [
        "اضغط على 'حجز موعد' في ملف الحرفي",
        "اختر التاريخ والوقت المناسب",
        "أدخل تفاصيل العمل المطلوب",
        "أكد الحجز وانتظر موافقة الحرفي",
      ],
    },
    {
      id: 5,
      title: "التواصل والمتابعة",
      description: "تواصل مع الحرفي وتابع حالة طلبك",
      icon: <MessageCircle className="w-8 h-8" />,
      details: [
        "استخدم نظام الرسائل للتواصل",
        "تابع حالة طلبك من لوحة التحكم",
        "احصل على إشعارات بتحديثات الطلب",
        "قيم الحرفي بعد انتهاء العمل",
      ],
    },
  ];

  const craftsmanSteps = [
    {
      id: 1,
      title: "إنشاء حساب حرفي",
      description: "سجل حسابك كحرفي وأضف معلومات مهنتك",
      icon: <Briefcase className="w-8 h-8" />,
      details: [
        "اختر 'تسجيل كحرفي' من الصفحة الرئيسية",
        "أدخل بياناتك الشخصية ومعلومات التواصل",
        "اختر مهنتك وتخصصاتك",
        "حدد نطاق عملك الجغرافي",
      ],
    },
    {
      id: 2,
      title: "إعداد الملف الشخصي",
      description: "أكمل ملفك الشخصي وأضف معرض أعمالك",
      icon: <Camera className="w-8 h-8" />,
      details: [
        "أضف صورة شخصية واضحة",
        "اكتب وصفاً مفصلاً عن خبراتك",
        "ارفع صور أعمالك السابقة",
        "حدد أسعارك وساعات عملك",
      ],
    },
    {
      id: 3,
      title: "تحديد نطاق العمل",
      description: "حدد المناطق التي تقدم فيها خدماتك على الخريطة",
      icon: <MapPin className="w-8 h-8" />,
      details: [
        "انتقل إلى إعدادات الملف الشخصي",
        "حدد موقعك الأساسي",
        "اختر نطاق عملك (1-5 كم)",
        "احفظ الإعدادات لتظهر للعملاء",
      ],
    },
    {
      id: 4,
      title: "إدارة الطلبات",
      description: "استقبل طلبات العملاء وأدر مواعيدك",
      icon: <CheckCircle className="w-8 h-8" />,
      details: [
        "تلقى إشعارات بالطلبات الجديدة",
        "راجع تفاصيل كل طلب",
        "اقبل أو ارفض الطلبات حسب توفرك",
        "حدث حالة العمل أثناء التنفيذ",
      ],
    },
    {
      id: 5,
      title: "بناء السمعة",
      description: "احصل على تقييمات إيجابية وزد من عدد عملائك",
      icon: <Star className="w-8 h-8" />,
      details: [
        "قدم خدمة عالية الجودة",
        "تواصل بشكل مهني مع العملاء",
        "اطلب من العملاء تقييم عملك",
        "حافظ على مواعيدك والتزاماتك",
      ],
    },
  ];

  return (
    <Layout>
      <style
        dangerouslySetInnerHTML={{
          __html: `
          @keyframes pulse {
            0% {
              box-shadow: 0 0 0 0 rgba(99, 102, 241, 0.4);
            }
            70% {
              box-shadow: 0 0 0 10px rgba(99, 102, 241, 0);
            }
            100% {
              box-shadow: 0 0 0 0 rgba(99, 102, 241, 0);
            }
          }
        `,
        }}
      />
      <div
        className={`min-h-screen ${
          darkMode
            ? "bg-gray-900 text-gray-100"
            : "bg-gradient-to-br from-blue-50 to-indigo-100 text-gray-800"
        } transition-colors duration-300`}
      >
        <div className="container mx-auto px-4 py-8">
          {/* Header */}
          <motion.div
            className="text-center mb-16 relative"
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            {/* Background decoration */}
            <div className="absolute inset-0 -z-10">
              <div
                className={`absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 ${
                  darkMode ? "bg-indigo-900/20" : "bg-indigo-200/30"
                } rounded-full blur-3xl`}
              ></div>
            </div>

            <motion.div
              initial={{ scale: 0.8 }}
              animate={{ scale: 1 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="mb-6"
            >
            </motion.div>

            <h1
              className={`text-4xl md:text-6xl font-bold mb-6 ${
                darkMode ? "text-indigo-300" : "text-indigo-800"
              } relative`}
            >
              دليل استخدام {settings?.siteName || "JobScope"}
              <motion.span
                className={`absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-32 h-1 ${
                  darkMode ? "bg-indigo-500" : "bg-indigo-600"
                } rounded-full`}
                initial={{ scaleX: 0 }}
                animate={{ scaleX: 1 }}
                transition={{ duration: 0.8, delay: 0.6 }}
              ></motion.span>
            </h1>

            <motion.p
              className={`text-lg md:text-xl ${
                darkMode ? "text-gray-300" : "text-gray-600"
              } max-w-4xl mx-auto leading-relaxed mb-8`}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.6, delay: 0.4 }}
            >
              تعلم كيفية استخدام المنصة خطوة بخطوة، سواء كنت تبحث عن حرفي أو
              تقدم خدماتك كحرفي محترف
            </motion.p>
          </motion.div>

          {/* Features Section */}
          <motion.div
            className="mb-16"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <h2
              className={`text-3xl font-bold text-center mb-8 ${
                darkMode ? "text-indigo-300" : "text-indigo-800"
              }`}
            >
              لماذا تختار منصتنا؟
            </h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
              {features.map((feature, index) => (
                <motion.div
                  key={index}
                  className={`relative p-6 rounded-xl ${
                    darkMode
                      ? "bg-gray-800 border border-gray-700"
                      : "bg-white border border-gray-200"
                  } shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer overflow-hidden group`}
                  whileHover={{ scale: 1.05, y: -5 }}
                  onClick={() => setActiveFeature(index)}
                >
                  <div
                    className={`absolute inset-0 bg-gradient-to-br ${feature.color} opacity-0 group-hover:opacity-10 transition-opacity duration-300`}
                  ></div>
                  <div
                    className={`w-16 h-16 rounded-full bg-gradient-to-br ${feature.color} flex items-center justify-center text-white mb-4 mx-auto`}
                  >
                    {feature.icon}
                  </div>
                  <h3
                    className={`text-xl font-bold text-center mb-3 ${
                      darkMode ? "text-white" : "text-gray-800"
                    }`}
                  >
                    {feature.title}
                  </h3>
                  <p
                    className={`text-center ${
                      darkMode ? "text-gray-300" : "text-gray-600"
                    }`}
                  >
                    {feature.description}
                  </p>
                  {activeFeature === index && (
                    <motion.div
                      className={`absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r ${feature.color}`}
                      initial={{ scaleX: 0 }}
                      animate={{ scaleX: 1 }}
                      transition={{ duration: 0.5 }}
                    ></motion.div>
                  )}
                </motion.div>
              ))}
            </div>
          </motion.div>

          {/* Tab Navigation */}
          <motion.div
            className="flex justify-center mb-8"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <div
              className={`flex rounded-lg p-1 ${
                darkMode
                  ? "bg-gray-800 border border-gray-700"
                  : "bg-white border border-indigo-200"
              } shadow-lg`}
            >
              <button
                className={`px-6 py-3 rounded-md font-medium transition-all duration-300 flex items-center gap-2 ${
                  activeTab === "client"
                    ? darkMode
                      ? "bg-indigo-600 text-white shadow-md"
                      : "bg-indigo-600 text-white shadow-md"
                    : darkMode
                    ? "text-gray-300 hover:text-white hover:bg-gray-700"
                    : "text-gray-600 hover:text-indigo-600 hover:bg-indigo-50"
                }`}
                onClick={() => setActiveTab("client")}
              >
                <User className="w-5 h-5" />
                دليل طالب الخدمة
              </button>
              <button
                className={`px-6 py-3 rounded-md font-medium transition-all duration-300 flex items-center gap-2 ${
                  activeTab === "craftsman"
                    ? darkMode
                      ? "bg-indigo-600 text-white shadow-md"
                      : "bg-indigo-600 text-white shadow-md"
                    : darkMode
                    ? "text-gray-300 hover:text-white hover:bg-gray-700"
                    : "text-gray-600 hover:text-indigo-600 hover:bg-indigo-50"
                }`}
                onClick={() => setActiveTab("craftsman")}
              >
                <Briefcase className="w-5 h-5" />
                دليل الحرفي
              </button>
            </div>
          </motion.div>

          {/* Content */}
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="visible"
            key={activeTab}
          >
            {(activeTab === "client" ? clientSteps : craftsmanSteps).map(
              (step) => (
                <motion.div
                  key={step.id}
                  variants={itemVariants}
                  className={`mb-8 ${
                    darkMode
                      ? "bg-gray-800 border border-gray-700"
                      : "bg-white border border-indigo-200"
                  } rounded-lg shadow-lg overflow-hidden`}
                >
                  <div className="p-6 md:p-8">
                    <div className="flex flex-col md:flex-row items-start gap-6">
                      {/* Step Number and Icon */}
                      <div className="flex-shrink-0">
                        <div
                          className={`w-16 h-16 rounded-full ${
                            darkMode ? "bg-indigo-600" : "bg-indigo-600"
                          } flex items-center justify-center text-white mb-4`}
                        >
                          {step.icon}
                        </div>
                        <div
                          className={`text-center text-sm font-bold ${
                            darkMode ? "text-indigo-300" : "text-indigo-600"
                          }`}
                        >
                          الخطوة {step.id}
                        </div>
                      </div>

                      {/* Content */}
                      <div className="flex-1">
                        <h3
                          className={`text-2xl font-bold mb-3 ${
                            darkMode ? "text-indigo-300" : "text-indigo-800"
                          }`}
                        >
                          {step.title}
                        </h3>
                        <p
                          className={`text-lg mb-4 ${
                            darkMode ? "text-gray-300" : "text-gray-600"
                          }`}
                        >
                          {step.description}
                        </p>

                        {/* Details List */}
                        <ul className="space-y-2">
                          {step.details.map((detail, detailIndex) => (
                            <li
                              key={detailIndex}
                              className={`flex items-start gap-3 ${
                                darkMode ? "text-gray-300" : "text-gray-700"
                              }`}
                            >
                              <ArrowRight
                                className={`w-5 h-5 mt-0.5 flex-shrink-0 ${
                                  darkMode
                                    ? "text-indigo-400"
                                    : "text-indigo-600"
                                }`}
                              />
                              <span>{detail}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  </div>
                </motion.div>
              )
            )}
          </motion.div>

          {/* Tips Section */}
          <motion.div
            className={`mt-12 p-8 rounded-lg ${
              darkMode
                ? "bg-gray-800 border border-gray-700"
                : "bg-gradient-to-r from-yellow-50 to-orange-50 border border-yellow-200"
            } shadow-lg`}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
          >
            <h3
              className={`text-2xl font-bold mb-6 text-center ${
                darkMode ? "text-yellow-300" : "text-yellow-700"
              }`}
            >
              💡 نصائح مهمة للنجاح
            </h3>
            <div className="grid md:grid-cols-2 gap-6">
              {activeTab === "client" ? (
                <>
                  <div
                    className={`p-4 rounded-lg ${
                      darkMode ? "bg-gray-700" : "bg-white"
                    } shadow-md`}
                  >
                    <h4
                      className={`font-bold mb-2 ${
                        darkMode ? "text-yellow-300" : "text-yellow-700"
                      }`}
                    >
                      🔍 اختيار الحرفي المناسب
                    </h4>
                    <p
                      className={`text-sm ${
                        darkMode ? "text-gray-300" : "text-gray-600"
                      }`}
                    >
                      تحقق من التقييمات والأعمال السابقة قبل الحجز. اقرأ تعليقات
                      العملاء السابقين واختر الحرفي الأنسب لاحتياجاتك.
                    </p>
                  </div>
                  <div
                    className={`p-4 rounded-lg ${
                      darkMode ? "bg-gray-700" : "bg-white"
                    } shadow-md`}
                  >
                    <h4
                      className={`font-bold mb-2 ${
                        darkMode ? "text-yellow-300" : "text-yellow-700"
                      }`}
                    >
                      💬 التواصل الواضح
                    </h4>
                    <p
                      className={`text-sm ${
                        darkMode ? "text-gray-300" : "text-gray-600"
                      }`}
                    >
                      اشرح تفاصيل العمل المطلوب بوضوح. كلما كانت التفاصيل أكثر
                      دقة، كان العمل أفضل والسعر أكثر عدالة.
                    </p>
                  </div>
                </>
              ) : (
                <>
                  <div
                    className={`p-4 rounded-lg ${
                      darkMode ? "bg-gray-700" : "bg-white"
                    } shadow-md`}
                  >
                    <h4
                      className={`font-bold mb-2 ${
                        darkMode ? "text-yellow-300" : "text-yellow-700"
                      }`}
                    >
                      📸 صور عالية الجودة
                    </h4>
                    <p
                      className={`text-sm ${
                        darkMode ? "text-gray-300" : "text-gray-600"
                      }`}
                    >
                      ارفع صور واضحة وعالية الجودة لأعمالك. الصور الجيدة تزيد من
                      ثقة العملاء وتحسن فرص حصولك على طلبات.
                    </p>
                  </div>
                  <div
                    className={`p-4 rounded-lg ${
                      darkMode ? "bg-gray-700" : "bg-white"
                    } shadow-md`}
                  >
                    <h4
                      className={`font-bold mb-2 ${
                        darkMode ? "text-yellow-300" : "text-yellow-700"
                      }`}
                    >
                      ⏰ الالتزام بالمواعيد
                    </h4>
                    <p
                      className={`text-sm ${
                        darkMode ? "text-gray-300" : "text-gray-600"
                      }`}
                    >
                      احترم مواعيدك والتزم بالجودة المطلوبة. السمعة الطيبة هي
                      أهم أصولك في هذا المجال.
                    </p>
                  </div>
                </>
              )}
            </div>
          </motion.div>

          {/* WhatsApp Contact */}
          <motion.div
            className={`mt-8 p-6 rounded-lg ${
              darkMode
                ? "bg-gray-800 border border-gray-700"
                : "bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200"
            } shadow-lg text-center`}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.5 }}
          >
            <h4
              className={`text-xl font-bold mb-3 ${
                darkMode ? "text-green-300" : "text-green-700"
              }`}
            >
              💬 هل تحتاج مساعدة إضافية؟
            </h4>
            <p
              className={`mb-6 ${darkMode ? "text-gray-300" : "text-gray-600"}`}
            >
              تواصل معنا مباشرة عبر الواتساب للحصول على دعم فوري
            </p>
            <button
              onClick={handleWhatsApp}
              className="bg-gradient-to-r from-green-500 to-green-700 text-white px-8 py-4 rounded-md hover:from-green-600 hover:to-green-800 transition-all duration-200 hover:shadow-md relative overflow-hidden group font-bold text-lg flex items-center justify-center mx-auto gap-3"
              style={{ animation: "pulse 2s infinite" }}
            >
              <span className="relative text-white z-10 flex items-center gap-3">
                <svg
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                  className="w-6 h-6"
                >
                  <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.893 3.488" />
                </svg>
                تواصل عبر الواتساب
              </span>
              <span className="absolute inset-0 bg-white opacity-20 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700"></span>
            </button>
            <p
              className={`mt-4 text-sm ${
                darkMode ? "text-gray-400" : "text-gray-500"
              }`}
            >
              سيتم فتح الواتساب مع رسالة جاهزة للإرسال
            </p>
          </motion.div>

          {/* Call to Action */}
          <motion.div
            className={`text-center mt-8 p-8 rounded-lg ${
              darkMode
                ? "bg-gray-800 border border-gray-700"
                : "bg-white border border-indigo-200"
            } shadow-lg`}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
          >
            <h3
              className={`text-2xl font-bold mb-4 ${
                darkMode ? "text-indigo-300" : "text-indigo-800"
              }`}
            >
              هل أنت مستعد للبدء؟
            </h3>
            <p
              className={`text-lg mb-6 ${
                darkMode ? "text-gray-300" : "text-gray-600"
              }`}
            >
              انضم إلى آلاف المستخدمين الذين يثقون في منصتنا لربط طالبي الخدمات
              بالحرفيين المهرة
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="/register/client"
                className="bg-gradient-to-r from-indigo-500 to-indigo-700 text-white px-8 py-3 rounded-md hover:from-indigo-600 hover:to-indigo-800 transition-all duration-200 hover:shadow-md relative overflow-hidden group font-medium"
                style={{ animation: "pulse 2s infinite" }}
              >
                <span className="relative text-white z-10 flex items-center justify-center gap-2">
                  <User className="w-5 h-5" />
                  سجل كطالب خدمة
                </span>
                <span className="absolute inset-0 bg-white opacity-20 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700"></span>
              </a>
              <a
                href="/register/craftsman"
                className="bg-gradient-to-r from-yellow-400 to-orange-500 text-white px-8 py-3 rounded-md hover:from-yellow-600 hover:to-orange-600 transition-all duration-200 hover:shadow-md relative overflow-hidden group font-medium"
                style={{ animation: "pulse 2s infinite" }}
              >
                <span className="relative text-white z-10 flex items-center justify-center gap-2">
                  <Briefcase className="w-5 h-5" />
                  سجل كحرفي
                </span>
                <span className="absolute inset-0 bg-white opacity-20 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700"></span>
              </a>
            </div>
          </motion.div>
        </div>
      </div>
    </Layout>
  );
};

export default HowToUsePage;
