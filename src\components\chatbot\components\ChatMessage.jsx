import React from "react";
import botAvatar from "/img/11zon_cropped.png?url";
import userAvatar from "/img/user-avatar.svg?url";

/**
 * مكون الرسالة الفردية في المحادثة
 * @param {Object} props - خصائص المكون
 * @param {string} props.message - نص الرسالة
 * @param {boolean} props.isUser - هل الرسالة من المستخدم
 * @param {boolean} props.darkMode - وضع الألوان الداكنة
 * @param {boolean} props.isThinking - هل البوت يفكر حالياً
 */
const ChatMessage = ({ message, isUser, darkMode, isThinking }) => {
  return (
    <div className={`flex ${isUser ? "justify-end" : "justify-start"} mb-3`}>
      <div className="flex items-start max-w-[80%]">
        {!isUser && (
          <img
            src={botAvatar}
            alt="Bot"
            className="w-10 h-10 rounded-full mr-2 mt-1 object-contain"
          />
        )}
        <div
          className={`px-4 py-2 rounded-lg ${
            isUser
              ? darkMode
                ? "bg-indigo-600 text-white"
                : "bg-indigo-500 text-white"
              : darkMode
              ? "bg-gray-700 text-white"
              : "bg-white text-gray-800 border border-gray-200"
          } ${isThinking ? "animate-pulse" : ""}`}
        >
          {isThinking ? (
            <div className="flex items-center">
              <span>جاري التفكير</span>
              <div className="ml-2 flex space-x-1">
                <div
                  className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
                  style={{ animationDelay: "0ms" }}
                ></div>
                <div
                  className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
                  style={{ animationDelay: "150ms" }}
                ></div>
                <div
                  className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
                  style={{ animationDelay: "300ms" }}
                ></div>
              </div>
            </div>
          ) : (
            message
          )}
        </div>
        {isUser && (
          <img
            src={userAvatar}
            alt="User"
            className="w-8 h-8 rounded-full ml-2 mt-1"
          />
        )}
      </div>
    </div>
  );
};

export default ChatMessage;
