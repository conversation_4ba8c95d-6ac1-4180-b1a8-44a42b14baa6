import React, { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import useThemeStore from "../../store/themeStore";
import useUserStore from "../../store/userStore";
import { Moon, Sun } from "lucide-react";

// استيراد المكونات بشكل مباشر (بدون Lazy Loading)
import HeroSection from "./components/HeroSection";
import ProfessionsSection from "./components/ProfessionsSection";
import HowItWorksSection from "./components/HowItWorksSection";
import CTASection from "./components/CTASection";
import Footer from "../../components/layout/Footer";

// استيراد CSS للتأثيرات البصرية
import "./welcome.css";

const WelcomePage = () => {
  const navigate = useNavigate();
  const darkMode = useThemeStore((state) => state.darkMode);
  const toggleDarkMode = useThemeStore((state) => state.toggleDarkMode);

  // التحقق من الجلسة السابقة للوصول المباشر
  useEffect(() => {
    // التحقق من مصدر الزيارة
    const isDirectAccess =
      !document.referrer ||
      !document.referrer.includes(window.location.origin) ||
      performance.getEntriesByType("navigation")[0]?.type === "reload";

    // فقط للوصول المباشر - التحقق من وجود جلسة سابقة
    if (isDirectAccess) {
      const token = localStorage.getItem("token");
      const userData = localStorage.getItem("user");
      const tokenExpiry = localStorage.getItem("tokenExpiry");

      if (token && userData) {
        // التحقق من انتهاء صلاحية التوكن
        if (tokenExpiry) {
          const expiryDate = new Date(tokenExpiry);
          const now = new Date();

          if (now > expiryDate) {
            // انتهت الصلاحية، مسح البيانات
            localStorage.removeItem("token");
            localStorage.removeItem("user");
            localStorage.removeItem("tokenExpiry");
            return;
          }
        }

        try {
          const user = JSON.parse(userData);
          console.log("جلسة سابقة صالحة موجودة، انتقال للصفحة المناسبة");

          // تحديث حالة المستخدم في المتجر
          const userStore = useUserStore.getState();
          userStore.login(user, user.userType, true);

          // الانتقال للصفحة المناسبة
          navigate(user.userType === "craftsman" ? "/profile/my" : "/home");
          return;
        } catch (error) {
          console.error("خطأ في تحليل بيانات المستخدم:", error);
          // مسح البيانات التالفة
          localStorage.removeItem("token");
          localStorage.removeItem("user");
          localStorage.removeItem("tokenExpiry");
        }
      }
    }
  }, [navigate]);

  // التمرير السلس بين الأقسام
  useEffect(() => {
    // تحديد جميع روابط التنقل الداخلية
    const internalLinks = document.querySelectorAll('a[href^="#"]');

    // إضافة معالج النقر لكل رابط
    internalLinks.forEach((link) => {
      link.addEventListener("click", (e) => {
        e.preventDefault();
        const targetId = link.getAttribute("href");
        const targetElement = document.querySelector(targetId);

        if (targetElement) {
          // التمرير السلس إلى القسم المستهدف
          targetElement.scrollIntoView({
            behavior: "smooth",
            block: "start",
          });
        }
      });
    });

    return () => {
      // إزالة معالجات الأحداث عند تفكيك المكون
      internalLinks.forEach((link) => {
        link.removeEventListener("click", () => {});
      });
    };
  }, []);

  return (
    <div
      className={`min-h-screen flex flex-col welcome-page ${
        darkMode ? "dark-mode" : "light-mode"
      }`}
    >
      {/* زر تبديل الوضع المظلم/الفاتح */}
      <div className="fixed top-6 left-6 z-50">
        <div
          className={`absolute inset-0 rounded-full ${
            darkMode ? "glow-yellow" : "glow-blue"
          }`}
        ></div>
        <button
          onClick={toggleDarkMode}
          className={`relative p-3 rounded-full shadow-lg transition-all duration-300 theme-toggle-btn overflow-hidden ${
            darkMode
              ? "bg-gray-800/90 text-yellow-300 hover:bg-gray-700/90"
              : "bg-indigo-600/90 text-white hover:bg-indigo-500/90 backdrop-blur-sm"
          }`}
          aria-label={darkMode ? "تفعيل الوضع الفاتح" : "تفعيل الوضع المظلم"}
        >
          <div className="relative flex items-center justify-center">
            {darkMode ? (
              <Sun size={22} className="animate-spin-slow relative z-10" />
            ) : (
              <>
                <div className="absolute inset-0 bg-indigo-400/30 rounded-full animate-pulse-circle"></div>
                <Moon size={22} className="relative z-10" />
              </>
            )}
          </div>
        </button>
      </div>
      {/* قسم الترحيب الرئيسي */}
      <div id="hero" className="section-container">
        <HeroSection />
      </div>

      {/* قسم المهن المتوفرة */}
      <div id="professions" className="section-container">
        <ProfessionsSection />
      </div>

      {/* قسم كيف تعمل المنصة */}
      <div id="how-it-works" className="section-container">
        <HowItWorksSection />
      </div>

      {/* قسم الدعوة للانضمام */}
      <div id="join-us" className="section-container">
        <CTASection />
      </div>

      {/* تذييل الصفحة */}
      <Footer />
    </div>
  );
};

export default WelcomePage;
