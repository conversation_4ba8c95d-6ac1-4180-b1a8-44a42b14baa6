// وظائف مساعدة للمصادقة

/**
 * الحصول على رمز المصادقة من التخزين المحلي
 * @returns {string|null} رمز المصادقة أو null إذا لم يكن موجودًا
 */
export const getAuthToken = () => {
  return localStorage.getItem('token');
};

/**
 * الحصول على نوع المستخدم من التخزين المحلي
 * @returns {string|null} نوع المستخدم أو null إذا لم يكن موجودًا
 */
export const getUserType = () => {
  return localStorage.getItem('userType');
};

/**
 * التحقق مما إذا كان المستخدم مسجل الدخول
 * @returns {boolean} true إذا كان المستخدم مسجل الدخول، false إذا لم يكن كذلك
 */
export const isAuthenticated = () => {
  return !!getAuthToken();
};

/**
 * تسجيل خروج المستخدم
 */
export const logout = () => {
  localStorage.removeItem('token');
  localStorage.removeItem('user');
  localStorage.removeItem('userType');
  localStorage.removeItem('userId');
  localStorage.removeItem('tokenExpiry');
  localStorage.removeItem('authVerified');
};
