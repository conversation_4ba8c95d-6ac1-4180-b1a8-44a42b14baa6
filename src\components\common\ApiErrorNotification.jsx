import React, { useState, useEffect } from "react";
import { AlertCircle, X } from "lucide-react";
import useThemeStore from "../../store/themeStore";

/**
 * مكون لعرض إشعارات أخطاء API بطريقة أكثر وضوحًا للمستخدم
 *
 * @param {Object} props - خصائص المكون
 * @param {string} props.message - رسالة الخطأ
 * @param {string} props.type - نوع الإشعار (error, warning, info)
 * @param {boolean} props.show - ما إذا كان يجب عرض الإشعار
 * @param {Function} props.onClose - دالة يتم استدعاؤها عند إغلاق الإشعار
 * @param {number} props.autoCloseTime - الوقت بالمللي ثانية قبل إغلاق الإشعار تلقائيًا (0 لعدم الإغلاق التلقائي)
 */
const ApiErrorNotification = ({
  message = "حدث خطأ أثناء الاتصال بالخادم",
  type = "error",
  show = false,
  onClose = () => {},
  autoCloseTime = 5000,
}) => {
  const darkMode = useThemeStore((state) => state.darkMode);
  const [isVisible, setIsVisible] = useState(show);

  // تحديث الحالة المرئية عند تغيير خاصية show
  useEffect(() => {
    setIsVisible(show);
  }, [show]);

  // إغلاق تلقائي بعد فترة زمنية محددة
  useEffect(() => {
    let timer;
    if (isVisible && autoCloseTime > 0) {
      timer = setTimeout(() => {
        setIsVisible(false);
        onClose();
      }, autoCloseTime);
    }
    return () => {
      if (timer) clearTimeout(timer);
    };
  }, [isVisible, autoCloseTime, onClose]);

  // إذا لم يكن مرئيًا، لا تعرض شيئًا
  if (!isVisible) return null;

  // تحديد الألوان بناءً على النوع والوضع المظلم
  const getColors = () => {
    if (type === "error") {
      return darkMode
        ? {
            bg: "bg-red-900/70",
            border: "border-red-700",
            text: "text-red-200",
            icon: "text-red-400",
          }
        : {
            bg: "bg-red-50",
            border: "border-red-200",
            text: "text-red-800",
            icon: "text-red-500",
          };
    } else if (type === "warning") {
      return darkMode
        ? {
            bg: "bg-yellow-900/70",
            border: "border-yellow-700",
            text: "text-yellow-200",
            icon: "text-yellow-400",
          }
        : {
            bg: "bg-yellow-50",
            border: "border-yellow-200",
            text: "text-yellow-800",
            icon: "text-yellow-500",
          };
    } else {
      return darkMode
        ? {
            bg: "bg-blue-900/70",
            border: "border-blue-700",
            text: "text-blue-200",
            icon: "text-blue-400",
          }
        : {
            bg: "bg-blue-50",
            border: "border-blue-200",
            text: "text-blue-800",
            icon: "text-blue-500",
          };
    }
  };

  const colors = getColors();

  return (
    <div
      className={`fixed top-4 left-1/2 transform -translate-x-1/2 z-50 w-full max-w-md mx-auto rounded-lg shadow-lg border ${colors.border} ${colors.bg} transition-all duration-300 animate-fade-in`}
      style={{ animation: "fade-in 0.3s ease-out forwards" }}
      role="alert"
    >
      <div className="flex items-start p-4">
        <div className="flex-shrink-0">
          <AlertCircle className={`h-5 w-5 ${colors.icon}`} />
        </div>
        <div className="mr-3 flex-1">
          <div className={`text-sm font-medium ${colors.text}`}>{message}</div>
          <div className={`mt-1 text-xs ${colors.text} opacity-80`}>
            {type === "error"
              ? "يرجى المحاولة مرة أخرى لاحقًا أو الاتصال بالدعم الفني إذا استمرت المشكلة."
              : type === "warning"
              ? "قد تكون بعض الميزات غير متاحة مؤقتًا."
              : "هذه رسالة إعلامية فقط."}
          </div>
        </div>
        <button
          type="button"
          className={`flex-shrink-0 mr-2 ${colors.text} hover:opacity-75 focus:outline-none`}
          onClick={() => {
            setIsVisible(false);
            onClose();
          }}
        >
          <X className="h-4 w-4" />
        </button>
      </div>
    </div>
  );
};

export default ApiErrorNotification;
