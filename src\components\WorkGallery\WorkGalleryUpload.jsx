import React, { useState, useRef } from "react";
import { Upload, X, Image, Loader } from "lucide-react";
import {
  uploadMultipleImages,
  validateImage,
  compressImage,
} from "../../services/imageUploadService";
import useThemeStore from "../../store/themeStore";
import { showToast } from "../../utils/toast";

const WorkGalleryUpload = ({
  onImagesUploaded,
  maxImages = 10,
  existingImages = [],
}) => {
  const darkMode = useThemeStore((state) => state.darkMode);
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(null);
  const [dragActive, setDragActive] = useState(false);
  const fileInputRef = useRef(null);

  // التحقق من عدد الصور الحالية
  const canUploadMore = existingImages.length < maxImages;
  const remainingSlots = maxImages - existingImages.length;

  const handleFileSelect = async (files) => {
    if (!canUploadMore) {
      showToast(`لا يمكن رفع أكثر من ${maxImages} صور`, "error", 3000);
      return;
    }

    const fileArray = Array.from(files);

    // التحقق من عدد الملفات
    if (fileArray.length > remainingSlots) {
      showToast(`يمكنك رفع ${remainingSlots} صور فقط`, "error", 3000);
      return;
    }

    // التحقق من صحة الملفات
    const validFiles = [];
    const invalidFiles = [];

    for (const file of fileArray) {
      const validation = validateImage(file);
      if (validation.isValid) {
        validFiles.push(file);
      } else {
        invalidFiles.push({ file, errors: validation.errors });
      }
    }

    // عرض أخطاء الملفات غير الصالحة
    if (invalidFiles.length > 0) {
      invalidFiles.forEach(({ file, errors }) => {
        showToast(`${file.name}: ${errors.join(", ")}`, "error", 5000);
      });
    }

    if (validFiles.length === 0) {
      return;
    }

    // ضغط الصور قبل الرفع
    setUploading(true);
    setUploadProgress({
      current: 0,
      total: validFiles.length,
      stage: "compressing",
    });

    try {
      const compressedFiles = [];
      for (let i = 0; i < validFiles.length; i++) {
        const file = validFiles[i];
        setUploadProgress({
          current: i + 1,
          total: validFiles.length,
          stage: "compressing",
          currentFile: file.name,
        });

        const compressedFile = await compressImage(file, 0.8, 1920, 1080);
        compressedFiles.push(compressedFile);
      }

      // رفع الصور
      setUploadProgress({
        current: 0,
        total: compressedFiles.length,
        stage: "uploading",
      });

      const results = await uploadMultipleImages(
        compressedFiles,
        (progress) => {
          setUploadProgress({
            ...progress,
            stage: "uploading",
          });
        }
      );

      // معالجة النتائج
      const successfulUploads = results.filter((result) => result.success);
      const failedUploads = results.filter((result) => !result.success);

      if (successfulUploads.length > 0) {
        const imageUrls = successfulUploads.map((result) => ({
          url: result.data.url,
          thumb: result.data.thumb,
          medium: result.data.medium,
          id: result.data.id,
          filename: result.data.filename,
          size: result.data.size,
          uploadedAt: new Date().toISOString(),
        }));

        onImagesUploaded(imageUrls);
        showToast(
          `تم رفع ${successfulUploads.length} صور بنجاح`,
          "success",
          3000
        );
      }

      if (failedUploads.length > 0) {
        showToast(`فشل في رفع ${failedUploads.length} صور`, "error", 3000);
      }
    } catch (error) {
      console.error("خطأ في رفع الصور:", error);
      showToast("حدث خطأ أثناء رفع الصور", "error", 3000);
    } finally {
      setUploading(false);
      setUploadProgress(null);
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
    }
  };

  const handleDrag = (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFileSelect(e.dataTransfer.files);
    }
  };

  const handleInputChange = (e) => {
    if (e.target.files && e.target.files[0]) {
      handleFileSelect(e.target.files);
    }
  };

  const openFileDialog = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  return (
    <div className="w-full">
      {/* منطقة رفع الصور */}
      <div
        className={`
          relative border-2 border-dashed rounded-lg p-6 text-center transition-all duration-200
          ${
            dragActive
              ? "border-blue-500 bg-blue-50 dark:bg-blue-900/20"
              : "border-gray-300 dark:border-gray-600"
          }
          ${
            !canUploadMore
              ? "opacity-50 cursor-not-allowed"
              : "cursor-pointer hover:border-blue-400 hover:bg-gray-50 dark:hover:bg-gray-800"
          }
          ${darkMode ? "bg-gray-800" : "bg-white"}
        `}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
        onClick={canUploadMore ? openFileDialog : undefined}
      >
        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept="image/*"
          onChange={handleInputChange}
          className="hidden"
          disabled={!canUploadMore || uploading}
        />

        {uploading ? (
          <div className="flex flex-col items-center space-y-4">
            <Loader className="w-12 h-12 text-blue-500 animate-spin" />
            <div className="text-center">
              <p
                className={`font-medium ${
                  darkMode ? "text-white" : "text-gray-900"
                }`}
              >
                {uploadProgress?.stage === "compressing"
                  ? "ضغط الصور..."
                  : "رفع الصور..."}
              </p>
              {uploadProgress && (
                <div className="mt-2">
                  <div className="w-64 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div
                      className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${uploadProgress.percentage || 0}%` }}
                    />
                  </div>
                  <p
                    className={`text-sm mt-1 ${
                      darkMode ? "text-gray-300" : "text-gray-600"
                    }`}
                  >
                    {uploadProgress.current} من {uploadProgress.total}
                    {uploadProgress.currentFile &&
                      ` - ${uploadProgress.currentFile}`}
                  </p>
                </div>
              )}
            </div>
          </div>
        ) : (
          <div className="flex flex-col items-center space-y-4">
            <Image
              className={`w-12 h-12 ${
                canUploadMore ? "text-gray-400" : "text-gray-300"
              }`}
            />
            <div>
              <p
                className={`text-lg font-medium ${
                  canUploadMore
                    ? darkMode
                      ? "text-white"
                      : "text-gray-900"
                    : "text-gray-400"
                }`}
              >
                {canUploadMore
                  ? "اسحب الصور هنا أو انقر للاختيار"
                  : "تم الوصول للحد الأقصى من الصور"}
              </p>
              {canUploadMore && (
                <p
                  className={`text-sm mt-1 ${
                    darkMode ? "text-gray-300" : "text-gray-600"
                  }`}
                >
                  يمكنك رفع {remainingSlots} صور إضافية (الحد الأقصى {maxImages}{" "}
                  صور)
                </p>
              )}
            </div>
            {canUploadMore && (
              <button
                type="button"
                className="inline-flex items-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
                disabled={uploading}
              >
                <Upload className="w-4 h-4 ml-2" />
                اختيار الصور
              </button>
            )}
          </div>
        )}
      </div>

      {/* معلومات إضافية */}
      <div
        className={`mt-4 text-sm ${
          darkMode ? "text-gray-300" : "text-gray-600"
        }`}
      >
        <p>• الأنواع المدعومة: JPEG, PNG, GIF, WebP</p>
        <p>• الحد الأقصى لحجم الصورة: 32MB</p>
        <p>• يمكن رفع 6 صور كحد أقصى</p>
        <p>• سيتم ضغط الصور تلقائياً لتحسين الأداء</p>
      </div>
    </div>
  );
};

export default WorkGalleryUpload;
