import React, { useState, useEffect, useRef } from "react";
import { SERVER_URL } from "../../services/config";

const LazyImage = ({ src, alt, className, placeholderClassName, onError }) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isInView, setIsInView] = useState(false);
  const [imgSrc, setImgSrc] = useState("");
  const [hasError, setHasError] = useState(false);
  const [errorCount, setErrorCount] = useState(0);
  const [useProxy, setUseProxy] = useState(false);
  const imgRef = useRef();

  // استخدام Intersection Observer API لتحديد متى تصبح الصورة مرئية
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        // إذا كانت الصورة مرئية، نبدأ بتحميلها
        if (entries[0].isIntersecting) {
          setIsInView(true);
          observer.disconnect();
        }
      },
      { threshold: 0.1 } // تحميل الصورة عندما يكون 10% منها مرئيًا على الأقل
    );

    if (imgRef.current) {
      observer.observe(imgRef.current);
    }

    return () => {
      if (imgRef.current) {
        observer.disconnect();
      }
    };
  }, []);

  // استخدام useEffect لإضافة مستمع للحدث المخصص لتحديث الصورة
  useEffect(() => {
    // إضافة مستمع للحدث المخصص
    const handleImageUpdate = (event) => {
      // التحقق مما إذا كان المسار الحالي يحتوي على معرف المستخدم المحدث
      const userIdMatch = src && typeof src === 'string' ? src.match(/profile-([a-f0-9]+)/) : null;
      if (userIdMatch && userIdMatch[1] === event.detail.userId) {
        console.log(
          "LazyImage: Received image update event for current user:",
          event.detail
        );
        // تحديث مسار الصورة
        setImgSrc(`${SERVER_URL}${event.detail.newImagePath}`);
        setIsLoaded(false);
        setHasError(false);
        setErrorCount(0);
      }
    };

    // إضافة المستمع
    window.addEventListener("userImageUpdated", handleImageUpdate);

    // إزالة المستمع عند تفكيك المكون
    return () => {
      window.removeEventListener("userImageUpdated", handleImageUpdate);
    };
  }, [src]);

  // تحديث مصدر الصورة عند تغيير الـ prop
  useEffect(() => {
    // إعادة تعيين حالة الخطأ عند تغيير مصدر الصورة
    setHasError(false);
    setErrorCount(0);
    setUseProxy(false);

    // طباعة مصدر الصورة للتصحيح

    // التحقق من أن src هو نص صالح
    if (
      !src ||
      typeof src !== "string" ||
      src === "undefined" ||
      src === "null"
    ) {
      // إذا كان المصدر فارغًا أو غير صالح، استخدم الصورة الافتراضية
      console.log("LazyImage: Empty or invalid source, using default avatar");
      setImgSrc("/img/default-avatar.svg");
      setIsLoaded(true); // تعيين الصورة كمحملة لإخفاء مؤشر التحميل
    } else if (
      src === "/img/user-avatar.svg" ||
      src === "/img/user-avatar.svg?url" ||
      src === "/img/default-avatar.svg"
    ) {
      // إذا كان المصدر هو الصورة الافتراضية، استخدمها مباشرة
      console.log("LazyImage: Using default avatar directly");
      setImgSrc("/img/default-avatar.svg");
      setIsLoaded(true); // تعيين الصورة كمحملة لإخفاء مؤشر التحميل
    } else if (src.startsWith("data:")) {
      // إذا كان المصدر بيانات Base64، استخدمه كما هو
      console.log("LazyImage: Using data URL directly");
      setImgSrc(src);
    } else if (
      typeof src === "string" &&
      src.includes("cinemaity.cinemaity.com")
    ) {
      // إذا كان المصدر من الموقع الحالي، استخدم المسار النسبي
      try {
        const url = new URL(src);
        console.log("LazyImage: Converting domain URL to path:", url.pathname);
        setImgSrc(url.pathname);
      } catch (e) {
        console.error("LazyImage: Invalid URL:", src);
        setImgSrc("/img/user-avatar.svg");
      }
    } else if (
      typeof src === "string" &&
      src.startsWith("https://jobscope-8t58.onrender.com/uploads/")
    ) {
      // إذا كان المصدر URL كاملاً من الخادم، استخدم المسار النسبي مع proxy
      const relativePath = src.replace(
        "https://jobscope-8t58.onrender.com",
        ""
      );
      console.log(
        "LazyImage: Converting server URL to relative path:",
        relativePath
      );
      setImgSrc(relativePath);
      setUseProxy(true);
    } else if (typeof src === "string" && src.startsWith("http")) {
      // إذا كان المصدر URL كاملاً آخر، استخدمه كما هو
      console.log("LazyImage: Using external URL directly:", src);
      setImgSrc(src);
    } else if (
      (typeof src === "string" && src.startsWith("/uploads/")) ||
      (typeof src === "string" && src.includes("profileImage")) ||
      (typeof src === "string" && src.includes("profile"))
    ) {
      // إذا كان المسار يبدأ بـ /uploads/ أو يحتوي على profileImage أو profile، فهو مسار للصور المحملة على الخادم
      // استخدم المسار الكامل مباشرة بدلاً من المسار النسبي
      const fullUrl = `${SERVER_URL}${
        typeof src === "string" && src.startsWith("/") ? src : `/${src}`
      }`;
      setImgSrc(fullUrl);
      setUseProxy(false);
    } else if (typeof src === "string" && src.startsWith("/")) {
      // إذا كان المسار يبدأ بـ / ولكن ليس /uploads/، فهو مسار محلي
      console.log("LazyImage: Using local path:", src);
      setImgSrc(src);
    } else {
      // إذا كان مسارًا نسبيًا، أضف عنوان الخادم
      const fullPath = `${SERVER_URL}${typeof src === 'string' && src.startsWith("/") ? "" : "/"}${src}`;
      console.log("LazyImage: Converting relative path to full URL:", fullPath);
      setImgSrc(fullPath);
    }

    // حفظ مصدر الصورة الأصلي في localStorage للتصحيح
    try {
      const originalSources = JSON.parse(
        localStorage.getItem("debugImageSources") || "[]"
      );
      // حفظ آخر 5 مصادر فقط لتجنب تجاوز الحصة
      if (originalSources.length > 5) {
        originalSources.shift(); // إزالة أقدم عنصر
      }
      originalSources.push({
        original: src,
        processed: imgSrc,
        timestamp: new Date().toISOString(),
      });
      localStorage.setItem(
        "debugImageSources",
        JSON.stringify(originalSources)
      );
    } catch (error) {
      console.log("Error saving to localStorage:", error.message);
      // محاولة مسح localStorage إذا كان ممتلئًا
      try {
        localStorage.removeItem("debugImageSources");
      } catch (e) {
        // تجاهل الخطأ إذا فشل المسح أيضًا
      }
    }
  }, [src]);

  // معالج تحميل الصورة
  const handleImageLoaded = () => {
    setIsLoaded(true);
    setHasError(false);
    setErrorCount(0);

    // إرسال حدث مخصص لإخبار المكونات الأخرى بأن الصورة تم تحميلها
    const imageLoadedEvent = new CustomEvent("imageLoaded", {
      detail: { src: imgSrc, originalSrc: src },
    });
    window.dispatchEvent(imageLoadedEvent);
  };

  // معالج خطأ تحميل الصورة
  const handleImageError = () => {
    console.log("LazyImage: Error loading image:", imgSrc);

    // إذا كان المسار الحالي هو الصورة الافتراضية ولا تزال هناك أخطاء
    if (
      (typeof imgSrc === "string" && imgSrc.includes("user-avatar.svg")) ||
      (typeof imgSrc === "string" && imgSrc.includes("default-avatar.svg"))
    ) {
      console.log(
        "LazyImage: Error loading default avatar, stopping retry attempts"
      );
      setIsLoaded(true); // تعيين الصورة كمحملة لإخفاء مؤشر التحميل
      return; // توقف عن المحاولة
    }

    // إذا كان عدد المحاولات يساوي 1، نعرض رسالة تحذير
    if (errorCount === 1) {
      console.log("LazyImage: Last attempt before using default avatar");
    }

    // زيادة عداد الأخطاء
    const newErrorCount = errorCount + 1;
    setErrorCount(newErrorCount);

    // إذا وصلنا إلى الحد الأقصى من المحاولات (2 محاولات فقط)، استخدم الصورة الافتراضية مباشرة
    if (newErrorCount >= 2) {
      console.log(
        "LazyImage: Maximum retry attempts reached (2), using default avatar"
      );
      setImgSrc("/img/default-avatar.svg");
      setIsLoaded(true); // تعيين الصورة كمحملة لإخفاء مؤشر التحميل
      return; // توقف عن المحاولة
    }

    // تجنب الدخول في حلقة لا نهائية من الأخطاء (حد أقصى 2 محاولات)
    if (errorCount < 2) {
      setHasError(true);

      // استراتيجية 1: إذا كنا نستخدم proxy وفشل التحميل، جرب استخدام المسار الكامل
      if (useProxy && errorCount === 0) {
        const fullUrl = `${SERVER_URL}${imgSrc}`;
        console.log("LazyImage: Strategy 1: Trying full URL:", fullUrl);
        setImgSrc(fullUrl);
        setUseProxy(false);
        return;
      }

      // استراتيجية 2: إذا كان المسار يبدأ بـ https://jobscope-8t58.onrender.com وفشل التحميل
      if (
        typeof imgSrc === 'string' && imgSrc.startsWith("https://jobscope-8t58.onrender.com") &&
        errorCount === 1
      ) {
        const relativePath = imgSrc.replace(
          "https://jobscope-8t58.onrender.com",
          ""
        );
        console.log(
          "LazyImage: Strategy 2: Trying relative path:",
          relativePath
        );
        setImgSrc(relativePath);
        return;
      }

      // استراتيجية 3: جرب استخدام المسار المطلق من الجذر
      if (errorCount === 2 && typeof imgSrc === 'string' && imgSrc.startsWith("/")) {
        const absolutePath = window.location.origin + imgSrc;
        console.log(
          "LazyImage: Strategy 3: Trying absolute path:",
          absolutePath
        );
        setImgSrc(absolutePath);
        return;
      }

      // استراتيجية 4: استخراج معرف المستخدم من المسار وتجربة التنسيق الصحيح
      if (errorCount === 2) {
        // استخراج معرف المستخدم من المسار إذا كان موجودًا
        const userIdMatch =
          typeof src === "string"
            ? src.match(/profile-([a-f0-9]+)(?:-(\d+))?\.png/)
            : null;
        if (userIdMatch && userIdMatch[1]) {
          const userId = userIdMatch[1];

          // محاولة الحصول على المستخدم من localStorage
          try {
            const storedUser = JSON.parse(localStorage.getItem("user") || "{}");
            if (
              storedUser &&
              (storedUser.id === userId || storedUser._id === userId)
            ) {
              // استخدام التنسيق الصحيح للصورة من قاعدة البيانات
              const correctPath = `/uploads/profile-${userId}-${Date.now()}.png`;
              console.log(
                "LazyImage: Strategy 4: Using correct format with user ID:",
                correctPath
              );
              setImgSrc(`${SERVER_URL}${correctPath}`);
              return;
            }
          } catch (e) {
            console.log("Error getting user from localStorage:", e.message);
          }

          // إذا لم نتمكن من الحصول على المستخدم، نستخدم التنسيق الصحيح مع معرف المستخدم فقط
          const correctPath = `/uploads/profile-${userId}.png`;
          console.log(
            "LazyImage: Strategy 4: Using correct format with user ID only:",
            correctPath
          );
          setImgSrc(`${SERVER_URL}${correctPath}`);
          return;
        }
      }

      // استراتيجية 5: تجربة التنسيق الصحيح للصورة بناءً على معرف المستخدم من localStorage
      if (errorCount === 3) {
        try {
          const storedUser = JSON.parse(localStorage.getItem("user") || "{}");
          if (storedUser && (storedUser.id || storedUser._id)) {
            const userId = storedUser.id || storedUser._id;
            // استخدام التنسيق الصحيح للصورة من قاعدة البيانات
            const correctPath = `/uploads/profile-${userId}-${Date.now()}.png`;
            console.log(
              "LazyImage: Strategy 5: Using correct format with stored user ID:",
              correctPath
            );
            setImgSrc(`${SERVER_URL}${correctPath}`);
            return;
          }
        } catch (e) {
          console.log("Error getting user from localStorage:", e.message);
        }
      }

      // استراتيجية 6: تجربة مسارات متعددة للصورة
      if (errorCount === 4) {
        // استخراج معرف المستخدم من المسار إذا كان موجودًا
        const userIdMatch =
          typeof src === "string" ? src.match(/profile-([a-f0-9]+)/) : null;
        if (userIdMatch && userIdMatch[1]) {
          const userId = userIdMatch[1];

          // تجربة عدة تنسيقات للصورة
          const formats = [
            `/uploads/profile-${userId}.png`,
            `/uploads/profile-${userId}.jpg`,
            `/uploads/profile-${userId}.jpeg`,
            `/uploads/profile-${userId}-${Date.now()}.png`,
          ];

          const formatIndex = Math.min(errorCount - 4, formats.length - 1);
          const alternativePath = formats[formatIndex];

          console.log(
            "LazyImage: Strategy 6: Trying alternative format:",
            alternativePath
          );
          setImgSrc(`${SERVER_URL}${alternativePath}`);
          return;
        }
      }
    }

    // تم نقل هذا المنطق إلى معالج الخطأ في الأعلى
    // لا حاجة لتكراره هنا

    // استدعاء معالج الخطأ المخصص إذا تم توفيره
    if (onError) {
      onError();
    }
  };

  return (
    <div ref={imgRef} className={className || ""}>
      {isInView ? (
        <>
          {/* صورة غير مرئية للتحميل */}
          {typeof imgSrc === "string" &&
          imgSrc.includes("default-avatar.svg") ? (
            <img
              src="/img/default-avatar.svg"
              alt={alt || "صورة المستخدم الافتراضية"}
              className={`${className || ""} block`}
              onLoad={() => setIsLoaded(true)}
              loading="eager"
              fetchPriority="high"
            />
          ) : (
            <img
              src={imgSrc}
              alt={alt}
              className={`${className || ""} ${isLoaded ? "block" : "hidden"}`}
              onLoad={handleImageLoaded}
              onError={handleImageError}
              loading="eager" // تحميل الصورة بشكل فوري
              fetchPriority="high" // إعطاء أولوية عالية لتحميل الصورة
            />
          )}

          {/* مؤشر التحميل يظهر حتى تكتمل الصورة أو يتم استخدام الصورة الافتراضية أو بعد محاولتين */}
          {!isLoaded &&
            !(
              typeof imgSrc === "string" &&
              imgSrc.includes("default-avatar.svg")
            ) &&
            errorCount < 2 && (
              <div
                className={
                  placeholderClassName ||
                  `${className || ""} bg-gray-200 animate-pulse`
                }
              >
                {/* إضافة مؤشر تحميل دائري */}
                <div className="absolute inset-0 flex items-center justify-center">
                  {/* إذا كان عدد المحاولات أكبر من 2، نعرض الصورة الافتراضية بدلاً من مؤشر التحميل */}
                  {errorCount >= 2 ? (
                    <img
                      src="/img/default-avatar.svg"
                      alt={alt || "صورة المستخدم الافتراضية"}
                      className={`${className ||
                        ""} block w-full h-full object-cover`}
                      onLoad={() => {
                        setIsLoaded(true);
                        setErrorCount(0); // إعادة تعيين عداد الأخطاء
                      }}
                      onError={() => {
                        console.log(
                          "LazyImage: Error loading default avatar, stopping all attempts"
                        );
                        setIsLoaded(true); // تعيين الصورة كمحملة لإخفاء مؤشر التحميل على أي حال
                      }}
                    />
                  ) : (
                    <div className="w-8 h-8 border-4 border-indigo-500 border-t-transparent rounded-full animate-spin"></div>
                  )}
                </div>
              </div>
            )}
        </>
      ) : (
        // مكان حجز قبل أن تصبح الصورة مرئية
        <div
          className={placeholderClassName || `${className || ""} bg-gray-200`}
        ></div>
      )}
    </div>
  );
};

export default LazyImage;
