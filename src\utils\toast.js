/**
 * وحدة خدمات الإشعارات المنبثقة (Toast Notifications)
 * تستخدم لعرض رسائل للمستخدم بشكل مؤقت
 */

// الألوان المستخدمة للإشعارات حسب النوع
const TOAST_COLORS = {
  success: {
    light: {
      bg: "bg-green-100",
      text: "text-green-700",
      border: "border-green-300",
      icon: "bg-green-200",
    },
    dark: {
      bg: "bg-green-900/95",
      text: "text-green-200",
      border: "border-green-700",
      icon: "bg-green-800",
    },
  },
  error: {
    light: {
      bg: "bg-red-100",
      text: "text-red-700",
      border: "border-red-300",
      icon: "bg-red-200",
    },
    dark: {
      bg: "bg-red-900/95",
      text: "text-red-200",
      border: "border-red-700",
      icon: "bg-red-800",
    },
  },
  warning: {
    light: {
      bg: "bg-yellow-100",
      text: "text-yellow-700",
      border: "border-yellow-300",
      icon: "bg-yellow-200",
    },
    dark: {
      bg: "bg-yellow-900/95",
      text: "text-yellow-200",
      border: "border-yellow-700",
      icon: "bg-yellow-800",
    },
  },
  info: {
    light: {
      bg: "bg-blue-100",
      text: "text-blue-700",
      border: "border-blue-300",
      icon: "bg-blue-200",
    },
    dark: {
      bg: "bg-blue-900/95",
      text: "text-blue-200",
      border: "border-blue-700",
      icon: "bg-blue-800",
    },
  },
};

// الأيقونات المستخدمة للإشعارات حسب النوع
const TOAST_ICONS = {
  success: `
    <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
    </svg>
  `,
  error: `
    <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
    </svg>
  `,
  warning: `
    <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
    </svg>
  `,
  info: `
    <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>
  `,
};

/**
 * عرض إشعار منبثق للمستخدم
 * @param {string} message - نص الرسالة
 * @param {string} type - نوع الإشعار (success, error, warning, info)
 * @param {number} duration - مدة ظهور الإشعار بالمللي ثانية (افتراضي: 1000)
 */
export const showToast = (message, type = "info", duration = 1000) => {
  // التحقق من وجود الرسالة
  if (!message) return;

  // التحقق من صحة النوع
  if (!["success", "error", "warning", "info"].includes(type)) {
    type = "info";
  }

  // التحقق من وجود عنصر الحاوية للإشعارات
  let toastContainer = document.getElementById("toast-container");

  // إنشاء حاوية الإشعارات إذا لم تكن موجودة
  if (!toastContainer) {
    toastContainer = document.createElement("div");
    toastContainer.id = "toast-container";
    toastContainer.className =
      "fixed inset-0 flex items-center justify-center z-50 pointer-events-none";
    toastContainer.style.position = "fixed";
    document.body.appendChild(toastContainer);
  }

  // إزالة جميع الإشعارات السابقة لمنع التراكم
  while (toastContainer.firstChild) {
    toastContainer.removeChild(toastContainer.firstChild);
  }

  // التحقق من وضع الألوان (داكن أم فاتح)
  const isDarkMode = document.body.classList.contains("dark-mode");
  const colorScheme = isDarkMode ? "dark" : "light";
  const colors = TOAST_COLORS[type][colorScheme];

  // إنشاء عنصر الإشعار
  const toast = document.createElement("div");
  toast.className = `p-4 rounded-lg shadow-2xl ${colors.bg} ${colors.text} text-center max-w-md border ${colors.border} pointer-events-auto animate-toast`;

  // إضافة المحتوى للإشعار
  toast.innerHTML = `
    <div class="flex flex-col items-center">
      <div class="w-12 h-12 rounded-full flex items-center justify-center mb-2 ${colors.icon}">
        ${TOAST_ICONS[type]}
      </div>
      <p class="text-lg font-semibold">${message}</p>
    </div>
  `;

  // إضافة الإشعار للحاوية
  toastContainer.appendChild(toast);

  // إزالة الإشعار بعد المدة المحددة
  setTimeout(() => {
    if (toast && toast.parentNode) {
      toast.classList.add("opacity-0");
      setTimeout(() => {
        if (toast && toast.parentNode) {
          toast.parentNode.removeChild(toast);
        }

        // إزالة الحاوية إذا لم تعد تحتوي على إشعارات
        if (toastContainer && toastContainer.childNodes.length === 0 && toastContainer.parentNode) {
          toastContainer.parentNode.removeChild(toastContainer);
        }
      }, 300);
    }
  }, duration);
};

export default { showToast };
