import React, { useEffect } from "react";
import { <PERSON> } from "react-router-dom";
import { motion } from "framer-motion";
import Button from "../../../components/common/Button";
import useThemeStore from "../../../store/themeStore";
import useSiteSettingsStore from "../../../store/siteSettingsStore";
import { Briefcase, User, Star, Clock, MapPin, Shield } from "lucide-react";

const CTASection = () => {
  const darkMode = useThemeStore((state) => state.darkMode);
  const { settings, fetchSettings } = useSiteSettingsStore();

  // جلب إعدادات الموقع
  useEffect(() => {
    fetchSettings();
  }, [fetchSettings]);

  // تكوين التأثيرات الحركية
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5,
      },
    },
  };

  // مميزات المنصة
  const features = [
    {
      icon: <Star size={24} />,
      title: "خدمات عالية الجودة",
      description: "حرفيون مهرة ومحترفون في مجالاتهم",
    },
    {
      icon: <Clock size={24} />,
      title: "توفير الوقت",
      description: "العثور على الحرفي المناسب بسرعة وسهولة",
    },
    {
      icon: <MapPin size={24} />,
      title: "خدمات محلية",
      description: "اعثر على حرفيين بالقرب من موقعك",
    },
    {
      icon: <Shield size={24} />,
      title: "موثوقية وأمان",
      description: "تقييمات ومراجعات حقيقية من العملاء",
    },
  ];

  return (
    <section
      id="join-us"
      className={`py-24 ${
        darkMode
          ? "bg-gradient-to-br from-indigo-900 via-indigo-950 to-gray-900"
          : "bg-gradient-to-br from-indigo-600 via-blue-600 to-blue-700"
      } text-white transition-colors duration-300 relative overflow-hidden`}
    >
      {/* أشكال زخرفية في الخلفية */}
      <div className="absolute inset-0 overflow-hidden">
        {/* نقاط متباعدة */}
        <div
          className="absolute top-0 left-0 w-full h-full opacity-10"
          style={{
            backgroundImage:
              "radial-gradient(circle, rgba(255,255,255,0.4) 1px, transparent 1px)",
            backgroundSize: "30px 30px",
          }}
        ></div>

        {/* أشكال هندسية */}
        <div className="absolute top-10 left-10 w-40 h-40 rounded-full bg-white opacity-5 blur-3xl"></div>
        <div className="absolute bottom-10 right-10 w-60 h-60 rounded-full bg-indigo-300 opacity-5 blur-3xl"></div>

        {/* تدرجات لونية */}
        <div className="absolute top-0 left-0 w-full h-20 bg-gradient-to-b from-white/10 to-transparent"></div>
        <div className="absolute bottom-0 left-0 w-full h-20 bg-gradient-to-t from-black/20 to-transparent"></div>
      </div>

      {/* تعريف الرسوم المتحركة */}
      <style
        dangerouslySetInnerHTML={{
          __html: `
        @keyframes pulse {
          0% {
            box-shadow: 0 0 0 0 rgba(99, 102, 241, 0.6);
          }
          70% {
            box-shadow: 0 0 0 15px rgba(99, 102, 241, 0);
          }
          100% {
            box-shadow: 0 0 0 0 rgba(99, 102, 241, 0);
          }
        }

        @keyframes float {
          0% {
            transform: translateY(0px);
          }
          50% {
            transform: translateY(-10px);
          }
          100% {
            transform: translateY(0px);
          }
        }
      `,
        }}
      />

      <div className="container mx-auto px-4 relative z-10">
        <div className="flex flex-col lg:flex-row items-center gap-12">
          {/* القسم الأيسر - النص والأزرار */}
          <div className="lg:w-1/2 text-center lg:text-right">
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              className="mb-4"
            >
              <span className="inline-block px-6 py-2 rounded-full text-sm font-semibold bg-white/10 border border-white/20 backdrop-blur-sm">
                منصة رائدة في سوريا
              </span>
            </motion.div>

            <motion.h2
              className="text-4xl md:text-5xl font-bold mb-8 relative inline-block"
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <span className="relative z-10">انضم إلى منصتنا اليوم</span>
              <span
                className={`absolute bottom-0 left-0 right-0 h-4 ${
                  darkMode ? "bg-indigo-500" : "bg-indigo-300"
                } opacity-30 transform -rotate-1 z-0`}
              ></span>
            </motion.h2>

            <motion.p
              className="text-xl mb-10 max-w-xl mx-auto lg:mx-0 leading-relaxed"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              سواء كنت حرفياً تبحث عن فرص عمل جديدة أو كنت تبحث عن حرفي ماهر
              لإنجاز مهمة ما، منصة{" "}
              <span className="font-bold text-indigo-300">
                {settings?.siteName || "JobScope"}
              </span>{" "}
              هي الحل الأمثل لك.
            </motion.p>

            <motion.div
              className="flex flex-col sm:flex-row gap-6 justify-center lg:justify-start"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.4 }}
            >
              <Link to="/register/craftsman">
                <Button
                  variant="primary"
                  className="bg-gradient-to-r from-indigo-500 to-blue-600 hover:from-indigo-600 hover:to-blue-700 text-white shadow-xl hover:shadow-2xl transition-all duration-200 relative overflow-hidden group text-lg py-4 px-8 rounded-xl"
                  style={{ animation: "pulse 2s infinite" }}
                >
                  <span className="relative z-10 font-bold flex items-center gap-2">
                    <Briefcase size={20} />
                    سجل كحرفي
                  </span>
                  <span className="absolute inset-0 bg-white opacity-20 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700"></span>
                </Button>
              </Link>
              <Link to="/register/client">
                <Button
                  variant="outline"
                  className="border-2 border-white/70 text-white hover:bg-white/10 shadow-lg hover:shadow-xl transition-all duration-200 relative overflow-hidden group text-lg py-4 px-8 rounded-xl"
                >
                  <span className="relative z-10 font-bold flex items-center gap-2">
                    <User size={20} />
                    سجل كطالب خدمة
                  </span>
                  <span className="absolute inset-0 bg-white opacity-0 transform -skew-x-12 -translate-x-full group-hover:translate-x-full group-hover:opacity-20 transition-all duration-700"></span>
                </Button>
              </Link>
            </motion.div>
          </div>

          {/* القسم الأيمن - مميزات المنصة */}
          <motion.div
            className="lg:w-1/2"
            variants={containerVariants}
            initial="hidden"
            animate="visible"
          >
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {features.map((feature, index) => (
                <motion.div
                  key={feature.title}
                  className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20 hover:bg-white/15 transition-all duration-300"
                  variants={itemVariants}
                  style={{
                    animation: `float ${3 + index * 0.5}s ease-in-out infinite`,
                  }}
                >
                  <div className="flex items-start gap-4">
                    <div className="bg-white/20 p-3 rounded-lg">
                      {feature.icon}
                    </div>
                    <div>
                      <h3 className="text-xl font-bold mb-2">
                        {feature.title}
                      </h3>
                      <p className="text-white/80">{feature.description}</p>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default CTASection;
