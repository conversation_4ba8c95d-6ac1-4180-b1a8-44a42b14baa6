import React, { useState, useEffect } from "react";
import { Link, useNavigate } from "react-router-dom";
import { motion } from "framer-motion";
import { Search, UserCheck } from "lucide-react";
import Button from "../../../components/common/Button";
import useThemeStore from "../../../store/themeStore";
import useUserStore from "../../../store/userStore";
import useSiteSettingsStore from "../../../store/siteSettingsStore";
import { LazyLoadImage } from "react-lazy-load-image-component";
import "react-lazy-load-image-component/src/effects/blur.css";

const HeroSection = () => {
  const darkMode = useThemeStore((state) => state.darkMode);
  const navigate = useNavigate();
  const user = useUserStore((state) => state.user);
  const { settings, fetchSettings } = useSiteSettingsStore();
  const isLoggedIn = !!user;
  const [imageSrc, setImageSrc] = useState({
    large: "",
    medium: "",
    small: "",
    placeholder: "",
  });

  // جلب إعدادات الموقع
  useEffect(() => {
    fetchSettings();
  }, [fetchSettings]);

  useEffect(() => {
    // استخدام صور بديلة من مواقع خارجية موثوقة
    const defaultImages = {
      large: "https://www.raed.net/img?id=1312374",
      medium: "https://www.raed.net/img?id=1312376",
      small: "https://www.raed.net/img?id=1312377",
      placeholder: "https://www.raed.net/img?id=1312374",
    };

    // محاولة تحميل الصورة باستخدام import.meta.url
    try {
      // تحديد مسارات الصور بأحجام مختلفة
      const imgUrlLarge = new URL(
        "https://www.raed.net/img?id=1312374",
        import.meta.url
      ).href;
      const imgUrlMedium = new URL(
        "https://www.raed.net/img?id=1312376",
        import.meta.url
      ).href;
      const imgUrlSmall = new URL(
        "https://www.raed.net/img?id=1312377",
        import.meta.url
      ).href;

      // استخدام الصورة الكبيرة كمصدر أساسي
      setImageSrc({
        large: imgUrlLarge,
        medium: imgUrlMedium || imgUrlLarge, // استخدام الصورة الكبيرة كبديل إذا لم تكن الصورة المتوسطة متوفرة
        small: imgUrlSmall || imgUrlLarge, // استخدام الصورة الكبيرة كبديل إذا لم تكن الصورة الصغيرة متوفرة
        placeholder: imgUrlSmall || imgUrlLarge, // استخدام الصورة الصغيرة كصورة مؤقتة أثناء التحميل
      });
    } catch (error) {
      console.error("Error loading images with import.meta.url:", error);
      // استخدام الصور البديلة من مواقع خارجية
      setImageSrc(defaultImages);
    }
  }, []);

  return (
    <section
      className={`${
        darkMode
          ? "bg-gradient-to-br from-gray-900 via-indigo-950 to-gray-900"
          : "bg-gradient-to-br from-indigo-900 via-blue-800 to-indigo-800"
      } text-white py-8 pt-6 transition-colors duration-300 relative overflow-hidden`}
    >
      {/* أشكال زخرفية في الخلفية */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-20 left-10 w-64 h-64 rounded-full bg-blue-500 opacity-10 blur-3xl"></div>
        <div className="absolute bottom-10 right-10 w-80 h-80 rounded-full bg-indigo-600 opacity-10 blur-3xl"></div>
        <div className="absolute top-40 right-1/4 w-40 h-40 rounded-full bg-purple-500 opacity-10 blur-3xl"></div>
      </div>
      <style
        dangerouslySetInnerHTML={{
          __html: `
        @keyframes pulse {
          0% {
            box-shadow: 0 0 0 0 rgba(99, 102, 241, 0.4);
          }
          70% {
            box-shadow: 0 0 0 10px rgba(99, 102, 241, 0);
          }
          100% {
            box-shadow: 0 0 0 0 rgba(99, 102, 241, 0);
          }
        }
      `,
        }}
      />
      <div className="container mx-auto px-4">
        <div className="flex flex-col md:flex-row items-center justify-between gap-2">
          <motion.div
            className="md:w-1/2 mb-4 md:mb-0"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <div className="relative mb-2">
              <motion.span
                className="inline-block text-indigo-300 text-lg font-semibold mb-2 bg-indigo-900/30 px-4 py-1 rounded-full border border-indigo-700/50"
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 }}
              >
                منصة {settings?.siteName || "JobScope"} - الأولى في سوريا
              </motion.span>
            </div>
            <h1
              className={`text-4xl md:text-6xl font-bold mb-4 relative ${
                darkMode ? "text-white" : ""
              }`}
            >
              <span className="relative z-10 leading-tight block">
                بوابتك <span className="text-indigo-300">الذكية</span> للوصول
                إلى
                <br />
                أفضل <span className="text-indigo-300">الحرفيين</span> في سوريا
              </span>
              <span
                className={`absolute bottom-0 left-0 right-0 h-4 ${
                  darkMode ? "bg-indigo-500" : "bg-indigo-300"
                } opacity-20 transform -rotate-1 z-0`}
              ></span>
            </h1>
            <p
              className={`text-xl mb-5 ${
                darkMode ? "text-gray-300" : "text-indigo-100"
              } max-w-xl leading-relaxed`}
            >
              ابحث عن أفضل الحرفيين في منطقتك أو سجل كحرفي واستقبل طلبات العمل.
              منصة متكاملة تجمع بين الاحترافية والسهولة.
            </p>
            <div className="flex flex-col sm:flex-row gap-4">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
                className="flex flex-col sm:flex-row gap-4"
              >
                <Link to="/login?type=craftsman">
                  <Button
                    variant="primary"
                    className="bg-gradient-to-r from-indigo-500 to-blue-600 hover:from-indigo-600 hover:to-blue-700 text-white flex items-center justify-center gap-2 shadow-xl hover:shadow-2xl transition-all duration-200 relative overflow-hidden group text-lg py-3 px-6 rounded-xl"
                    style={{ animation: "pulse 2s infinite" }}
                  >
                    <span className="relative z-10 flex items-center justify-center gap-2 font-bold text-white">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-5 w-5"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                        />
                      </svg>
                      تسجيل الدخول كحرفي
                    </span>
                    <span className="absolute inset-0 bg-white opacity-20 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700"></span>
                  </Button>
                </Link>
                <Button
                  variant="outline"
                  className="border-2 border-white/70 text-white hover:bg-white/10 flex items-center justify-center gap-2 shadow-lg hover:shadow-xl transition-all duration-200 relative overflow-hidden group text-lg py-3 px-6 rounded-xl"
                  onClick={() => {
                    // توجيه المستخدم إلى صفحة البحث
                    navigate("/search");
                  }}
                >
                  <span className="relative z-10 flex items-center justify-center gap-2 font-bold text-white">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-5 w-5"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                      />
                    </svg>
                    استكشاف كطالب خدمة
                  </span>
                  <span className="absolute inset-0 bg-white opacity-0 transform -skew-x-12 -translate-x-full group-hover:translate-x-full group-hover:opacity-20 transition-all duration-700"></span>
                </Button>
              </motion.div>
            </div>
          </motion.div>

          <motion.div
            className="md:w-1/2 flex flex-col items-center mt-0"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <div className="flex justify-center w-full relative mb-2">
              {imageSrc.large && (
                <picture className="w-full flex justify-center">
                  {/* استخدام LazyLoadImage مع تأثير blur أثناء التحميل */}
                  <LazyLoadImage
                    src={imageSrc.large}
                    srcSet={`${imageSrc.small} 500w, ${imageSrc.medium} 1000w, ${imageSrc.large} 1500w`}
                    sizes="(max-width: 600px) 500px, (max-width: 1200px) 1000px, 1500px"
                    alt={`${settings?.siteName ||
                      "JobScope"} - منصة سورية لربط طالبي الخدمات بالحرفيين`}
                    className="w-4/5 md:w-3/4 rounded-lg shadow-lg mx-auto"
                    effect="blur"
                    placeholderSrc={imageSrc.placeholder}
                    threshold={300}
                    onError={() => {
                      console.log("Image failed to load, trying fallback path");
                      // استخدام صور بديلة من مواقع خارجية موثوقة
                      setImageSrc({
                        large: "https://www.raed.net/img?id=1312374",
                        medium: "https://www.raed.net/img?id=1312376",
                        small: "https://www.raed.net/img?id=1312377",
                        placeholder: "https://www.raed.net/img?id=1312374",
                      });
                    }}
                  />
                </picture>
              )}
            </div>
            <div
              className={`w-4/5 md:w-3/4 p-5 rounded-xl ${
                darkMode ? "bg-gray-800/80" : "bg-white/90"
              } backdrop-blur-md shadow-xl border ${
                darkMode ? "border-gray-700" : "border-indigo-200"
              } transform hover:scale-[1.02] transition-all duration-300`}
            >
              <h3
                className={`text-xl font-bold mb-3 ${
                  darkMode ? "text-indigo-300" : "text-indigo-700"
                } flex items-center gap-2`}
              >
                <span className="bg-indigo-600 text-white p-1 rounded-md">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M5 13l4 4L19 7"
                    />
                  </svg>
                </span>
                منصة {settings?.siteName || "JobScope"} - مميزات فريدة
              </h3>
              <div
                className={`grid grid-cols-1 md:grid-cols-2 gap-4 ${
                  darkMode ? "text-indigo-200" : "text-indigo-600"
                }`}
              >
                <div className="flex items-center gap-2 bg-indigo-50/20 p-2 rounded-lg hover:bg-indigo-100/30 transition-colors duration-200">
                  <div className="bg-indigo-100 p-1.5 rounded-md">
                    <Search
                      size={16}
                      className="text-indigo-600 flex-shrink-0"
                    />
                  </div>
                  <span className="font-medium">العثور على حرفيين ماهرين</span>
                </div>
                <div className="flex items-center gap-2 bg-indigo-50/20 p-2 rounded-lg hover:bg-indigo-100/30 transition-colors duration-200">
                  <div className="bg-indigo-100 p-1.5 rounded-md">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="text-indigo-600 flex-shrink-0"
                    >
                      <rect width="18" height="18" x="3" y="4" rx="2" ry="2" />
                      <line x1="16" x2="16" y1="2" y2="6" />
                      <line x1="8" x2="8" y1="2" y2="6" />
                      <line x1="3" x2="21" y1="10" y2="10" />
                      <path d="m9 16 2 2 4-4" />
                    </svg>
                  </div>
                  <span className="font-medium">حجز مواعيد الخدمة بسهولة</span>
                </div>
                <div className="flex items-center gap-2 bg-indigo-50/20 p-2 rounded-lg hover:bg-indigo-100/30 transition-colors duration-200">
                  <div className="bg-indigo-100 p-1.5 rounded-md">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="text-indigo-600 flex-shrink-0"
                    >
                      <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
                    </svg>
                  </div>
                  <span className="font-medium">
                    التواصل مباشرة مع الحرفيين
                  </span>
                </div>
                <div className="flex items-center gap-2 bg-indigo-50/20 p-2 rounded-lg hover:bg-indigo-100/30 transition-colors duration-200">
                  <div className="bg-indigo-100 p-1.5 rounded-md">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="text-indigo-600 flex-shrink-0"
                    >
                      <path d="M12 2l2.4 7.4H22l-6 4.6 2.3 7-6.3-4.6L5.7 21l2.3-7-6-4.6h7.6L12 2z" />
                    </svg>
                  </div>
                  <span className="font-medium">تقييمات موثوقة من العملاء</span>
                </div>
                <div className="flex items-center gap-2 md:col-span-2 bg-indigo-50/20 p-2 rounded-lg hover:bg-indigo-100/30 transition-colors duration-200">
                  <div className="bg-indigo-100 p-1.5 rounded-md">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="text-indigo-600 flex-shrink-0"
                    >
                      <rect x="3" y="11" width="18" height="10" rx="2" />
                      <circle cx="12" cy="5" r="2" />
                      <path d="M12 7v4" />
                      <line x1="8" y1="16" x2="8" y2="16" />
                      <line x1="16" y1="16" x2="16" y2="16" />
                    </svg>
                  </div>
                  <span className="font-medium">
                    مساعد ذكي للرد على استفساراتك ومساعدتك
                  </span>
                </div>

                <div className="flex items-center gap-2 md:col-span-2 bg-indigo-50/20 p-2 rounded-lg hover:bg-indigo-100/30 transition-colors duration-200">
                  <div className="bg-indigo-100 p-1.5 rounded-md">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="text-indigo-600 flex-shrink-0"
                    >
                      <circle cx="11" cy="11" r="8" />
                      <path d="m21 21-4.3-4.3" />
                      <path d="m11 8v6" />
                      <path d="M8 11h6" />
                    </svg>
                  </div>
                  <span className="font-medium">
                    بحث ذكي يفهم اللغة العربية الطبيعية (الفصحى أو السورية)
                  </span>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;
