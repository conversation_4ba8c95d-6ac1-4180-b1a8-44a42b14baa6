import React, { useState, useRef, useEffect } from "react";
import { BotMessageSquare, Sparkles } from "lucide-react";
import { useNavigate } from "react-router-dom";
import useThemeStore from "../../store/themeStore";
import useLanguageStore from "../../store/languageStore";
import useUserStore from "../../store/userStore";
import useSiteSettingsStore from "../../store/siteSettingsStore";
import {
  analyzeIntent,
  analyzeContext,
  generateResponse,
} from "../../services/advancedNlpService";
import { predictIntent } from "../../services/intentClassifierService";
import conversationService from "../../services/conversationService";

// استيراد المكونات الفرعية
import ChatHeader from "./components/ChatHeader";
import ChatBody from "./components/ChatBody";
import ChatInput from "./components/ChatInput";
import ChatActions from "./components/ChatActions";

// استيراد البيانات
import faqDatabase from "./data/faqDatabase";
import quickSuggestions from "./data/quickSuggestions";

/**
 * مكون الشات بوت الرئيسي
 */
const Chatbot = () => {
  const [opened, setOpened] = useState(false);
  const [messages, setMessages] = useState([]);
  const [inputValue, setInputValue] = useState("");
  const [suggestions, setSuggestions] = useState(quickSuggestions);
  const [showSuggestions, setShowSuggestions] = useState(true);
  const [isThinking, setIsThinking] = useState(false);
  const [conversation, setConversation] = useState(null);
  const [modelInitialized, setModelInitialized] = useState(false);
  const [isInitializingModel, setIsInitializingModel] = useState(false);
  const [showFAQ, setShowFAQ] = useState(false);
  const messagesEndRef = useRef(null);
  const inputRef = useRef(null);

  const darkMode = useThemeStore((state) => state.darkMode);
  const language = useLanguageStore((state) => state.language);
  const isAuthenticated = useUserStore((state) => state.isAuthenticated);
  const { settings, fetchSettings } = useSiteSettingsStore();
  const navigate = useNavigate();

  // جلب إعدادات الموقع
  useEffect(() => {
    fetchSettings();
  }, [fetchSettings]);

  // إنشاء محادثة جديدة عند فتح الشات بوت
  useEffect(() => {
    if (opened && !conversation) {
      const newConversation = conversationService.createConversation();
      setConversation(newConversation);
    }
  }, [opened, conversation]);

  // التمرير إلى آخر رسالة
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  // إضافة رسالة ترحيبية عند فتح الشات (فقط إذا لم يتم تهيئة النموذج)
  useEffect(() => {
    if (opened && messages.length === 0 && modelInitialized) {
      const welcomeMessage = {
        text: `مرحباً بك في منصة ${settings?.siteName ||
          "JobScope"}! أنا المساعد الذكي الخاص بك. كيف يمكنني مساعدتك اليوم؟`,
        isUser: false,
      };

      setMessages([welcomeMessage]);

      if (conversation) {
        const updatedConversation = conversationService.addMessage(
          conversation,
          welcomeMessage
        );
        setConversation(updatedConversation);
      }
    } else if (
      opened &&
      messages.length === 0 &&
      !modelInitialized &&
      !isInitializingModel
    ) {
      // إذا لم يتم تهيئة النموذج بعد، نضيف رسالة بسيطة
      const simpleWelcomeMessage = {
        text: `مرحباً بك في منصة ${settings?.siteName ||
          "JobScope"}! جاري تحميل المساعد الذكي... هذا التحميل يحدث مرة واحدة فقط عند أول استخدام، وفي المرات القادمة سيكون المساعد جاهزاً فوراً.`,
        isUser: false,
      };

      setMessages([simpleWelcomeMessage]);
    }

    if (opened) {
      setTimeout(() => {
        inputRef.current?.focus();
      }, 100);
    }
  }, [
    opened,
    conversation,
    messages.length,
    modelInitialized,
    isInitializingModel,
  ]);

  // التمرير إلى آخر رسالة عند إضافة رسالة جديدة
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // تهيئة نموذج التعرف على النوايا عند فتح الشات بوت
  useEffect(() => {
    // تهيئة النموذج فقط عند فتح الشات بوت وإذا لم يتم تهيئته من قبل
    if (opened && !modelInitialized && !isInitializingModel) {
      const initModel = async () => {
        try {
          setIsInitializingModel(true);
          console.log("جاري تهيئة نموذج التعرف على النوايا...");

          // إضافة رسالة تحميل للمستخدم
          const loadingMessage = {
            text:
              "جاري تحميل القدرات الذكية... سأكون جاهزًا للمساعدة خلال لحظات.",
            isUser: false,
          };
          setMessages((prev) => [...prev, loadingMessage]);

          // تهيئة النموذج
          await predictIntent("مرحبا");

          setModelInitialized(true);
          console.log("تم تهيئة نموذج التعرف على النوايا بنجاح");

          // إزالة رسالة التحميل وإضافة رسالة الترحيب
          setMessages([
            {
              text: `مرحباً بك في منصة ${settings?.siteName ||
                "JobScope"}! أنا المساعد الذكي الخاص بك. كيف يمكنني مساعدتك اليوم؟`,
              isUser: false,
            },
          ]);
        } catch (error) {
          console.error("خطأ في تهيئة نموذج التعرف على النوايا:", error);

          // إضافة رسالة خطأ للمستخدم
          setMessages((prev) => [
            ...prev.filter(
              (msg) => !msg.text.includes("جاري تحميل القدرات الذكية")
            ),
            {
              text:
                "مرحباً بك! يمكنني مساعدتك بالإجابة على الأسئلة الشائعة. بعض الميزات المتقدمة قد لا تكون متاحة حاليًا.",
              isUser: false,
            },
          ]);
        } finally {
          setIsInitializingModel(false);
        }
      };

      initModel();
    }
  }, [opened, modelInitialized, isInitializingModel]);

  // معالجة اختيار سؤال من الأسئلة الشائعة
  const handleSelectFAQ = (question) => {
    // إغلاق قسم الأسئلة الشائعة
    setShowFAQ(false);

    // إضافة السؤال كرسالة من المستخدم
    const userMessage = { text: question, isUser: true };
    setMessages((prev) => [...prev, userMessage]);
    setIsThinking(true);

    // تحديث المحادثة
    if (conversation) {
      const updatedConversation = conversationService.addMessage(
        conversation,
        userMessage
      );
      setConversation(updatedConversation);
    }

    // البحث عن الإجابة في قاعدة بيانات الأسئلة الشائعة
    let answer = null;
    for (const [faqQuestion, faqAnswer] of Object.entries(faqDatabase)) {
      if (faqQuestion === question) {
        answer = faqAnswer;
        break;
      }
    }

    // إضافة الإجابة كرسالة من البوت
    setTimeout(() => {
      const botMessage = {
        text: answer || "عذراً، لم أتمكن من العثور على إجابة لهذا السؤال.",
        isUser: false,
      };

      setIsThinking(false);
      setMessages((prev) => [...prev, botMessage]);
      setShowSuggestions(true);

      // تحديث المحادثة
      if (conversation) {
        const updatedConversation = conversationService.addMessage(
          conversation,
          botMessage
        );
        setConversation(updatedConversation);
      }
    }, 500);
  };

  // إرسال رسالة
  const handleSendMessage = async () => {
    if (!inputValue.trim()) return;

    // إضافة رسالة المستخدم
    const userMessage = { text: inputValue, isUser: true };
    const userInput = inputValue; // حفظ قيمة الإدخال قبل إعادة تعيينها
    setMessages((prev) => [...prev, userMessage]);
    setInputValue("");
    setShowSuggestions(false);
    setIsThinking(true);

    // تحديث المحادثة
    if (conversation) {
      const updatedConversation = conversationService.addMessage(
        conversation,
        userMessage
      );
      setConversation(updatedConversation);
    }

    // معالجة الرسالة وإضافة رد البوت
    setTimeout(async () => {
      try {
        // البحث في قاعدة بيانات الأسئلة الشائعة أولاً
        let botResponse = null;
        let bestMatchScore = 0;
        let bestMatchAnswer = null;

        // تحويل النص إلى أحرف صغيرة وإزالة علامات الترقيم للمقارنة
        const normalizedInput = userInput.trim().replace(/[؟.,!]/g, "");

        for (const [question, answer] of Object.entries(faqDatabase)) {
          // تحويل السؤال إلى أحرف صغيرة وإزالة علامات الترقيم للمقارنة
          const normalizedQuestion = question.trim().replace(/[؟.,!]/g, "");

          // المطابقة المباشرة
          if (normalizedInput === normalizedQuestion) {
            botResponse = { text: answer, isUser: false };
            break;
          }

          // المطابقة الجزئية
          if (
            normalizedInput.includes(normalizedQuestion) ||
            normalizedQuestion.includes(normalizedInput)
          ) {
            // حساب درجة التطابق بناءً على طول النص المشترك
            const matchLength = Math.min(
              normalizedInput.length,
              normalizedQuestion.length
            );
            const score =
              matchLength /
              Math.max(normalizedInput.length, normalizedQuestion.length);

            if (score > bestMatchScore) {
              bestMatchScore = score;
              bestMatchAnswer = answer;
            }
          }
        }

        // إذا لم يتم العثور على مطابقة مباشرة ولكن هناك مطابقة جزئية جيدة
        if (!botResponse && bestMatchScore > 0.7) {
          botResponse = { text: bestMatchAnswer, isUser: false };
        }

        // إذا لم يتم العثور على إجابة في قاعدة البيانات، استخدم الذكاء الاصطناعي
        if (!botResponse) {
          // تحليل النية باستخدام الخدمة المتقدمة
          const intentAnalysis = analyzeIntent(userInput);

          // تحسين التحليل باستخدام سياق المحادثة
          const contextAnalysis = analyzeContext(messages, intentAnalysis);

          // استخدام نموذج التعرف على النوايا للتحقق (فقط إذا تم تهيئة النموذج)
          const tfPrediction = modelInitialized
            ? await predictIntent(userInput)
            : { intent: "unknown", confidence: 0 };

          // دمج النتائج (استخدام النتيجة ذات الثقة الأعلى)
          const finalAnalysis =
            tfPrediction.confidence > contextAnalysis.confidence
              ? {
                  ...contextAnalysis,
                  intent: tfPrediction.intent,
                  confidence: tfPrediction.confidence,
                }
              : contextAnalysis;

          // توليد الرد المناسب
          const response = generateResponse(finalAnalysis);

          botResponse = { text: response.text, isUser: false };
          setSuggestions(response.suggestions);

          // تحديث حالة المحادثة
          if (conversation) {
            const updatedState = conversationService.updateState(
              conversation,
              finalAnalysis.intent
            );
            const updatedContext = conversationService.updateContext(
              updatedState,
              {
                lastIntent: finalAnalysis.intent,
                profession: finalAnalysis.profession,
                city: finalAnalysis.city,
                hospital: finalAnalysis.hospital,
                mosque: finalAnalysis.mosque,
              }
            );
            setConversation(updatedContext);
          }
        }

        setIsThinking(false);
        setMessages((prev) => [...prev, botResponse]);
        setShowSuggestions(true);

        // تحديث المحادثة
        if (conversation) {
          const updatedConversation = conversationService.addMessage(
            conversation,
            botResponse
          );
          setConversation(updatedConversation);
        }
      } catch (error) {
        console.error("خطأ في معالجة الرسالة:", error);
        setIsThinking(false);
        setMessages((prev) => [
          ...prev,
          {
            text:
              "عذراً، حدث خطأ أثناء معالجة رسالتك. الرجاء المحاولة مرة أخرى.",
            isUser: false,
          },
        ]);
        setShowSuggestions(true);
      }
    }, 1000);
  };

  // اختيار اقتراح
  const handleSelectSuggestion = (suggestion) => {
    setInputValue(suggestion);
    handleSendMessage();
  };

  // التنقل إلى صفحة أخرى
  const handleNavigate = (path) => {
    setOpened(false);
    navigate(path);
  };

  // النصوص حسب اللغة
  const texts = {
    ar: {
      title: `مساعدك الذكي ${settings?.siteName || "JobScope"}`,
      placeholder: "اكتب رسالتك هنا...",
      send: "إرسال",
    },
    en: {
      title: `${settings?.siteName || "JobScope"} AI Assistant`,
      placeholder: "Type your message here...",
      send: "Send",
    },
  };

  const t = texts[language];

  // لا يظهر الشات بوت إذا لم يكن المستخدم مسجل الدخول
  if (!isAuthenticated) return null;

  return (
    <>
      {/* طبقة تغطية الصفحة أثناء تحميل النموذج */}
      {opened && isInitializingModel && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-[200] cursor-not-allowed flex items-center justify-center">
          <div
            className={`${
              darkMode ? "bg-gray-800 text-white" : "bg-white text-gray-800"
            } p-6 rounded-lg shadow-xl flex flex-col items-center`}
          >
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500 mb-4"></div>
            <p className="text-lg font-semibold">جاري تحميل المساعد الذكي...</p>
            <p
              className={`text-sm ${
                darkMode ? "text-gray-300" : "text-gray-500"
              } mt-2`}
            >
              يرجى الانتظار قليلاً
            </p>
            <p
              className={`text-xs ${
                darkMode ? "text-gray-400" : "text-gray-600"
              } mt-3 text-center max-w-xs`}
            >
              هذا التحميل يحدث فقط عند أول استخدام للمساعد الذكي. في المرات
              القادمة، سيظهر المساعد فوراً دون انتظار.
            </p>
          </div>
        </div>
      )}

      {/* زر فتح المحادثة */}
      {!opened && (
        <button
          onClick={() => setOpened(true)}
          className={`fixed bottom-6 left-6 w-14 h-14 rounded-full flex items-center justify-center shadow-lg z-[100] transition-all duration-200 hover:scale-105 ${
            darkMode
              ? "bg-indigo-600 hover:bg-indigo-700"
              : "bg-indigo-500 hover:bg-indigo-600"
          } animate-pulse-subtle`}
        >
          <div className="relative">
            <BotMessageSquare size={24} className="text-white" />
            <div className="absolute -top-1 -right-1">
              <Sparkles
                size={12}
                className="text-yellow-300 animate-sparkle-spin"
              />
            </div>
          </div>
        </button>
      )}

      {/* نافذة المحادثة */}
      {opened && (
        <div
          className={`fixed bottom-6 left-6 z-[100] shadow-xl rounded-lg overflow-hidden ${
            darkMode ? "border border-gray-700" : "border border-gray-200"
          } animate-fadeIn w-[350px]`}
        >
          {/* رأس المحادثة */}
          <ChatHeader
            title={t.title}
            darkMode={darkMode}
            onClose={() => setOpened(false)}
            showFAQ={showFAQ}
            toggleFAQ={() => setShowFAQ(!showFAQ)}
          />

          {/* أزرار الإجراءات السريعة */}
          <ChatActions darkMode={darkMode} onNavigate={handleNavigate} />

          {/* جسم المحادثة */}
          <ChatBody
            messages={messages}
            darkMode={darkMode}
            isThinking={isThinking}
            showSuggestions={showSuggestions}
            suggestions={suggestions}
            onSelectSuggestion={handleSelectSuggestion}
            isInitializingModel={isInitializingModel}
            showFAQ={showFAQ}
            onSelectFAQ={handleSelectFAQ}
            messagesEndRef={messagesEndRef}
          />

          {/* مدخل الرسائل - يظهر فقط في وضع المحادثة */}
          {!showFAQ && (
            <ChatInput
              value={inputValue}
              onChange={setInputValue}
              onSubmit={handleSendMessage}
              placeholder={
                isInitializingModel
                  ? "جاري تحميل المساعد الذكي..."
                  : t.placeholder
              }
              darkMode={darkMode}
              dir={language === "ar" ? "rtl" : "ltr"}
              disabled={isThinking || isInitializingModel}
              inputRef={inputRef}
            />
          )}
        </div>
      )}
    </>
  );
};

export default Chatbot;
