import { useState, useEffect } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import { motion } from "framer-motion";
import Layout from "../../components/layout/Layout";
import Card from "../../components/common/Card";
import LoadingButton from "../../components/common/LoadingButton";
import useThemeStore from "../../store/themeStore";
import supabaseAuthService from "../../services/supabaseAuthService";
import { supabase } from "../../config/supabase";
import { showToast } from "../../utils/toastUtils";
import { Eye, EyeOff, Lock, CheckCircle } from "lucide-react";

const ResetPasswordPage = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const darkMode = useThemeStore((state) => state.darkMode);

  const [formData, setFormData] = useState({
    newPassword: "",
    confirmPassword: "",
  });
  const [errors, setErrors] = useState({});
  const [isLoading, setIsLoading] = useState(false);
  const [showPasswords, setShowPasswords] = useState({
    newPassword: false,
    confirmPassword: false,
  });
  const [isValidToken, setIsValidToken] = useState(false);
  const [isCheckingToken, setIsCheckingToken] = useState(true);

  // التحقق من صحة الرمز المميز عند تحميل الصفحة
  useEffect(() => {
    const checkToken = async () => {
      try {
        console.log("Current URL:", window.location.href);
        console.log(
          "Search params:",
          Object.fromEntries(searchParams.entries())
        );

        // محاولة الحصول على الرموز المميزة من URL بطرق مختلفة
        let accessToken = searchParams.get("access_token");
        let refreshToken = searchParams.get("refresh_token");
        const type = searchParams.get("type");
        const error = searchParams.get("error");
        const errorDescription = searchParams.get("error_description");

        // التحقق من وجود خطأ في URL
        if (error) {
          console.error("URL contains error:", error, errorDescription);
          showToast(
            `خطأ في الرابط: ${errorDescription || error}`,
            "error",
            3000
          );
          navigate("/login");
          return;
        }

        // محاولة الحصول على الجلسة من Supabase مباشرة
        const {
          data: sessionData,
          error: sessionError,
        } = await supabase.auth.getSession();
        console.log("Current session:", sessionData, sessionError);

        // إذا لم نجد الرموز في URL، نحاول الحصول عليها من hash
        if (!accessToken) {
          const hash = window.location.hash;
          console.log("URL hash:", hash);

          if (hash) {
            const hashParams = new URLSearchParams(hash.substring(1));
            accessToken = hashParams.get("access_token");
            refreshToken = hashParams.get("refresh_token");
            console.log("Tokens from hash:", {
              accessToken: !!accessToken,
              refreshToken: !!refreshToken,
            });
          }
        }

        // محاولة استخدام الجلسة الحالية إذا كانت متاحة
        if (sessionData?.session && !accessToken) {
          accessToken = sessionData.session.access_token;
          refreshToken = sessionData.session.refresh_token;
          console.log("Using current session tokens");
        }

        // التحقق من وجود الرموز المطلوبة
        if (!accessToken) {
          console.error("No access token found in URL or session");
          showToast(
            "رابط إعادة تعيين كلمة المرور غير صالح أو منتهي الصلاحية",
            "error",
            3000
          );
          navigate("/login");
          return;
        }

        // التحقق من نوع العملية (إذا كان متاحاً)
        if (type && type !== "recovery") {
          console.error("Invalid type:", type);
          showToast("نوع العملية غير صحيح", "error", 3000);
          navigate("/login");
          return;
        }

        console.log("Verifying token with Supabase...");
        // التحقق من صحة الرمز المميز مع Supabase
        const result = await supabaseAuthService.verifyPasswordResetToken(
          accessToken,
          refreshToken
        );

        if (result.success) {
          console.log("Token verification successful");
          setIsValidToken(true);
        } else {
          console.error("Token verification failed:", result.error);
          showToast(
            result.error?.message ||
              "رابط إعادة تعيين كلمة المرور غير صالح أو منتهي الصلاحية",
            "error",
            3000
          );
          navigate("/login");
        }
      } catch (error) {
        console.error("Error checking reset token:", error);
        showToast("حدث خطأ في التحقق من الرابط", "error", 3000);
        navigate("/login");
      } finally {
        setIsCheckingToken(false);
      }
    };

    checkToken();
  }, [searchParams, navigate]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });

    // مسح الأخطاء عند كتابة المستخدم
    if (errors[name]) {
      setErrors({
        ...errors,
        [name]: "",
      });
    }
  };

  const togglePasswordVisibility = (field) => {
    setShowPasswords({
      ...showPasswords,
      [field]: !showPasswords[field],
    });
  };

  const validateForm = () => {
    let isValid = true;
    const newErrors = {};

    // التحقق من كلمة المرور الجديدة
    if (!formData.newPassword) {
      newErrors.newPassword = "كلمة المرور الجديدة مطلوبة";
      isValid = false;
    } else if (formData.newPassword.length < 6) {
      newErrors.newPassword = "يجب أن تتكون كلمة المرور من 6 أحرف على الأقل";
      isValid = false;
    }

    // التحقق من تأكيد كلمة المرور
    if (!formData.confirmPassword) {
      newErrors.confirmPassword = "تأكيد كلمة المرور مطلوب";
      isValid = false;
    } else if (formData.newPassword !== formData.confirmPassword) {
      newErrors.confirmPassword = "كلمات المرور غير متطابقة";
      isValid = false;
    }

    setErrors(newErrors);
    return isValid;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (validateForm()) {
      setIsLoading(true);
      try {
        // الحصول على الرموز المميزة بنفس الطريقة المستخدمة في التحقق
        let accessToken = searchParams.get("access_token");
        let refreshToken = searchParams.get("refresh_token");

        // إذا لم نجد الرموز في URL، نحاول الحصول عليها من hash
        if (!accessToken) {
          const hash = window.location.hash;
          if (hash) {
            const hashParams = new URLSearchParams(hash.substring(1));
            accessToken = hashParams.get("access_token");
            refreshToken = hashParams.get("refresh_token");
          }
        }

        // محاولة استخدام الجلسة الحالية إذا كانت متاحة
        if (!accessToken) {
          const { data: sessionData } = await supabase.auth.getSession();
          if (sessionData?.session) {
            accessToken = sessionData.session.access_token;
            refreshToken = sessionData.session.refresh_token;
          }
        }

        if (!accessToken) {
          showToast(
            "لم يتم العثور على رمز المصادقة. يرجى المحاولة مرة أخرى.",
            "error",
            3000
          );
          navigate("/login");
          return;
        }

        console.log("Updating password...");

        // إعادة تعيين كلمة المرور
        const result = await supabaseAuthService.updatePassword(
          formData.newPassword,
          accessToken,
          refreshToken
        );

        if (result.success) {
          showToast("تم تغيير كلمة المرور بنجاح", "success", 3000);

          // انتظار قليل ثم التوجيه إلى صفحة تسجيل الدخول
          setTimeout(() => {
            navigate("/login", {
              state: {
                message:
                  "تم تغيير كلمة المرور بنجاح. يرجى تسجيل الدخول بكلمة المرور الجديدة.",
              },
            });
          }, 2000);
        } else {
          console.error("Password update failed:", result.error);
          showToast(
            result.error?.message || "حدث خطأ أثناء تغيير كلمة المرور",
            "error",
            3000
          );
        }
      } catch (error) {
        console.error("Error resetting password:", error);
        showToast("حدث خطأ أثناء تغيير كلمة المرور", "error", 3000);
      } finally {
        setIsLoading(false);
      }
    }
  };

  if (isCheckingToken) {
    return (
      <Layout>
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4"></div>
            <p className={`${darkMode ? "text-gray-300" : "text-gray-600"}`}>
              جاري التحقق من الرابط...
            </p>
          </div>
        </div>
      </Layout>
    );
  }

  if (!isValidToken) {
    return null; // سيتم التوجيه إلى صفحة تسجيل الدخول
  }

  return (
    <Layout>
      <div className="min-h-screen bg-gradient-to-b from-blue-200 via-indigo-300 to-blue-200 flex items-center justify-center p-4">
        <div className="w-full max-w-md">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <Card
              className={`p-8 shadow-2xl ${
                darkMode
                  ? "bg-gray-800 border-gray-700"
                  : "bg-white border-gray-200"
              }`}
            >
              <div className="text-center mb-8">
                <div
                  className={`mx-auto w-16 h-16 rounded-full flex items-center justify-center mb-4 ${
                    darkMode ? "bg-indigo-900/50" : "bg-indigo-100"
                  }`}
                >
                  <Lock
                    className={`w-8 h-8 ${
                      darkMode ? "text-indigo-400" : "text-indigo-600"
                    }`}
                  />
                </div>
                <h1
                  className={`text-2xl font-bold mb-2 ${
                    darkMode ? "text-white" : "text-gray-900"
                  }`}
                >
                  إعادة تعيين كلمة المرور
                </h1>
                <p
                  className={`${darkMode ? "text-gray-400" : "text-gray-600"}`}
                >
                  أدخل كلمة المرور الجديدة
                </p>
              </div>

              <form onSubmit={handleSubmit}>
                <div className="mb-4">
                  <label
                    className={`block font-medium mb-2 ${
                      darkMode ? "text-gray-300" : "text-gray-700"
                    }`}
                  >
                    كلمة المرور الجديدة <span className="text-red-500">*</span>
                  </label>
                  <div className="relative">
                    <input
                      type={showPasswords.newPassword ? "text" : "password"}
                      name="newPassword"
                      value={formData.newPassword}
                      onChange={handleInputChange}
                      className={`w-full pl-10 pr-4 py-3 text-left border rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors ${
                        errors.newPassword
                          ? "border-red-500 focus:ring-red-500"
                          : darkMode
                          ? "border-gray-600 bg-gray-700 text-white"
                          : "border-gray-300 bg-white text-gray-900"
                      }`}
                      style={{ direction: "ltr" }}
                      placeholder="أدخل كلمة المرور الجديدة"
                      required
                    />
                    <button
                      type="button"
                      onClick={() => togglePasswordVisibility("newPassword")}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
                    >
                      {showPasswords.newPassword ? (
                        <EyeOff size={20} />
                      ) : (
                        <Eye size={20} />
                      )}
                    </button>
                  </div>
                  {errors.newPassword && (
                    <p className="text-red-500 text-sm mt-1">
                      {errors.newPassword}
                    </p>
                  )}
                </div>

                <div className="mb-6">
                  <label
                    className={`block font-medium mb-2 ${
                      darkMode ? "text-gray-300" : "text-gray-700"
                    }`}
                  >
                    تأكيد كلمة المرور <span className="text-red-500">*</span>
                  </label>
                  <div className="relative">
                    <input
                      type={showPasswords.confirmPassword ? "text" : "password"}
                      name="confirmPassword"
                      value={formData.confirmPassword}
                      onChange={handleInputChange}
                      className={`w-full pl-10 pr-4 py-3 text-left border rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors ${
                        errors.confirmPassword
                          ? "border-red-500 focus:ring-red-500"
                          : darkMode
                          ? "border-gray-600 bg-gray-700 text-white"
                          : "border-gray-300 bg-white text-gray-900"
                      }`}
                      style={{ direction: "ltr" }}
                      placeholder="أعد إدخال كلمة المرور"
                      required
                    />
                    <button
                      type="button"
                      onClick={() =>
                        togglePasswordVisibility("confirmPassword")
                      }
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
                    >
                      {showPasswords.confirmPassword ? (
                        <EyeOff size={20} />
                      ) : (
                        <Eye size={20} />
                      )}
                    </button>
                  </div>
                  {errors.confirmPassword && (
                    <p className="text-red-500 text-sm mt-1">
                      {errors.confirmPassword}
                    </p>
                  )}
                </div>

                <LoadingButton
                  type="submit"
                  variant="primary"
                  fullWidth
                  isLoading={isLoading}
                  loadingText="جاري تغيير كلمة المرور..."
                  className={`${
                    darkMode
                      ? "bg-gradient-to-r from-indigo-600 to-purple-700 hover:from-indigo-700 hover:to-purple-800"
                      : "bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700"
                  } text-white transition-all duration-200 shadow-md hover:shadow-lg py-3`}
                >
                  <span className="flex items-center justify-center gap-2">
                    <CheckCircle size={20} />
                    تغيير كلمة المرور
                  </span>
                </LoadingButton>
              </form>

              <div className="mt-6 text-center">
                <button
                  onClick={() => navigate("/login")}
                  className={`text-sm ${
                    darkMode
                      ? "text-indigo-400 hover:text-indigo-300"
                      : "text-indigo-600 hover:text-indigo-700"
                  } transition-colors duration-200 hover:underline`}
                >
                  العودة إلى تسجيل الدخول
                </button>
              </div>
            </Card>
          </motion.div>
        </div>
      </div>
    </Layout>
  );
};

export default ResetPasswordPage;
