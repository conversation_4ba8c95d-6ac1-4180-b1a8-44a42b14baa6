import React from "react";
import useSiteSettingsStore from "../../store/siteSettingsStore";
import useThemeStore from "../../store/themeStore";

// مكون لعرض معلومات الموقع في أي مكان في التطبيق
const SiteInfo = ({ type }) => {
  const { settings } = useSiteSettingsStore();
  const darkMode = useThemeStore((state) => state.darkMode);

  // عرض اسم الموقع
  if (type === "name") {
    return <span>{settings?.siteName}</span>;
  }

  // عرض شعار الموقع
  if (type === "logo") {
    return (
      <img
        src={settings?.siteLogo}
        alt={settings?.siteName}
        className="h-10 w-auto"
        onError={(e) => {
          e.target.src = "/logo.png";
        }}
      />
    );
  }

  // عرض وصف الموقع
  if (type === "description") {
    return (
      <p className={`${darkMode ? "text-gray-300" : "text-gray-600"}`}>
        {settings?.description}
      </p>
    );
  }

  // عرض معلومات الاتصال
  if (type === "contact") {
    return (
      <div className="space-y-2">
        <p className={`${darkMode ? "text-gray-300" : "text-gray-600"}`}>
          <strong>البريد الإلكتروني:</strong> {settings?.contactEmail}
        </p>
        <p className={`${darkMode ? "text-gray-300" : "text-gray-600"}`}>
          <strong>الهاتف:</strong> {settings?.contactPhone}
        </p>
        <p className={`${darkMode ? "text-gray-300" : "text-gray-600"}`}>
          <strong>العنوان:</strong> {settings?.siteAddress}
        </p>
        <p className={`${darkMode ? "text-gray-300" : "text-gray-600"}`}>
          <strong>ساعات العمل:</strong> {settings?.siteWorkingHours}
        </p>
      </div>
    );
  }

  // عرض جميع المعلومات
  return (
    <div className="space-y-4">
      <div className="flex items-center space-x-3 space-x-reverse">
        <img
          src={settings?.siteLogo}
          alt={settings?.siteName}
          className="h-12 w-auto"
          onError={(e) => {
            e.target.src = "/logo.png";
          }}
        />
        <h2
          className={`text-xl font-bold ${
            darkMode ? "text-white" : "text-gray-800"
          }`}
        >
          {settings?.siteName}
        </h2>
      </div>

      <p className={`${darkMode ? "text-gray-300" : "text-gray-600"}`}>
        {settings?.description}
      </p>

      <div className="space-y-1">
        <p className={`${darkMode ? "text-gray-300" : "text-gray-600"}`}>
          <strong>البريد الإلكتروني:</strong> {settings?.contactEmail}
        </p>
        <p className={`${darkMode ? "text-gray-300" : "text-gray-600"}`}>
          <strong>الهاتف:</strong> {settings?.contactPhone}
        </p>
        <p className={`${darkMode ? "text-gray-300" : "text-gray-600"}`}>
          <strong>العنوان:</strong> {settings?.siteAddress}
        </p>
        <p className={`${darkMode ? "text-gray-300" : "text-gray-600"}`}>
          <strong>ساعات العمل:</strong> {settings?.siteWorkingHours}
        </p>
      </div>
    </div>
  );
};

export default SiteInfo;
