import React from "react";
import ReactD<PERSON> from "react-dom/client";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "react-router-dom";
import { GoogleOAuthProvider } from "@react-oauth/google";
import App from "./App";
import "../index.css";
import "./styles/error-flash.css";
import "./styles/toast.css";
import "./styles/notification-badge.css";
import "./styles/animations.css";
import "./styles/scroll-behavior.css";
import LanguageProvider from "./components/LanguageProvider";

ReactDOM.createRoot(document.getElementById("root")).render(
  <React.StrictMode>
    <GoogleOAuthProvider clientId="79461320705-ru94lkf71prenrqpv9v9pnvnlvndcseb.apps.googleusercontent.com">
      <BrowserRouter>
        <LanguageProvider>
          <App />
        </LanguageProvider>
      </BrowserRouter>
    </GoogleOAuthProvider>
  </React.StrictMode>
);
