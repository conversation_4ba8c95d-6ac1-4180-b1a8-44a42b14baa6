import React, { useState, useEffect, useRef } from "react";
import { motion } from "framer-motion";
import {
  Calendar,
  Clock,
  User,
  FileText,
  CheckCircle,
  XCircle,
  Star,
  Edit,
  ThumbsUp,
  Loader,
} from "lucide-react";
import Button from "../common/Button";
import useThemeStore from "../../store/themeStore";
import useBookingStore from "../../store/bookingStore";
import useReviewStore from "../../store/reviewStore";
import BookingEditForm from "./BookingEditForm";
import ReviewModal from "../reviews/ReviewModal";
import LazyImage from "../common/LazyImage";

const BookingDetailsModal = ({
  booking,
  onClose,
  userType,
  showEditForm,
  setShowEditForm,
  showReviewModal,
  setShowReviewModal,
}) => {
  const darkMode = useThemeStore((state) => state.darkMode);
  const { updateBookingStatus } = useBookingStore();
  const getReviewById = useReviewStore((state) => state.getReviewById);

  // Estado para controlar el estado de carga de los botones
  const [isLoading, setIsLoading] = useState(false);

  const [canEditBooking, setCanEditBooking] = useState(false);
  const [timeLeft, setTimeLeft] = useState("");

  // تحديد نوع المستخدم
  const isClient = userType === "client";
  const isCraftsman = userType === "craftsman";

  // طباعة معلومات التصحيح
  console.log("معلومات الطلب:", {
    bookingId: booking.id,
    craftsmanName: booking.craftsmanName,
    clientName: booking.clientName,
    client: booking.client,
    craftsman: booking.craftsman,
    userType: userType,
  });

  const otherPartyName = isClient ? booking.craftsmanName : booking.clientName;

  // استخراج رقم الهاتف من كائن العميل أو الحرفي
  const craftsmanPhone =
    booking.craftsmanPhone ||
    (booking.craftsman && booking.craftsman.phone) ||
    (booking.craftsman &&
      booking.craftsman.user &&
      booking.craftsman.user.phone) ||
    "";

  const clientPhone =
    booking.clientPhone || (booking.client && booking.client.phone) || "";

  // مرجع للشاشة المنبثقة
  const modalRef = useRef(null);

  // مرجع لتتبع ما إذا كانت النافذة قد تم فتحها من قبل
  const modalOpenedBefore = useRef(false);

  // لن نضيف مستمع للنقرات خارج الشاشة لأننا نريد أن تبقى النافذة مفتوحة حتى ينقر المستخدم على زر الإغلاق

  // Obtener la valoración si existe
  const review = booking.reviewId ? getReviewById(booking.reviewId) : null;

  // التحقق من إمكانية تعديل التقييم (خلال 20 دقيقة من إنشائه)
  const canEditReview = useReviewStore((state) => state.canEditReview);
  const [canEditReviewState, setCanEditReviewState] = useState(false);

  // التحقق من إمكانية تعديل التقييم
  useEffect(() => {
    if (review && isClient) {
      const canEditThisReview = canEditReview(review.id);
      setCanEditReviewState(canEditThisReview);
    }
  }, [review, isClient, canEditReview]);

  // تم إزالة وظيفة التحقق من الطلبات المنتهية وإلغائها تلقائيًا

  // Verificar si el booking puede ser editado (dentro de los primeros 10 minutos)
  useEffect(() => {
    const checkEditability = () => {
      // تم إزالة التحقق من انتهاء صلاحية الطلب

      // إذا كان الطلب ليس في حالة "قيد الانتظار"، فلا يمكن تعديله
      if (booking.status !== "pending") {
        setCanEditBooking(false);
        setTimeLeft("0:00");
        return;
      }

      // حساب الوقت المنقضي منذ إنشاء الطلب
      const createdAt = new Date(booking.createdAt);
      const now = new Date();
      const diffInMinutes = (now - createdAt) / (1000 * 60);

      // حساب الوقت المتبقي بالثواني
      const timeLeftInSeconds = Math.max(0, 10 * 60 - diffInMinutes * 60);
      const minutes = Math.floor(timeLeftInSeconds / 60);
      const seconds = Math.floor(timeLeftInSeconds % 60);

      // تنسيق الوقت المتبقي
      setTimeLeft(`${minutes}:${seconds < 10 ? "0" : ""}${seconds}`);

      // التحقق مما إذا كان الوقت المسموح به للتعديل قد انتهى
      if (diffInMinutes > 10 || timeLeftInSeconds <= 0) {
        // إذا انتهى الوقت، لا يمكن تعديل الطلب
        setCanEditBooking(false);
      } else {
        // إذا لم ينته الوقت، يمكن تعديل الطلب
        setCanEditBooking(true);
      }

      // طباعة معلومات التصحيح
      console.log("معلومات التحقق من إمكانية تعديل الطلب:", {
        bookingId: booking.id,
        createdAt: createdAt.toLocaleString(),
        now: now.toLocaleString(),
        diffInMinutes: diffInMinutes,
        timeLeftInSeconds: timeLeftInSeconds,
        canEdit: diffInMinutes <= 10 && timeLeftInSeconds > 0
      });
    };

    checkEditability();
    const timer = setInterval(checkEditability, 1000);

    return () => clearInterval(timer);
  }, [booking, updateBookingStatus, onClose]);

  const handleStatusChange = async (status) => {
    try {
      // Activar el estado de carga
      setIsLoading(true);
      console.log(`Cambiando estado a: ${status}`);

      // Esperar a que se complete la actualización
      await updateBookingStatus(booking.id, status);

      // Actualizar la interfaz de usuario localmente
      console.log(`Estado cambiado exitosamente a: ${status}`);

      // Cerrar el modal después de un breve retraso para permitir que se complete la actualización
      setTimeout(() => {
        onClose();
        // Recargar la página para mostrar los cambios
        window.location.reload();
      }, 2000);
    } catch (error) {
      console.error("Error al cambiar el estado:", error);
      // Manejar el error aquí (mostrar un mensaje, etc.)

      // Desactivar el estado de carga en caso de error
      setIsLoading(false);

      // Aún así, cerrar el modal y recargar para mostrar los cambios locales
      setTimeout(() => {
        onClose();
        window.location.reload();
      }, 2000);
    }
  };

  // تم نقل دالة handleReviewSuccess إلى المكون BookingDetailsModalWithEdit

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("ar-SY");
  };

  const formatTime = (timeString) => {
    // Convertir el formato de 24 horas a formato de 12 horas con indicador AM/PM en árabe
    const [hours, minutes] = timeString.split(":");
    const hour = parseInt(hours, 10);
    const ampm = hour >= 12 ? "م" : "ص"; // م para PM, ص para AM
    const hour12 = hour % 12 || 12; // Convertir 0 a 12
    return `${hour12}:${minutes} ${ampm}`;
  };

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center z-[100] p-4 backdrop-blur-sm"
      onClick={(e) => e.stopPropagation()} // منع انتشار النقرات إلى العناصر الأساسية
    >
      <motion.div
        ref={modalRef}
        className={`${
          darkMode
            ? "bg-gradient-to-br from-gray-800 to-gray-900 text-gray-200"
            : "bg-gradient-to-br from-white to-indigo-50"
        } rounded-lg shadow-xl w-full max-w-2xl transition-colors duration-300 border ${
          darkMode ? "border-indigo-800/30" : "border-indigo-200"
        }`}
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        onClick={(e) => e.stopPropagation()} // منع انتشار النقرات إلى العناصر الأساسية
      >
        <div className="p-6">
          {/* Header with close button */}
          <div className="flex justify-between items-center mb-6">
            <h2
              className={`text-xl font-bold ${
                darkMode ? "text-indigo-300" : "text-indigo-800"
              } relative inline-block transition-colors duration-300`}
            >
              <span className="relative z-10">تفاصيل الطلب</span>
              <span
                className={`absolute bottom-0 left-0 right-0 h-2 ${
                  darkMode ? "bg-indigo-500" : "bg-indigo-300"
                } opacity-40 transform -rotate-1 z-0`}
              ></span>
            </h2>
            <button
              onClick={onClose}
              className={`${
                darkMode
                  ? "text-gray-400 hover:text-gray-300"
                  : "text-gray-500 hover:text-gray-700"
              } transition-colors duration-300 p-1 rounded-full hover:bg-gray-100/10`}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>

          {/* Booking details */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            <div
              className={`px-3 py-2 rounded-md shadow-sm ${
                darkMode
                  ? "bg-gray-700/80 border border-gray-600"
                  : "bg-white/80 border border-indigo-100"
              } transition-colors duration-300`}
            >
              <div className="flex items-center">
                <Calendar
                  size={20}
                  className={`${
                    darkMode ? "text-indigo-400" : "text-indigo-500"
                  } ml-2`}
                />
                <div>
                  <p
                    className={`text-sm ${
                      darkMode ? "text-indigo-300" : "text-indigo-500"
                    } transition-colors duration-300`}
                  >
                    تاريخ تقديم الطلب
                  </p>
                  <p
                    className={`font-medium ${
                      darkMode ? "text-indigo-200" : "text-indigo-700"
                    } transition-colors duration-300`}
                  >
                    {booking.endDate
                      ? `${formatDate(booking.date)} - ${formatDate(
                          booking.endDate
                        )}`
                      : formatDate(booking.date)}
                  </p>
                </div>
              </div>
            </div>

            <div
              className={`px-3 py-2 rounded-md shadow-sm ${
                darkMode
                  ? "bg-gray-700/80 border border-gray-600"
                  : "bg-white/80 border border-indigo-100"
              } transition-colors duration-300`}
            >
              <div className="flex items-center">
                <Clock
                  size={20}
                  className={`${
                    darkMode ? "text-indigo-400" : "text-indigo-500"
                  } ml-2`}
                />
                <div>
                  <p
                    className={`text-sm ${
                      darkMode ? "text-indigo-300" : "text-indigo-500"
                    } transition-colors duration-300`}
                  >
                    وقت تقديم الطلب
                  </p>
                  <p
                    className={`font-medium ${
                      darkMode ? "text-indigo-200" : "text-indigo-700"
                    } transition-colors duration-300`}
                  >
                    {booking.endTime
                      ? `${formatTime(booking.time)} - ${formatTime(
                          booking.endTime
                        )}`
                      : formatTime(booking.time)}
                  </p>
                </div>
              </div>
            </div>

            <div
              className={`px-3 py-2 rounded-md shadow-sm ${
                darkMode
                  ? "bg-gray-700/80 border border-gray-600"
                  : "bg-white/80 border border-indigo-100"
              } transition-colors duration-300`}
            >
              <div className="flex items-center">
                <User
                  size={20}
                  className={`${
                    darkMode ? "text-indigo-400" : "text-indigo-500"
                  } ml-2`}
                />
                <div>
                  <p
                    className={`text-sm ${
                      darkMode ? "text-indigo-300" : "text-indigo-500"
                    } transition-colors duration-300`}
                  >
                    {isClient ? "اسم الحرفي" : "اسم العميل"}
                  </p>
                  <p
                    className={`font-medium ${
                      darkMode ? "text-indigo-200" : "text-indigo-700"
                    } transition-colors duration-300`}
                  >
                    {otherPartyName ||
                      (isClient
                        ? booking.craftsmanName ||
                          (booking.craftsman &&
                            booking.craftsman.user &&
                            booking.craftsman.user.name) ||
                          ""
                        : booking.clientName ||
                          (booking.client && booking.client.name) ||
                          "")}
                  </p>
                </div>
              </div>
            </div>

            <div
              className={`px-3 py-2 rounded-md shadow-sm ${
                darkMode
                  ? "bg-gray-700/80 border border-gray-600"
                  : "bg-white/80 border border-indigo-100"
              } transition-colors duration-300`}
            >
              <div className="flex items-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className={`h-5 w-5 ${
                    darkMode ? "text-indigo-400" : "text-indigo-500"
                  } ml-2`}
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
                  />
                </svg>
                <div>
                  <p
                    className={`text-sm ${
                      darkMode ? "text-indigo-300" : "text-indigo-500"
                    } transition-colors duration-300`}
                  >
                    {isClient ? "رقم هاتف الحرفي" : "رقم هاتف العميل"}
                  </p>
                  <p
                    className={`font-medium ${
                      darkMode ? "text-indigo-200" : "text-indigo-700"
                    } transition-colors duration-300`}
                  >
                    {isClient
                      ? craftsmanPhone
                        ? craftsmanPhone
                        : "لم يتم توفير رقم الهاتف"
                      : clientPhone
                      ? clientPhone
                      : "لم يتم توفير رقم الهاتف"}
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Description */}
          <div className="mb-6">
            <h3
              className={`font-bold mb-2 ${
                darkMode ? "text-indigo-300" : "text-indigo-700"
              } transition-colors duration-300 flex items-center`}
            >
              <FileText size={18} className="ml-1" />
              وصف المشكلة
            </h3>
            <div
              className={`p-3 rounded-md shadow-sm ${
                darkMode
                  ? "bg-gray-700/80 border border-gray-600 text-gray-300"
                  : "bg-white/80 border border-indigo-100 text-gray-700"
              } transition-colors duration-300 max-h-40 overflow-y-auto`}
            >
              <p className="leading-relaxed break-words">
                {booking.description}
              </p>
            </div>
          </div>

          {/* Action buttons for pending bookings */}
          {booking.status === "pending" && isCraftsman && (
            <div className="flex flex-wrap gap-3 justify-center mt-6">
              <Button
                variant="primary"
                onClick={() => {
                  setIsLoading(true);
                  handleStatusChange("accepted");
                }}
                disabled={isLoading}
                className={`text-white flex items-center justify-center gap-2 shadow-md hover:shadow-lg transition-all duration-200 relative overflow-hidden group py-2 px-6 ${
                  darkMode
                    ? "bg-gradient-to-r from-green-600 to-emerald-700 hover:from-green-700 hover:to-emerald-800"
                    : "bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700"
                } ${isLoading ? 'opacity-70 cursor-not-allowed' : ''}`}
              >
                <span className="relative z-10 flex items-center">
                  {isLoading ? (
                    <>
                      <Loader size={18} className="ml-1 animate-spin" />
                      جاري المعالجة...
                    </>
                  ) : (
                    <>
                      <CheckCircle size={18} className="ml-1" />
                      الموافقة على الطلب
                    </>
                  )}
                </span>
                <span className="absolute inset-0 bg-white opacity-20 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700"></span>
              </Button>

              <Button
                variant="outline"
                onClick={() => {
                  setIsLoading(true);
                  handleStatusChange("rejected");
                }}
                disabled={isLoading}
                className={`flex items-center justify-center gap-2 shadow-md hover:shadow-lg transition-all duration-200 relative overflow-hidden group py-2 px-6 ${
                  darkMode
                    ? "bg-gradient-to-r from-red-600 to-rose-700 hover:from-red-700 hover:to-rose-800 text-white"
                    : "bg-gradient-to-r from-red-500 to-rose-600 hover:from-red-600 hover:to-rose-700 text-white"
                } ${isLoading ? 'opacity-70 cursor-not-allowed' : ''}`}
              >
                <span className="relative z-10 flex items-center">
                  {isLoading ? (
                    <>
                      <Loader size={18} className="ml-1 animate-spin" />
                      جاري المعالجة...
                    </>
                  ) : (
                    <>
                      <XCircle size={18} className="ml-1" />
                      رفض الطلب
                    </>
                  )}
                </span>
                <span className="absolute inset-0 bg-white opacity-20 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700"></span>
              </Button>
            </div>
          )}

          {/* Action buttons for accepted bookings */}
          {booking.status === "accepted" && isCraftsman && (
            <div className="flex flex-wrap gap-3 justify-center mt-6">
              <Button
                variant="primary"
                onClick={() => {
                  setIsLoading(true);
                  handleStatusChange("completed");
                }}
                disabled={isLoading}
                className={`text-white flex items-center justify-center gap-2 shadow-md hover:shadow-lg transition-all duration-200 relative overflow-hidden group py-2 px-6 ${
                  darkMode
                    ? "bg-gradient-to-r from-green-600 to-emerald-700 hover:from-green-700 hover:to-emerald-800"
                    : "bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700"
                } ${isLoading ? 'opacity-70 cursor-not-allowed' : ''}`}
              >
                <span className="relative z-10 flex items-center">
                  {isLoading ? (
                    <>
                      <Loader size={18} className="ml-1 animate-spin" />
                      جاري المعالجة...
                    </>
                  ) : (
                    <>
                      <CheckCircle size={18} className="ml-1" />
                      إكمال الخدمة
                    </>
                  )}
                </span>
                <span className="absolute inset-0 bg-white opacity-20 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700"></span>
              </Button>
            </div>
          )}

          {/* Cancel button for client (for pending or accepted bookings) */}
          {(booking.status === "pending" || booking.status === "accepted") && isClient && (
            <div className="flex flex-wrap gap-3 justify-center mt-6">
              <Button
                variant="outline"
                onClick={() => {
                  setIsLoading(true);
                  handleStatusChange("cancelled");
                }}
                disabled={isLoading}
                className={`flex items-center justify-center gap-2 shadow-md hover:shadow-lg transition-all duration-200 relative overflow-hidden group py-2 px-6 ${
                  darkMode
                    ? "bg-gradient-to-r from-red-600 to-rose-700 hover:from-red-700 hover:to-rose-800 text-white"
                    : "bg-gradient-to-r from-red-500 to-rose-600 hover:from-red-600 hover:to-rose-700 text-white"
                } ${isLoading ? 'opacity-70 cursor-not-allowed' : ''}`}
              >
                <span className="relative z-10 flex items-center">
                  {isLoading ? (
                    <>
                      <Loader size={18} className="ml-1 animate-spin" />
                      جاري المعالجة...
                    </>
                  ) : (
                    <>
                      <XCircle size={18} className="ml-1" />
                      إلغاء الطلب
                    </>
                  )}
                </span>
                <span className="absolute inset-0 bg-white opacity-20 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700"></span>
              </Button>
            </div>
          )}
        </div>
      </motion.div>
    </div>
  );
};

// Renderizar el formulario de edición y el formulario de evaluación fuera del modal principal
const BookingDetailsModalWithEdit = (props) => {
  const [showEditForm, setShowEditForm] = useState(false);
  const [showReviewModal, setShowReviewModal] = useState(false);

  // الحصول على التقييم الحالي إذا كان موجودًا
  const getReviewById = useReviewStore((state) => state.getReviewById);
  const existingReview = props.booking.reviewId
    ? getReviewById(props.booking.reviewId)
    : null;

  const handleReviewSuccess = (reviewData) => {
    console.log("تم إضافة/تعديل التقييم بنجاح:", reviewData);
    setShowReviewModal(false);
    // تحديث الحجز بمعرف التقييم الجديد
    props.booking.reviewId = reviewData.id;
    props.onClose();
  };

  return (
    <>
      <BookingDetailsModal
        {...props}
        showEditForm={showEditForm}
        setShowEditForm={setShowEditForm}
        showReviewModal={showReviewModal}
        setShowReviewModal={setShowReviewModal}
      />

      {showEditForm && (
        <BookingEditForm
          booking={props.booking}
          onClose={() => setShowEditForm(false)}
          onSave={() => {
            setShowEditForm(false);
            props.onClose();
          }}
        />
      )}

      {/* نافذة التقييم المنبثقة */}
      {showReviewModal && (
        <ReviewModal
          booking={props.booking}
          onClose={() => setShowReviewModal(false)}
          onSuccess={handleReviewSuccess}
          existingReview={existingReview}
          isEditing={existingReview !== null}
        />
      )}
    </>
  );
};

export default BookingDetailsModalWithEdit;
