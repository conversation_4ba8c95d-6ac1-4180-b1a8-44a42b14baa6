import React, { useState, useEffect, useRef } from "react";
import { motion } from "framer-motion";
import {
  Calendar,
  Clock,
  User,
  FileText,
  CheckCircle,
  XCircle,
  Star,
  Edit,
  ThumbsUp,
  Loader,
  Send,
  AlertTriangle,
} from "lucide-react";
import Button from "../common/Button";
import LazyImage from "../common/LazyImage";
import useThemeStore from "../../store/themeStore";
import useBookingStore from "../../store/bookingStore";
import useReviewStore from "../../store/reviewStore";
import BookingEditForm from "./BookingEditForm";
import ReviewModal from "../reviews/ReviewModal";
import CountdownTimer from "../reviews/CountdownTimer";
import SimpleReviewsList from "../reviews/SimpleReviewsList";
import toastUtils from "../../utils/toastUtils";
import { BOOKING_SETTINGS } from "../../services/config";
import bookingService from "../../services/bookingService";
import { reviewService } from "../../services/api";

const BookingDetailsModal = ({
  booking,
  onClose,
  userType,
  showEditForm,
  setShowEditForm,
  showReviewModal,
  setShowReviewModal,
}) => {
  const darkMode = useThemeStore((state) => state.darkMode);
  const { updateBookingStatus } = useBookingStore();
  const getReviewById = useReviewStore((state) => state.getReviewById);

  // Estado para controlar qué botón está cargando
  const [loadingButton, setLoadingButton] = useState(null);

  const [canEditBooking, setCanEditBooking] = useState(false);
  const [timeLeft, setTimeLeft] = useState("");

  // تحديد نوع المستخدم
  const isClient = userType === "client";
  const isCraftsman = userType === "craftsman";

  // التأكد من وجود خاصية visibleToCraftsman في الطلب
  if (booking.visibleToCraftsman === undefined) {
    booking.visibleToCraftsman = false;
  }


  const otherPartyName = isClient ? booking.craftsmanName : booking.clientName;

  // استخراج رقم الهاتف من كائن العميل أو الحرفي
  const craftsmanPhone =
    booking.craftsmanPhone ||
    (booking.craftsman && booking.craftsman.phone) ||
    (booking.craftsman &&
      booking.craftsman.user &&
      booking.craftsman.user.phone) ||
    "";

  const clientPhone =
    booking.clientPhone || (booking.client && booking.client.phone) || "";

  // مرجع للشاشة المنبثقة
  const modalRef = useRef(null);

  // مرجع لتتبع ما إذا كانت النافذة قد تم فتحها من قبل
  const modalOpenedBefore = useRef(false);

  // لن نضيف مستمع للنقرات خارج الشاشة لأننا نريد أن تبقى النافذة مفتوحة حتى ينقر المستخدم على زر الإغلاق

  // Obtener la valoración si existe
  const [review, setReview] = useState(null);

  // تحميل التقييم عند فتح النافذة
  useEffect(() => {
    const loadReview = async () => {
      if (booking.reviewId) {

        // استخراج المعرف الفعلي إذا كان كائنًا
        let actualReviewId = booking.reviewId;

        // إذا كان reviewId كائنًا، نحاول استخراج المعرف منه
        if (typeof booking.reviewId === "object" && booking.reviewId !== null) {
          actualReviewId = booking.reviewId.id || booking.reviewId._id;
        }

        // إذا لم نتمكن من استخراج معرف صالح، نعود
        if (!actualReviewId) {
          console.warn(
            "لم يتم العثور على معرف صالح في الكائن:",
            booking.reviewId
          );
          return;
        }

        // تحويل معرف التقييم إلى نص للمقارنة
        const reviewIdStr = String(actualReviewId);

        try {
          // أولاً، محاولة الحصول على التقييم من الذاكرة المحلية
          const localReview = await getReviewById(reviewIdStr);

          if (localReview) {
           
            setReview(localReview);
          } else {
            // إذا لم يتم العثور على التقييم محليًا، تحميله من الخادم
            try {
              const serverReview = await reviewService.getReviewById(
                reviewIdStr
              );

              if (serverReview) {
              
                setReview(serverReview);

                // إضافة التقييم إلى متجر التقييمات
                const { reviews } = useReviewStore.getState();
                const reviewExists = reviews.some(
                  (r) =>
                    (r.id &&
                      String(r.id) ===
                        String(serverReview.id || serverReview._id)) ||
                    (r._id &&
                      String(r._id) ===
                        String(serverReview.id || serverReview._id))
                );

                if (!reviewExists) {
                  useReviewStore.setState({
                    reviews: [...reviews, serverReview],
                  });
                }
              } else {
                // لا نقوم بتعيين أي تقييم افتراضي
                setReview(null);
              }
            } catch (error) {
              console.error("خطأ في تحميل التقييم من الخادم:", error);
              // لا نقوم بتعيين أي تقييم افتراضي
              setReview(null);
            }
          }
        } catch (error) {
          console.error("خطأ في تحميل التقييم:", error);
          setReview(null);
        }
      }
    };

    loadReview();
  }, [booking.reviewId, getReviewById]);

  // التحقق من إمكانية تعديل التقييم (خلال 20 دقيقة من إنشائه)
  const canEditReview = useReviewStore((state) => state.canEditReview);
  const [canEditReviewState, setCanEditReviewState] = useState(false);

  // التحقق من إمكانية تعديل التقييم
  useEffect(() => {
    if (review && isClient) {
      const canEditThisReview = canEditReview(review.id);
      setCanEditReviewState(canEditThisReview);
    }
  }, [review, isClient, canEditReview]);

  // تم إزالة وظيفة التحقق من الطلبات المنتهية وإلغائها تلقائيًا

  // التحقق مما إذا كان يمكن تعديل الحجز (خلال الـ 10 دقائق الأولى)
  useEffect(() => {
    const checkEditability = () => {
      // تم إزالة التحقق من انتهاء صلاحية الطلب

      // إذا كان الطلب ليس في حالة "قيد الانتظار"، فلا يمكن تعديله
      if (booking.status !== "pending") {
        setCanEditBooking(false);
        setTimeLeft("0:00");
        return;
      }

      // حساب الوقت المنقضي منذ إنشاء الطلب
      const createdAt = new Date(booking.createdAt);
      const now = new Date();
      const diffInMinutes = (now - createdAt) / (1000 * 60);

      // استخدام ثابت مدة التعديل من ملف الإعدادات
      const {
        EDIT_WINDOW_MINUTES,
        TIME_WARNING_THRESHOLD,
        TIME_DANGER_THRESHOLD,
      } = BOOKING_SETTINGS;

      // حساب الوقت المتبقي بالثواني
      const timeLeftInSeconds = Math.max(
        0,
        EDIT_WINDOW_MINUTES * 60 - diffInMinutes * 60
      );
      const minutes = Math.floor(timeLeftInSeconds / 60);
      const seconds = Math.floor(timeLeftInSeconds % 60);

      // تنسيق الوقت المتبقي
      setTimeLeft(`${minutes}:${seconds < 10 ? "0" : ""}${seconds}`);

      // التحقق مما إذا كان الوقت المسموح به للتعديل قد انتهى
      if (diffInMinutes > EDIT_WINDOW_MINUTES || timeLeftInSeconds <= 0) {
        // إذا انتهى الوقت، لا يمكن تعديل الطلب
        setCanEditBooking(false);

        // إذا كان هذا أول فتح للنافذة وكان المستخدم هو طالب الخدمة، أظهر إشعارًا
        if (!modalOpenedBefore.current && isClient) {
          toastUtils.showToast(
            "لم يعد بإمكانك تعديل الطلب أو إلغاؤه، انتهى الوقت المسموح به",
            "warning"
          );
        }
      } else {
        // إذا لم ينته الوقت، يمكن تعديل الطلب
        setCanEditBooking(true);
      }

      // تعيين علامة أن النافذة قد تم فتحها
      modalOpenedBefore.current = true;

      
    };

    checkEditability();
    const timer = setInterval(checkEditability, 1000);

    return () => clearInterval(timer);
  }, [booking, updateBookingStatus, onClose]);

  const handleStatusChange = async (status) => {
    try {
      // Activar el estado de carga solo para el botón específico
      setLoadingButton(status);

      // Esperar a que se complete la actualización
      await updateBookingStatus(booking.id, status);

      // Actualizar la interfaz de usuario localmente

      // Cerrar el modal después de un breve retraso para permitir que se complete la actualización
      setTimeout(() => {
        onClose();
        // Recargar la página para mostrar los cambios
        window.location.reload();
      }, 2000);
    } catch (error) {
      console.error("Error al cambiar el estado:", error);
      // Manejar el error aquí (mostrar un mensaje, etc.)

      // Desactivar el estado de carga en caso de error
      setLoadingButton(null);

      // Aún así, cerrar el modal y recargar para mostrar los cambios locales
      setTimeout(() => {
        onClose();
        window.location.reload();
      }, 2000);
    }
  };

  // تم نقل دالة handleReviewSuccess إلى المكون BookingDetailsModalWithEdit

  const formatDate = (dateString) => {
    if (!dateString) return "غير محدد";

    try {
      const date = new Date(dateString);

      // التحقق من صحة التاريخ
      if (isNaN(date.getTime())) {
        console.error("تاريخ غير صالح:", dateString);
        return "تاريخ غير صالح";
      }

      // إذا كان التاريخ هو تاريخ التحديث، نعرض التاريخ والوقت
      if (dateString === booking.updatedAt) {
        return `${date.toLocaleDateString("ar-SY")} ${date.getHours()}:${String(
          date.getMinutes()
        ).padStart(2, "0")}`;
      }

      // إذا كان التاريخ هو تاريخ الإنشاء أو تاريخ بداية الطلب، نعرض التاريخ بتنسيق مختلف (مثل الصورة)
      if (dateString === booking.createdAt || dateString === booking.date) {
        // تنسيق مثل: ٢٠٢٣/٠٥/٠٩ (تم التحديث: ٢٠٢٣/٠٥/٢٨)
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, "0");
        const day = String(date.getDate()).padStart(2, "0");
        return `${year}/${month}/${day}`;
      }

      // وإلا نعرض التاريخ فقط
      return date.toLocaleDateString("ar-SY");
    } catch (error) {
      console.error("خطأ في تنسيق التاريخ:", error);
      return "خطأ في التاريخ";
    }
  };

  const formatTime = (timeString) => {
    // Convertir el formato de 24 horas a formato de 12 horas con indicador AM/PM en árabe
    const [hours, minutes] = timeString.split(":");
    const hour = parseInt(hours, 10);
    const ampm = hour >= 12 ? "م" : "ص"; // م para PM, ص para AM
    const hour12 = hour % 12 || 12; // Convertir 0 a 12
    return `${hour12}:${minutes} ${ampm}`;
  };

  // دالة لإلغاء الطلب
  const handleCancelBooking = async () => {
    try {
      setLoadingButton("cancelled");

      // إلغاء الطلب
      await updateBookingStatus(booking.id, "cancelled");

      // إظهار إشعار نجاح
      toastUtils.showToast("تم إلغاء الطلب بنجاح", "success");

      // إغلاق النافذة وتحديث الصفحة
      setTimeout(() => {
        onClose();
        window.location.reload();
      }, 2000);
    } catch (error) {
      console.error("خطأ في إلغاء الطلب:", error);
      toastUtils.showToast("حدث خطأ أثناء إلغاء الطلب", "error");
      setLoadingButton(null);
    }
  };

  // دالة لإرسال الطلب فورًا وتجاوز فترة الانتظار
  const handleSendImmediately = async () => {
    try {
      setLoadingButton("send-immediately");

      // تأكيد الطلب وإرساله للحرفي
      const result = await bookingService.confirmBooking(booking.id);

      // تحديث حالة الطلب محلياً في الواجهة
      booking.canEdit = false;
      booking.visibleToCraftsman = true; // تحديث حالة الرؤية للحرفي

      // إظهار إشعار نجاح
      toastUtils.showToast("تم إرسال الطلب للحرفي بنجاح", "success");

      // تعطيل إمكانية التعديل
      setCanEditBooking(false);

      // تحديث حالة التحميل بعد الانتهاء
      setLoadingButton(null);

      // إغلاق النافذة وتحديث الصفحة بعد فترة قصيرة
      setTimeout(() => {
        onClose();
        window.location.reload();
      }, 2000);
    } catch (error) {
      console.error("خطأ في إرسال الطلب فورًا:", error);
      toastUtils.showToast("حدث خطأ أثناء إرسال الطلب", "error");
      setLoadingButton(null);
    }
  };

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center z-[100] p-4 backdrop-blur-sm"
      onClick={(e) => e.stopPropagation()} // منع انتشار النقرات إلى العناصر الأساسية
    >
      <motion.div
        ref={modalRef}
        className={`${
          darkMode
            ? "bg-gradient-to-br from-gray-800 to-gray-900 text-gray-200"
            : "bg-gradient-to-br from-white to-indigo-50"
        } rounded-lg shadow-xl w-full max-w-2xl transition-colors duration-300 border ${
          darkMode ? "border-indigo-800/30" : "border-indigo-200"
        }`}
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        onClick={(e) => e.stopPropagation()} // منع انتشار النقرات إلى العناصر الأساسية
      >
        <div className="p-6">
          {/* Header with close button */}
          <div className="flex justify-between items-center mb-6">
            <h2
              className={`text-xl font-bold ${
                darkMode ? "text-indigo-300" : "text-indigo-800"
              } relative inline-block transition-colors duration-300`}
            >
              <span className="relative z-10">تفاصيل الطلب</span>
              <span
                className={`absolute bottom-0 left-0 right-0 h-2 ${
                  darkMode ? "bg-indigo-500" : "bg-indigo-300"
                } opacity-40 transform -rotate-1 z-0`}
              ></span>
            </h2>
            <button
              onClick={onClose}
              className={`${
                darkMode
                  ? "text-gray-400 hover:text-gray-300"
                  : "text-gray-500 hover:text-gray-700"
              } transition-colors duration-300 p-1 rounded-full hover:bg-gray-100/10`}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>

          {/* Booking details */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            <div
              className={`px-3 py-2 rounded-md shadow-sm ${
                darkMode
                  ? "bg-gray-700/80 border border-gray-600"
                  : "bg-white/80 border border-indigo-100"
              } transition-colors duration-300`}
            >
              <div className="flex items-center">
                <Calendar
                  size={20}
                  className={`${
                    darkMode ? "text-indigo-400" : "text-indigo-500"
                  } ml-2`}
                />
                <div>
                  <p
                    className={`text-sm ${
                      darkMode ? "text-indigo-300" : "text-indigo-500"
                    } transition-colors duration-300`}
                  >
                    تاريخ بداية الطلب
                  </p>
                  <p
                    className={`font-medium ${
                      darkMode ? "text-indigo-400" : "text-indigo-600"
                    } transition-colors duration-300`}
                  >
                    {formatDate(booking.date)}
                  </p>
                </div>
              </div>
            </div>

            <div
              className={`px-3 py-2 rounded-md shadow-sm ${
                darkMode
                  ? "bg-gray-700/80 border border-gray-600"
                  : "bg-white/80 border border-indigo-100"
              } transition-colors duration-300`}
            >
              <div className="flex items-center">
                <Clock
                  size={20}
                  className={`${
                    darkMode ? "text-indigo-400" : "text-indigo-500"
                  } ml-2`}
                />
                <div>
                  <p
                    className={`text-sm ${
                      darkMode ? "text-indigo-300" : "text-indigo-500"
                    } transition-colors duration-300`}
                  >
                    وقت بداية الطلب
                  </p>
                  <p
                    className={`font-medium ${
                      darkMode ? "text-indigo-400" : "text-indigo-600"
                    } transition-colors duration-300`}
                  >
                    {formatTime(booking.time)}
                  </p>
                </div>
              </div>
            </div>

            <div
              className={`px-3 py-2 rounded-md shadow-sm ${
                darkMode
                  ? "bg-gray-700/80 border border-gray-600"
                  : "bg-white/80 border border-indigo-100"
              } transition-colors duration-300`}
            >
              <div className="flex items-center">
                <Calendar
                  size={20}
                  className={`${
                    darkMode ? "text-indigo-400" : "text-indigo-500"
                  } ml-2`}
                />
                <div>
                  <p
                    className={`text-sm ${
                      darkMode ? "text-indigo-300" : "text-indigo-500"
                    } transition-colors duration-300`}
                  >
                    تاريخ انتهاء الطلب
                  </p>
                  <p
                    className={`font-medium ${
                      darkMode ? "text-indigo-400" : "text-indigo-600"
                    } transition-colors duration-300`}
                  >
                    {booking.endDate
                      ? formatDate(booking.endDate)
                      : booking.date
                      ? formatDate(booking.date)
                      : "غير محدد"}
                  </p>
                </div>
              </div>
            </div>

            <div
              className={`px-3 py-2 rounded-md shadow-sm ${
                darkMode
                  ? "bg-gray-700/80 border border-gray-600"
                  : "bg-white/80 border border-indigo-100"
              } transition-colors duration-300`}
            >
              <div className="flex items-center">
                <Clock
                  size={20}
                  className={`${
                    darkMode ? "text-indigo-400" : "text-indigo-500"
                  } ml-2`}
                />
                <div>
                  <p
                    className={`text-sm ${
                      darkMode ? "text-indigo-300" : "text-indigo-500"
                    } transition-colors duration-300`}
                  >
                    وقت انتهاء الطلب
                  </p>
                  <p
                    className={`font-medium ${
                      darkMode ? "text-indigo-400" : "text-indigo-600"
                    } transition-colors duration-300`}
                  >
                    {booking.endTime
                      ? formatTime(booking.endTime)
                      : booking.time
                      ? formatTime(booking.time)
                      : "غير محدد"}
                  </p>
                </div>
              </div>
            </div>

            <div
              className={`px-3 py-2 rounded-md shadow-sm ${
                darkMode
                  ? "bg-gray-700/80 border border-gray-600"
                  : "bg-white/80 border border-indigo-100"
              } transition-colors duration-300`}
            >
              <div className="flex items-center">
                <User
                  size={20}
                  className={`${
                    darkMode ? "text-indigo-400" : "text-indigo-500"
                  } ml-2`}
                />
                <div>
                  <p
                    className={`text-sm ${
                      darkMode ? "text-indigo-300" : "text-indigo-500"
                    } transition-colors duration-300`}
                  >
                    {isClient ? "اسم الحرفي" : "اسم العميل"}
                  </p>
                  <p
                    className={`font-medium ${
                      darkMode ? "text-indigo-400" : "text-indigo-600"
                    } transition-colors duration-300`}
                  >
                    {otherPartyName ||
                      (isClient
                        ? booking.craftsmanName ||
                          (booking.craftsman &&
                            booking.craftsman.user &&
                            booking.craftsman.user.name) ||
                          ""
                        : booking.clientName ||
                          (booking.client && booking.client.name) ||
                          "")}
                  </p>
                </div>
              </div>
            </div>

            <div
              className={`px-3 py-2 rounded-md shadow-sm ${
                darkMode
                  ? "bg-gray-700/80 border border-gray-600"
                  : "bg-white/80 border border-indigo-100"
              } transition-colors duration-300`}
            >
              <div className="flex items-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className={`h-5 w-5 ${
                    darkMode ? "text-indigo-400" : "text-indigo-500"
                  } ml-2`}
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
                  />
                </svg>
                <div>
                  <p
                    className={`text-sm ${
                      darkMode ? "text-indigo-300" : "text-indigo-500"
                    } transition-colors duration-300`}
                  >
                    {isClient ? "رقم هاتف الحرفي" : "رقم هاتف العميل"}
                  </p>
                  <p
                    className={`font-medium ${
                      darkMode ? "text-indigo-400" : "text-indigo-600"
                    } transition-colors duration-300`}
                  >
                    {isClient
                      ? craftsmanPhone
                        ? craftsmanPhone
                        : "لم يتم توفير رقم الهاتف"
                      : clientPhone
                      ? clientPhone
                      : "لم يتم توفير رقم الهاتف"}
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Description */}
          <div className="mb-6">
            <h3
              className={`font-bold mb-2 ${
                darkMode ? "text-indigo-300" : "text-indigo-700"
              } transition-colors duration-300 flex items-center`}
            >
              <FileText size={18} className="ml-1" />
              وصف المشكلة
            </h3>
            <div
              className={`p-3 rounded-md shadow-sm ${
                darkMode
                  ? "bg-gray-700/80 border border-gray-600 text-gray-300"
                  : "bg-white/80 border border-indigo-100 text-gray-700"
              } transition-colors duration-300 max-h-40 overflow-y-auto`}
            >
              <p className="leading-relaxed break-words">
                {booking.description}
              </p>
            </div>
          </div>

          {/* Action buttons for pending bookings */}
          {booking.status === "pending" && isCraftsman && (
            <div className="flex flex-wrap gap-3 justify-center mt-6">
              <Button
                variant="primary"
                onClick={() => handleStatusChange("accepted")}
                disabled={loadingButton !== null}
                className={`text-white flex items-center justify-center gap-2 shadow-md hover:shadow-lg transition-all duration-200 relative overflow-hidden group py-2 px-6 ${
                  darkMode
                    ? "bg-gradient-to-r from-green-600 to-emerald-700 hover:from-green-700 hover:to-emerald-800"
                    : "bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700"
                } ${
                  loadingButton === "accepted"
                    ? "opacity-70 cursor-not-allowed"
                    : ""
                }`}
              >
                <span className="relative z-10 flex items-center">
                  {loadingButton === "accepted" ? (
                    <>
                      <Loader size={18} className="ml-1 animate-spin" />
                      جاري المعالجة...
                    </>
                  ) : (
                    <>
                      <CheckCircle size={18} className="ml-1" />
                      الموافقة على الطلب
                    </>
                  )}
                </span>
                <span className="absolute inset-0 bg-white opacity-20 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700"></span>
              </Button>

              <Button
                variant="outline"
                onClick={() => handleStatusChange("rejected")}
                disabled={loadingButton !== null}
                className={`flex items-center justify-center gap-2 shadow-md hover:shadow-lg transition-all duration-200 relative overflow-hidden group py-2 px-6 ${
                  darkMode
                    ? "bg-gradient-to-r from-red-600 to-rose-700 hover:from-red-700 hover:to-rose-800 text-white"
                    : "bg-gradient-to-r from-red-500 to-rose-600 hover:from-red-600 hover:to-rose-700 text-white"
                } ${
                  loadingButton === "rejected"
                    ? "opacity-70 cursor-not-allowed"
                    : ""
                }`}
              >
                <span className="relative z-10 flex items-center">
                  {loadingButton === "rejected" ? (
                    <>
                      <Loader size={18} className="ml-1 animate-spin" />
                      جاري المعالجة...
                    </>
                  ) : (
                    <>
                      <XCircle size={18} className="ml-1" />
                      رفض الطلب
                    </>
                  )}
                </span>
                <span className="absolute inset-0 bg-white opacity-20 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700"></span>
              </Button>
            </div>
          )}

          {/* Action buttons for accepted bookings */}
          {booking.status === "accepted" && isCraftsman && (
            <div className="flex flex-wrap gap-3 justify-center mt-6">
              <Button
                variant="primary"
                onClick={() => handleStatusChange("completed")}
                disabled={loadingButton !== null}
                className={`text-white flex items-center justify-center gap-2 shadow-md hover:shadow-lg transition-all duration-200 relative overflow-hidden group py-2 px-6 ${
                  darkMode
                    ? "bg-gradient-to-r from-green-600 to-emerald-700 hover:from-green-700 hover:to-emerald-800"
                    : "bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700"
                } ${
                  loadingButton === "completed"
                    ? "opacity-70 cursor-not-allowed"
                    : ""
                }`}
              >
                <span className="relative z-10 flex items-center">
                  {loadingButton === "completed" ? (
                    <>
                      <Loader size={18} className="ml-1 animate-spin" />
                      جاري المعالجة...
                    </>
                  ) : (
                    <>
                      <CheckCircle size={18} className="ml-1" />
                      إكمال الخدمة
                    </>
                  )}
                </span>
                <span className="absolute inset-0 bg-white opacity-20 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700"></span>
              </Button>
            </div>
          )}

          {/* زر التقييم للعميل عندما يكون الطلب مكتمل أو تمت الموافقة عليه أو مرفوض أو ملغي ولا يوجد تقييم سابق */}
          {(booking.status === "completed" ||
            booking.status === "accepted" ||
            booking.status === "rejected" ||
            booking.status === "cancelled") &&
            isClient &&
            (!review || (typeof review !== "object") || review.overallRating === undefined) &&
            setShowReviewModal && (
              <div className="mt-6">
                <Button
                  variant="primary"
                  onClick={() => setShowReviewModal(true)}
                  className={`text-white flex items-center justify-center gap-2 shadow-md hover:shadow-lg transition-all duration-200 relative overflow-hidden group py-2 px-6 w-full ${
                    darkMode
                      ? "bg-gradient-to-r from-indigo-600 to-indigo-700 hover:from-indigo-700 hover:to-indigo-800"
                      : "bg-gradient-to-r from-indigo-500 to-indigo-600 hover:from-indigo-600 hover:to-indigo-700"
                  }`}
                >
                  <span className="relative z-10 flex items-center">
                    <ThumbsUp size={18} className="ml-1" />
                    إضافة تقييم للخدمة
                  </span>
                  <span className="absolute inset-0 bg-white opacity-20 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700"></span>
                </Button>
              </div>
            )}

          {/* Review section for completed, accepted, rejected or cancelled bookings */}
          {console.log("حالة الطلب:", booking.status, "التقييم:", review)}
          {(booking.status === "completed" ||
            booking.status === "accepted" ||
            booking.status === "rejected" ||
            booking.status === "cancelled") &&
            review && typeof review === "object" && review.overallRating !== undefined && (
              <div className="mt-6">
                <div className="flex justify-between items-center mb-3">
                  <h3
                    className={`font-bold ${
                      darkMode ? "text-indigo-300" : "text-indigo-700"
                    } transition-colors duration-300 flex items-center`}
                  >
                    <Star
                      size={18}
                      className={`ml-1 ${
                        darkMode ? "text-yellow-400" : "text-yellow-500"
                      }`}
                    />
                    التقييم
                  </h3>

                  {/* زر تعديل التقييم - يظهر فقط إذا كان التقييم قابل للتعديل (خلال 10 دقائق من إنشائه) */}
                  {isClient && canEditReviewState && (
                    <Button
                      variant="secondary"
                      onClick={() => setShowReviewModal(true)}
                      className={`text-xs py-1 px-3 flex items-center ${
                        darkMode
                          ? "bg-indigo-700 hover:bg-indigo-600 text-white"
                          : "bg-indigo-100 hover:bg-indigo-200 text-indigo-700"
                      } transition-colors duration-300`}
                    >
                      <Edit size={14} className="ml-1" />
                      تعديل التقييم
                    </Button>
                  )}
                </div>

                <div
                  className={`p-4 rounded-md shadow-sm ${
                    darkMode
                      ? "bg-gray-700/80 border border-gray-600"
                      : "bg-white/80 border border-indigo-100"
                  } transition-colors duration-300`}
                >
                  {/* استخدام مكون SimpleReviewsList لعرض التقييم */}
                  <SimpleReviewsList reviews={[review]} darkMode={darkMode} />

                  {/* عرض وقت إنشاء التقييم والوقت المتبقي للتعديل إذا كان قابلاً للتعديل */}
                  {isClient && review && (
                    <div
                      className={`mt-4 text-xs ${
                        darkMode ? "text-gray-400" : "text-gray-500"
                      }`}
                    >
                      <div className="flex items-center justify-between">
                        <span>
                          يمكنك تعديل هذا التقييم خلال 10 دقائق فقط من إنشائه.
                        </span>
                        {canEditReviewState && (
                          <CountdownTimer
                            endTime={
                              new Date(
                                new Date(review.createdAt).getTime() +
                                  10 * 60 * 1000
                              )
                            }
                            darkMode={darkMode}
                            className="mr-2"
                          />
                        )}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}

          {/* قسم التعديل والإلغاء للطلبات قيد الانتظار */}
          {booking.status === "pending" &&
            userType === "client" &&
            canEditBooking &&
            !booking.visibleToCraftsman && (
              <div className="mb-6">
                {/* ملاحظة حول فترة التعديل */}
                <div
                  className={`p-4 rounded-md mb-4 ${
                    darkMode
                      ? "bg-indigo-900/30 border border-indigo-800"
                      : "bg-indigo-50 border border-indigo-100"
                  }`}
                >
                  <div className="flex items-center gap-2 mb-2">
                    <AlertTriangle
                      size={20}
                      className={`${
                        !canEditBooking
                          ? "text-red-500"
                          : parseInt(timeLeft) <=
                            BOOKING_SETTINGS.TIME_DANGER_THRESHOLD
                          ? "text-red-500"
                          : parseInt(timeLeft) <=
                            BOOKING_SETTINGS.TIME_WARNING_THRESHOLD
                          ? "text-yellow-500"
                          : darkMode
                          ? "text-indigo-400"
                          : "text-indigo-600"
                      }`}
                    />
                    <span
                      className={`font-bold ${
                        darkMode ? "text-indigo-300" : "text-indigo-700"
                      }`}
                    >
                      ملاحظة:
                    </span>
                  </div>

                  <p
                    className={`text-sm ${
                      darkMode ? "text-indigo-300" : "text-indigo-700"
                    }`}
                  >
                    يمكنك تعديل طلبك أو إلغاؤه خلال{" "}
                    {BOOKING_SETTINGS.EDIT_WINDOW_MINUTES} دقائق فقط من وقت
                    تقديمه.
                  </p>

                  {/* عرض الوقت المتبقي بلون مختلف حسب الوقت */}
                  <div className="mt-2 flex items-center">
                    <span
                      className={`text-sm font-medium ${
                        !canEditBooking
                          ? "text-red-500"
                          : parseInt(timeLeft) <=
                            BOOKING_SETTINGS.TIME_DANGER_THRESHOLD
                          ? "text-red-500"
                          : parseInt(timeLeft) <=
                            BOOKING_SETTINGS.TIME_WARNING_THRESHOLD
                          ? "text-yellow-500"
                          : darkMode
                          ? "text-indigo-400"
                          : "text-indigo-600"
                      }`}
                    >
                      الوقت المتبقي: {timeLeft}
                    </span>
                  </div>
                </div>

                {/* أزرار التعديل والإلغاء وإرسال الطلب فورًا */}
                <div className="space-y-3">
                  <div className="flex flex-wrap gap-2">
                    <Button
                      variant="primary"
                      onClick={() => {
                        setShowEditForm(true);
                      }}
                      className={`flex items-center justify-center gap-2 flex-1 ${
                        darkMode
                          ? "bg-gradient-to-r from-amber-600 to-amber-700 hover:from-amber-700 hover:to-amber-800 text-white"
                          : "bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700 text-white"
                      }`}
                    >
                      <Edit size={16} />
                      <span>تعديل الطلب</span>
                    </Button>
                    <Button
                      variant="danger"
                      onClick={handleCancelBooking}
                      className={`flex items-center justify-center gap-2 flex-1 ${
                        darkMode
                          ? "bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white"
                          : "bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white"
                      }`}
                      disabled={loadingButton === "cancelled"}
                    >
                      {loadingButton === "cancelled" ? (
                        <Loader size={16} className="animate-spin" />
                      ) : (
                        <XCircle size={16} />
                      )}
                      <span>إلغاء الطلب</span>
                    </Button>
                  </div>

                  {/* زر إرسال الطلب فورًا */}
                  <Button
                    variant="success"
                    onClick={handleSendImmediately}
                    className={`w-full flex items-center justify-center gap-2 py-3 ${
                      darkMode
                        ? "bg-gradient-to-r from-green-600 to-emerald-700 hover:from-green-700 hover:to-emerald-800"
                        : "bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700"
                    } text-white shadow-md hover:shadow-lg transition-all duration-200 relative overflow-hidden group`}
                    disabled={loadingButton === "send-immediately"}
                  >
                    {loadingButton === "send-immediately" ? (
                      <Loader size={16} className="animate-spin" />
                    ) : (
                      <Send size={16} />
                    )}
                    <span className="relative z-10">إرسال الطلب فورًا</span>
                    <span className="absolute inset-0 bg-white opacity-20 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700"></span>
                  </Button>
                  <p
                    className={`text-xs ${
                      darkMode ? "text-gray-400" : "text-gray-600"
                    }`}
                  >
                    سيتم إرسال الطلب للحرفي فورًا وتجاوز فترة الانتظار، ولن
                    تتمكن من تعديله أو إلغائه بعد ذلك.
                  </p>
                </div>
              </div>
            )}

          {/* رسالة تأكيد إرسال الطلب للحرفي */}
          {booking.status === "pending" &&
            userType === "client" &&
            booking.visibleToCraftsman && (
              <div className="mb-6">
                <div
                  className={`p-4 rounded-md mb-4 ${
                    darkMode
                      ? "bg-green-900/30 border border-green-800"
                      : "bg-green-50 border border-green-100"
                  }`}
                >
                  <div className="flex items-center gap-2 mb-2">
                    <CheckCircle
                      size={20}
                      className={`${
                        darkMode ? "text-green-400" : "text-green-600"
                      }`}
                    />
                    <span
                      className={`font-bold ${
                        darkMode ? "text-green-300" : "text-green-700"
                      }`}
                    >
                      تم إرسال الطلب:
                    </span>
                  </div>

                  <p
                    className={`text-sm ${
                      darkMode ? "text-green-300" : "text-green-700"
                    }`}
                  >
                    تم إرسال طلبك للحرفي بنجاح. سيقوم الحرفي بمراجعة طلبك والرد
                    عليه قريباً.
                  </p>
                </div>
              </div>
            )}
        </div>
      </motion.div>
    </div>
  );
};

// Renderizar el formulario de edición y el formulario de evaluación fuera del modal principal
const BookingDetailsModalWithEdit = (props) => {
  const [showEditForm, setShowEditForm] = useState(false);
  const [showReviewModal, setShowReviewModal] = useState(false);

  // الحصول على التقييم الحالي إذا كان موجودًا
  const getReviewById = useReviewStore((state) => state.getReviewById);
  const fetchReviewById = useReviewStore((state) => state.fetchReviewById);
  const [existingReview, setExistingReview] = useState(null);

  // تحميل التقييم عند فتح النافذة
  useEffect(() => {
    const loadReview = async () => {
      if (props.booking.reviewId) {

        // استخراج المعرف الفعلي إذا كان كائنًا
        let actualReviewId = props.booking.reviewId;

        // إذا كان reviewId كائنًا، نحاول استخراج المعرف منه
        if (
          typeof props.booking.reviewId === "object" &&
          props.booking.reviewId !== null
        ) {
          actualReviewId =
            props.booking.reviewId.id || props.booking.reviewId._id;
        }

        // إذا لم نتمكن من استخراج معرف صالح، نعود
        if (!actualReviewId) {
          console.warn(
            "لم يتم العثور على معرف صالح في الكائن:",
            props.booking.reviewId
          );
          return;
        }

        // تحويل معرف التقييم إلى نص للمقارنة
        const reviewIdStr = String(actualReviewId);

        try {
          // أولاً، محاولة الحصول على التقييم من الذاكرة المحلية
          const localReview = await getReviewById(reviewIdStr);

          if (localReview) {
           
           
            setExistingReview(localReview);
          } else {
            // إذا لم يتم العثور على التقييم محليًا، تحميله من الخادم
            try {
              const serverReview = await fetchReviewById(reviewIdStr);

              if (serverReview) {
               
                setExistingReview(serverReview);
              } else {
              }
            } catch (error) {
              console.error("خطأ في تحميل التقييم من الخادم:", error);
            }
          }
        } catch (error) {
          console.error("خطأ في تحميل التقييم:", error);
        }
      }
    };

    loadReview();
  }, [props.booking.reviewId, getReviewById, fetchReviewById]);

  const handleReviewSuccess = (reviewData) => {
    setShowReviewModal(false);

    // تحديث الحجز بمعرف التقييم الجديد إذا كان التقييم موجودًا
    if (reviewData && (reviewData.id || reviewData._id)) {
      props.booking.reviewId = reviewData.id || reviewData._id;
    } else {
     
    }

    props.onClose();
  };

  return (
    <>
      <BookingDetailsModal
        {...props}
        showEditForm={showEditForm}
        setShowEditForm={setShowEditForm}
        showReviewModal={showReviewModal}
        setShowReviewModal={setShowReviewModal}
      />

      {showEditForm && (
        <BookingEditForm
          booking={props.booking}
          onClose={() => setShowEditForm(false)}
          onSave={(updatedBooking) => {
            // تحديث بيانات الطلب الحالي بالبيانات الجديدة
            if (updatedBooking) {
              Object.assign(props.booking, updatedBooking);
            }
            setShowEditForm(false);
            props.onClose();
          }}
        />
      )}
      {/* Mensaje de depuración para verificar si se está mostrando el formulario de edición */}
      {showEditForm && (
        <div className="fixed bottom-4 right-4 bg-red-500 text-white p-2 rounded-md z-[200]">
          تم تنشيط نموذج التعديل
        </div>
      )}

      {/* نافذة التقييم المنبثقة */}
      {showReviewModal && (
        <ReviewModal
          booking={props.booking}
          onClose={() => setShowReviewModal(false)}
          onSuccess={handleReviewSuccess}
          existingReview={existingReview}
          isEditing={existingReview !== null}
        />
      )}
    </>
  );
};

export default BookingDetailsModalWithEdit;
