import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { FiUpload, FiImage, FiSettings } from 'react-icons/fi';
import { useThemeStore } from '../../../store/themeStore';
import { WorkGalleryManager } from '../../../components/WorkGallery';
import Card from '../../../components/common/Card';

const ProfileGalleryNew = ({ 
  isEditing = false, 
  user = null,
  onGalleryUpdate = null 
}) => {
  const darkMode = useThemeStore((state) => state.darkMode);
  const [useNewSystem, setUseNewSystem] = useState(false);
  const [showUpgradePrompt, setShowUpgradePrompt] = useState(false);

  // التحقق من وجود صور في النظام القديم
  const hasOldGallery = user?.gallery && user.gallery.length > 0;
  const hasNewGallery = user?.workGallery && user.workGallery.length > 0;

  // إذا كان المستخدم يستخدم النظام الجديد بالفعل
  if (hasNewGallery || useNewSystem) {
    return (
      <Card className={`p-6 mb-6 rounded-xl shadow-lg ${
        darkMode
          ? "bg-gray-800 text-gray-200 border border-gray-700"
          : "bg-gradient-to-br from-blue-50/80 to-indigo-50 border border-indigo-100"
      } transition-colors duration-300 hover:shadow-xl`}>
        
        {/* رأس القسم */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center">
            <div className={`p-2 rounded-full mr-3 ${
              darkMode ? "bg-indigo-900/50" : "bg-indigo-100"
            }`}>
              <FiImage size={24} className={`${
                darkMode ? "text-indigo-400" : "text-indigo-500"
              } transition-colors duration-300`} />
            </div>
            <h2 className={`text-xl font-bold ${
              darkMode ? "text-indigo-300" : "text-indigo-800"
            } relative inline-block transition-colors duration-300`}>
              <span className="relative z-10">معرض الأعمال المطور</span>
              <span className={`absolute bottom-0 left-0 right-0 h-2 ${
                darkMode ? "bg-indigo-600" : "bg-indigo-400"
              } opacity-40 transform -rotate-1 z-0 rounded-full`}></span>
            </h2>
          </div>

          {/* مؤشر النظام الجديد */}
          <div className={`px-3 py-1 rounded-full text-xs font-medium ${
            darkMode 
              ? "bg-green-900/50 text-green-300 border border-green-800"
              : "bg-green-100 text-green-700 border border-green-200"
          }`}>
            <span className="flex items-center">
              <div className="w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse"></div>
              النظام المطور
            </span>
          </div>
        </div>

        {/* النظام الجديد */}
        <WorkGalleryManager
          isEditable={isEditing}
          maxImages={20}
          title=""
        />

        {/* معلومات إضافية */}
        <div className={`mt-4 p-3 rounded-lg ${
          darkMode
            ? "bg-blue-900/30 border border-blue-800/50"
            : "bg-blue-50/80 border border-blue-200"
        }`}>
          <div className="flex items-start">
            <FiUpload size={16} className={`mt-1 mr-2 ${
              darkMode ? "text-blue-400" : "text-blue-500"
            }`} />
            <div>
              <p className={`text-sm font-medium ${
                darkMode ? "text-blue-300" : "text-blue-700"
              }`}>
                مزايا النظام المطور:
              </p>
              <ul className={`text-xs mt-1 space-y-1 ${
                darkMode ? "text-gray-400" : "text-gray-600"
              }`}>
                <li>• رفع أسرع وأكثر استقرار</li>
                <li>• ضغط تلقائي للصور</li>
                <li>• معاينة فورية ومتقدمة</li>
                <li>• إدارة أفضل للصور</li>
              </ul>
            </div>
          </div>
        </div>
      </Card>
    );
  }

  // إذا كان لديه صور في النظام القديم
  if (hasOldGallery) {
    return (
      <Card className={`p-6 mb-6 rounded-xl shadow-lg ${
        darkMode
          ? "bg-gray-800 text-gray-200 border border-gray-700"
          : "bg-gradient-to-br from-blue-50/80 to-indigo-50 border border-indigo-100"
      } transition-colors duration-300 hover:shadow-xl`}>
        
        {/* رأس القسم */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center">
            <div className={`p-2 rounded-full mr-3 ${
              darkMode ? "bg-indigo-900/50" : "bg-indigo-100"
            }`}>
              <FiImage size={24} className={`${
                darkMode ? "text-indigo-400" : "text-indigo-500"
              } transition-colors duration-300`} />
            </div>
            <h2 className={`text-xl font-bold ${
              darkMode ? "text-indigo-300" : "text-indigo-800"
            } relative inline-block transition-colors duration-300`}>
              <span className="relative z-10">معرض الأعمال</span>
            </h2>
          </div>

          {/* زر الترقية */}
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => setShowUpgradePrompt(true)}
            className={`flex items-center px-4 py-2 rounded-lg font-medium transition-all duration-200 ${
              darkMode
                ? "bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 text-white"
                : "bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600 text-white"
            } shadow-lg hover:shadow-xl`}
          >
            <FiSettings size={16} className="mr-2" />
            ترقية للنظام المطور
          </motion.button>
        </div>

        {/* عرض الصور القديمة */}
        <div className="grid grid-cols-2 md:grid-cols-3 gap-4 mb-4">
          {user.gallery.map((image, index) => (
            <motion.div
              key={index}
              whileHover={{ scale: 1.03 }}
              className="relative group"
            >
              <div className={`h-48 rounded-lg overflow-hidden shadow-md border ${
                darkMode ? "border-gray-700" : "border-indigo-200"
              } transition-all duration-300 group-hover:shadow-xl`}>
                <img
                  src={image}
                  alt={`عمل ${index + 1}`}
                  className="w-full h-full object-cover"
                />
                <div className="absolute inset-0 bg-indigo-500/0 group-hover:bg-indigo-500/10 transition-colors duration-300"></div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* رسالة الترقية */}
        <div className={`p-4 rounded-lg ${
          darkMode
            ? "bg-yellow-900/30 border border-yellow-800/50"
            : "bg-yellow-50/80 border border-yellow-200"
        }`}>
          <div className="flex items-start">
            <FiUpload size={20} className={`mt-1 mr-3 ${
              darkMode ? "text-yellow-400" : "text-yellow-600"
            }`} />
            <div>
              <p className={`font-medium ${
                darkMode ? "text-yellow-300" : "text-yellow-700"
              }`}>
                قم بالترقية للنظام المطور
              </p>
              <p className={`text-sm mt-1 ${
                darkMode ? "text-gray-400" : "text-gray-600"
              }`}>
                احصل على تجربة أفضل مع رفع أسرع وميزات متقدمة لإدارة معرض أعمالك
              </p>
            </div>
          </div>
        </div>

        {/* مودال تأكيد الترقية */}
        {showUpgradePrompt && (
          <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              className={`max-w-md w-full mx-4 p-6 rounded-xl shadow-2xl ${
                darkMode ? "bg-gray-800 text-white" : "bg-white text-gray-900"
              }`}
            >
              <h3 className="text-xl font-bold mb-4">ترقية معرض الأعمال</h3>
              <p className={`mb-6 ${darkMode ? "text-gray-300" : "text-gray-600"}`}>
                سيتم نقل صورك الحالية إلى النظام المطور مع الاحتفاظ بجميع الصور. 
                ستحصل على ميزات جديدة مثل الرفع الأسرع والضغط التلقائي.
              </p>
              
              <div className="flex space-x-3 space-x-reverse">
                <button
                  onClick={() => {
                    setUseNewSystem(true);
                    setShowUpgradePrompt(false);
                  }}
                  className="flex-1 bg-blue-500 text-white py-2 px-4 rounded-lg hover:bg-blue-600 transition-colors"
                >
                  ترقية الآن
                </button>
                <button
                  onClick={() => setShowUpgradePrompt(false)}
                  className={`flex-1 py-2 px-4 rounded-lg transition-colors ${
                    darkMode
                      ? "bg-gray-700 text-gray-300 hover:bg-gray-600"
                      : "bg-gray-200 text-gray-700 hover:bg-gray-300"
                  }`}
                >
                  لاحقاً
                </button>
              </div>
            </motion.div>
          </div>
        )}
      </Card>
    );
  }

  // إذا لم يكن لديه صور (مستخدم جديد)
  return (
    <WorkGalleryManager
      isEditable={isEditing}
      maxImages={20}
      title="معرض الأعمال"
    />
  );
};

export default ProfileGalleryNew;
