/*
 * خدمة المصادقة باستخدام Supabase
 * بديل Firebase للمصادقة في سوريا
 */

import { supabase } from "../config/supabase";

// رسائل الخطأ باللغة العربية
const getErrorMessage = (errorCode) => {
  // تحويل رسالة الخطأ إلى أحرف صغيرة للمقارنة
  const lowerErrorCode = (errorCode || "").toLowerCase();

  // البحث عن كلمات مفتاحية في رسالة الخطأ
  if (
    lowerErrorCode.includes("invalid login credentials") ||
    lowerErrorCode.includes("invalid_credentials") ||
    lowerErrorCode.includes("invalid email or password")
  ) {
    return "كلمة المرور غير صحيحة";
  }

  if (
    lowerErrorCode.includes("email not confirmed") ||
    lowerErrorCode.includes("email_not_confirmed")
  ) {
    return "يرجى تأكيد بريدك الإلكتروني أولاً";
  }

  if (
    lowerErrorCode.includes("too many requests") ||
    lowerErrorCode.includes("rate_limit")
  ) {
    return "محاولات كثيرة، انتظر قليلاً";
  }

  if (
    lowerErrorCode.includes("user not found") ||
    lowerErrorCode.includes("user_not_found")
  ) {
    return "البريد الإلكتروني غير مسجل";
  }

  if (
    lowerErrorCode.includes("weak password") ||
    lowerErrorCode.includes("password should be at least")
  ) {
    return "كلمة المرور ضعيفة جداً. يجب أن تكون 6 أحرف على الأقل.";
  }

  if (
    lowerErrorCode.includes("invalid email") ||
    lowerErrorCode.includes("email_address_invalid")
  ) {
    return "عنوان البريد الإلكتروني غير صالح. يرجى التحقق من صيغة البريد الإلكتروني.";
  }

  if (
    lowerErrorCode.includes("signup disabled") ||
    lowerErrorCode.includes("signup_disabled")
  ) {
    return "التسجيل معطل حالياً. يرجى المحاولة لاحقاً.";
  }

  if (
    lowerErrorCode.includes("user already registered") ||
    lowerErrorCode.includes("email_address_not_authorized")
  ) {
    return "البريد الإلكتروني مسجل بالفعل. يرجى تسجيل الدخول أو استخدام بريد إلكتروني آخر.";
  }

  if (
    lowerErrorCode.includes("network") ||
    lowerErrorCode.includes("connection") ||
    lowerErrorCode.includes("timeout")
  ) {
    return "مشكلة في الاتصال بالإنترنت. يرجى التحقق من اتصالك والمحاولة مرة أخرى.";
  }

  if (
    lowerErrorCode.includes("server") ||
    lowerErrorCode.includes("internal")
  ) {
    return "مشكلة في الخادم. يرجى المحاولة لاحقاً.";
  }

  // رسائل افتراضية للأخطاء الشائعة
  const errorMessages = {
    invalid_credentials: "البريد الإلكتروني أو كلمة المرور غير صحيحة",
    email_not_confirmed: "يرجى تأكيد البريد الإلكتروني أولاً",
    too_many_requests: "تم إرسال طلبات كثيرة، يرجى المحاولة لاحقاً",
    weak_password: "كلمة المرور ضعيفة، يجب أن تكون 6 أحرف على الأقل",
    email_address_invalid: "عنوان البريد الإلكتروني غير صالح",
    signup_disabled: "التسجيل معطل حالياً",
    email_address_not_authorized: "البريد الإلكتروني غير مصرح له",
    user_already_registered: "المستخدم مسجل بالفعل",
    unknown: "حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.",
  };

  return errorMessages[errorCode] || errorMessages["unknown"];
};

const supabaseAuthService = {
  // إنشاء حساب جديد باستخدام البريد الإلكتروني وكلمة المرور
  registerWithEmailAndPassword: async (email, password, name) => {
    try {
      console.log("Supabase registration attempt:", { email, name });

      const { data, error } = await supabase.auth.signUp({
        email: email,
        password: password,
        options: {
          data: {
            full_name: name,
            display_name: name,
          },
          // تحديد URL للتوجيه بعد تأكيد البريد الإلكتروني - نفس الصفحة الحالية
          emailRedirectTo: window.location.href,
        },
      });

      if (error) {
        console.error("Supabase registration error:", error);
        return {
          success: false,
          error: {
            code: error.message,
            message: getErrorMessage(error.message) || error.message,
          },
        };
      }

      console.log("Supabase registration successful:", data);

      return {
        success: true,
        user: {
          uid: data.user?.id,
          email: data.user?.email,
          name: name,
          emailVerified: data.user?.email_confirmed_at ? true : false,
        },
        needsEmailVerification: !data.user?.email_confirmed_at,
      };
    } catch (error) {
      console.error("Error registering with Supabase:", error);
      return {
        success: false,
        error: {
          code: error.code || "unknown",
          message: getErrorMessage(error.code) || error.message,
        },
      };
    }
  },

  // إنشاء حساب مؤقت للتحقق من البريد الإلكتروني (خاص بصفحة تسجيل الحرفيين)
  registerForEmailVerification: async (email, password, name, currentUrl) => {
    try {
      console.log("Creating temporary account for email verification:", {
        email,
        name,
      });

      const { data, error } = await supabase.auth.signUp({
        email: email,
        password: password,
        options: {
          data: {
            full_name: name,
            display_name: name,
            temp_account: true, // تمييز الحساب كمؤقت
          },
          // توجيه المستخدم لنفس الصفحة الحالية
          emailRedirectTo: currentUrl || window.location.href,
        },
      });

      if (error) {
        console.error("Supabase temp registration error:", error);
        return {
          success: false,
          error: {
            code: error.message,
            message: getErrorMessage(error.message) || error.message,
          },
        };
      }

      console.log("Supabase temp registration successful:", data);

      return {
        success: true,
        user: {
          uid: data.user?.id,
          email: data.user?.email,
          name: name,
          emailVerified: data.user?.email_confirmed_at ? true : false,
        },
        needsEmailVerification: !data.user?.email_confirmed_at,
      };
    } catch (error) {
      console.error("Error creating temp account:", error);
      return {
        success: false,
        error: {
          code: error.code || "unknown",
          message: getErrorMessage(error.code) || error.message,
        },
      };
    }
  },

  // تسجيل الدخول باستخدام البريد الإلكتروني وكلمة المرور
  loginWithEmailAndPassword: async (email, password) => {
    try {
      console.log("Supabase login attempt:", { email });

      const { data, error } = await supabase.auth.signInWithPassword({
        email: email,
        password: password,
      });

      if (error) {
        console.error("Supabase login error:", error);
        return {
          success: false,
          error: {
            code: error.message,
            message: getErrorMessage(error.message) || error.message,
          },
        };
      }

      console.log("Supabase login successful:", data);

      return {
        success: true,
        user: {
          uid: data.user?.id,
          email: data.user?.email,
          name:
            data.user?.user_metadata?.full_name ||
            data.user?.user_metadata?.display_name,
          emailVerified: data.user?.email_confirmed_at ? true : false,
        },
      };
    } catch (error) {
      console.error("Error logging in with Supabase:", error);
      return {
        success: false,
        error: {
          code: error.code || "unknown",
          message: getErrorMessage(error.code) || error.message,
        },
      };
    }
  },

  // تسجيل الدخول باستخدام Google
  loginWithGoogle: async () => {
    try {
      console.log("Supabase Google login attempt");

      const { data, error } = await supabase.auth.signInWithOAuth({
        provider: "google",
        options: {
          redirectTo: window.location.origin + "/login",
        },
      });

      if (error) {
        console.error("Supabase Google login error:", error);
        return {
          success: false,
          error: {
            code: error.message,
            message: getErrorMessage(error.message) || error.message,
          },
        };
      }

      // في حالة OAuth، البيانات ستأتي من callback
      return {
        success: true,
        redirected: true,
      };
    } catch (error) {
      console.error("Error with Google login:", error);
      return {
        success: false,
        error: {
          code: error.code || "unknown",
          message: getErrorMessage(error.code) || error.message,
        },
      };
    }
  },

  // إرسال بريد إعادة تعيين كلمة المرور
  sendPasswordResetEmail: async (email) => {
    try {
      console.log("Sending password reset email:", email);

      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: window.location.origin + "/reset-password",
      });

      if (error) {
        console.error("Password reset error:", error);
        return {
          success: false,
          error: {
            code: error.message,
            message: getErrorMessage(error.message) || error.message,
          },
        };
      }

      return {
        success: true,
        message: "تم إرسال بريد إعادة تعيين كلمة المرور بنجاح",
      };
    } catch (error) {
      console.error("Error sending password reset email:", error);
      return {
        success: false,
        error: {
          code: error.code || "unknown",
          message: getErrorMessage(error.code) || error.message,
        },
      };
    }
  },

  // إعادة إرسال رابط التحقق من البريد الإلكتروني
  resendEmailVerification: async (email) => {
    try {
      console.log("Resending email verification for:", email);

      const { error } = await supabase.auth.resend({
        type: "signup",
        email: email,
        options: {
          emailRedirectTo: window.location.origin + "/register/client",
        },
      });

      if (error) {
        console.error("Resend verification error:", error);
        return {
          success: false,
          error: {
            code: error.message,
            message: getErrorMessage(error.message) || error.message,
          },
        };
      }

      return {
        success: true,
        message: "تم إعادة إرسال رابط التحقق بنجاح",
      };
    } catch (error) {
      console.error("Error resending email verification:", error);
      return {
        success: false,
        error: {
          code: error.code || "unknown",
          message: getErrorMessage(error.code) || error.message,
        },
      };
    }
  },

  // التحقق من صحة رمز إعادة تعيين كلمة المرور
  verifyPasswordResetToken: async (accessToken, refreshToken) => {
    try {
      console.log("Verifying password reset token");

      if (!accessToken) {
        return {
          success: false,
          error: {
            code: "missing_token",
            message: "رمز المصادقة مفقود",
          },
        };
      }

      // تعيين الجلسة باستخدام الرموز المميزة
      const { data, error } = await supabase.auth.setSession({
        access_token: accessToken,
        refresh_token: refreshToken || null,
      });

      if (error) {
        console.error("Token verification error:", error);
        return {
          success: false,
          error: {
            code: error.message,
            message: getErrorMessage(error.message) || error.message,
          },
        };
      }

      if (data.user) {
        console.log("Token verification successful, user:", data.user.email);
        return {
          success: true,
          user: data.user,
        };
      }

      return {
        success: false,
        error: {
          code: "invalid_token",
          message: "رمز إعادة تعيين كلمة المرور غير صالح",
        },
      };
    } catch (error) {
      console.error("Error verifying reset token:", error);
      return {
        success: false,
        error: {
          code: error.code || "unknown",
          message: getErrorMessage(error.code) || error.message,
        },
      };
    }
  },

  // تحديث كلمة المرور
  updatePassword: async (newPassword, accessToken, refreshToken) => {
    try {
      console.log("Updating password");

      if (!accessToken) {
        return {
          success: false,
          error: {
            code: "missing_token",
            message: "رمز المصادقة مفقود",
          },
        };
      }

      if (!newPassword || newPassword.length < 6) {
        return {
          success: false,
          error: {
            code: "invalid_password",
            message: "كلمة المرور يجب أن تكون 6 أحرف على الأقل",
          },
        };
      }

      // تعيين الجلسة أولاً
      const { error: sessionError } = await supabase.auth.setSession({
        access_token: accessToken,
        refresh_token: refreshToken || null,
      });

      if (sessionError) {
        console.error("Session error:", sessionError);
        return {
          success: false,
          error: {
            code: sessionError.message,
            message:
              getErrorMessage(sessionError.message) || sessionError.message,
          },
        };
      }

      console.log("Session set successfully, updating password...");

      // تحديث كلمة المرور
      const { data, error } = await supabase.auth.updateUser({
        password: newPassword,
      });

      if (error) {
        console.error("Password update error:", error);
        return {
          success: false,
          error: {
            code: error.message,
            message: getErrorMessage(error.message) || error.message,
          },
        };
      }

      console.log("Password updated successfully");

      // تسجيل الخروج بعد تحديث كلمة المرور
      await supabase.auth.signOut();

      return {
        success: true,
        user: data.user,
        message: "تم تحديث كلمة المرور بنجاح",
      };
    } catch (error) {
      console.error("Error updating password:", error);
      return {
        success: false,
        error: {
          code: error.code || "unknown",
          message: getErrorMessage(error.code) || error.message,
        },
      };
    }
  },

  // الحصول على المستخدم الحالي
  getCurrentUser: () => {
    try {
      const {
        data: { user },
      } = supabase.auth.getUser();
      return user;
    } catch (error) {
      console.error("Error getting current user:", error);
      return null;
    }
  },

  // تسجيل الخروج
  logout: async () => {
    try {
      const { error } = await supabase.auth.signOut();

      if (error) {
        console.error("Logout error:", error);
        return {
          success: false,
          error: {
            code: error.message,
            message: error.message,
          },
        };
      }

      return {
        success: true,
        message: "تم تسجيل الخروج بنجاح",
      };
    } catch (error) {
      console.error("Error during logout:", error);
      return {
        success: false,
        error: {
          code: error.code || "unknown",
          message: error.message,
        },
      };
    }
  },

  // تحديث الملف الشخصي للمستخدم
  updateUserProfile: async (profileData) => {
    try {
      const { data, error } = await supabase.auth.updateUser({
        data: profileData,
      });

      if (error) {
        console.error("Profile update error:", error);
        return {
          success: false,
          error: {
            code: error.message,
            message: error.message,
          },
        };
      }

      return {
        success: true,
        message: "تم تحديث الملف الشخصي بنجاح",
        user: {
          uid: data.user?.id,
          email: data.user?.email,
          name:
            data.user?.user_metadata?.full_name ||
            data.user?.user_metadata?.display_name,
        },
      };
    } catch (error) {
      console.error("خطأ في تحديث الملف الشخصي:", error);
      return {
        success: false,
        error: {
          code: error.code || "update-profile-error",
          message: error.message || "حدث خطأ أثناء تحديث الملف الشخصي",
        },
      };
    }
  },

  // الحصول على معلومات المستخدم
  getUserInfo: async (uid) => {
    try {
      const {
        data: { user },
      } = await supabase.auth.getUser();

      if (user) {
        return {
          uid: user.id,
          displayName:
            user.user_metadata?.full_name || user.user_metadata?.display_name,
          email: user.email,
          phoneNumber: user.phone,
          photoURL: user.user_metadata?.avatar_url,
          emailVerified: user.email_confirmed_at ? true : false,
        };
      }

      return null;
    } catch (error) {
      console.error("Error getting user info:", error);
      return null;
    }
  },

  // التحقق من حالة تأكيد البريد الإلكتروني
  checkEmailVerification: async () => {
    try {
      const {
        data: { user },
      } = await supabase.auth.getUser();

      if (user) {
        return {
          success: true,
          emailVerified: user.email_confirmed_at ? true : false,
          user: {
            uid: user.id,
            email: user.email,
            name:
              user.user_metadata?.full_name || user.user_metadata?.display_name,
            emailVerified: user.email_confirmed_at ? true : false,
          },
        };
      }

      return {
        success: false,
        emailVerified: false,
      };
    } catch (error) {
      console.error("Error checking email verification:", error);
      return {
        success: false,
        emailVerified: false,
        error: error.message,
      };
    }
  },

  // التحقق من URL parameters للتأكيد
  handleEmailConfirmation: async () => {
    try {
      // التحقق من وجود hash في URL (يحتوي على token التأكيد)
      const hashParams = new URLSearchParams(window.location.hash.substring(1));
      const accessToken = hashParams.get("access_token");
      const refreshToken = hashParams.get("refresh_token");
      const type = hashParams.get("type");

      if (type === "signup" && accessToken) {
        // تعيين الجلسة باستخدام tokens
        const { error } = await supabase.auth.setSession({
          access_token: accessToken,
          refresh_token: refreshToken,
        });

        if (error) {
          console.error("Error setting session:", error);
          return {
            success: false,
            error: error.message,
          };
        }

        // التحقق من المستخدم
        const {
          data: { user },
        } = await supabase.auth.getUser();

        if (user && user.email_confirmed_at) {
          return {
            success: true,
            confirmed: true,
            user: {
              uid: user.id,
              email: user.email,
              name:
                user.user_metadata?.full_name ||
                user.user_metadata?.display_name,
              emailVerified: true,
            },
          };
        }
      }

      return {
        success: false,
        confirmed: false,
      };
    } catch (error) {
      console.error("Error handling email confirmation:", error);
      return {
        success: false,
        confirmed: false,
        error: error.message,
      };
    }
  },

  // مراقبة تغييرات حالة المصادقة
  onAuthStateChange: (callback) => {
    return supabase.auth.onAuthStateChange((event, session) => {
      console.log("Auth state changed:", event, session);

      if (event === "SIGNED_IN" && session?.user) {
        callback({
          event,
          user: {
            uid: session.user.id,
            email: session.user.email,
            name:
              session.user.user_metadata?.full_name ||
              session.user.user_metadata?.display_name,
            emailVerified: session.user.email_confirmed_at ? true : false,
          },
        });
      } else {
        callback({ event, user: null });
      }
    });
  },
};

export default supabaseAuthService;
