import React from "react";
import { motion } from "framer-motion";
import useThemeStore from "../../store/themeStore";

const LoadingScreen = () => {
  const darkMode = useThemeStore((state) => state.darkMode);

  return (
    <div
      className={`fixed inset-0 flex flex-col items-center justify-center z-50 ${
        darkMode ? "bg-gray-900" : "bg-gradient-to-br from-blue-50 to-indigo-100"
      } transition-colors duration-300`}
    >
      <div className="text-center">
        <motion.div
          className="flex justify-center mb-6"
          initial={{ scale: 0.8, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ duration: 0.5 }}
        >
          <div className="relative">
            <div className="w-24 h-24 border-t-4 border-b-4 border-indigo-600 rounded-full animate-spin"></div>
            <div className="absolute inset-0 flex items-center justify-center">
              <span className={`text-2xl font-bold ${darkMode ? "text-indigo-400" : "text-indigo-700"}`}>JS</span>
            </div>
          </div>
        </motion.div>
        
        <motion.h2
          className={`text-xl font-bold mb-2 ${darkMode ? "text-white" : "text-indigo-800"}`}
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          جاري التحميل...
        </motion.h2>
        
        <motion.p
          className={`text-sm ${darkMode ? "text-gray-400" : "text-gray-600"}`}
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.3 }}
        >
          يرجى الانتظار قليلاً
        </motion.p>
      </div>
    </div>
  );
};

export default LoadingScreen;
