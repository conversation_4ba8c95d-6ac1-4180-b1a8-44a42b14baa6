/*
 * صفحة البحث - مع تحديد الموقع الجغرافي التلقائي
 * يستخدم نظام هجين لتحديد الموقع: GPS -> IP-API -> GeoJS -> الموقع الافتراضي (دمشق)
 * يمكن للمستخدم تحديد موقع مختلف يدوياً من خلال النقر على الخريطة
 */

import React, { useEffect, useState, useRef } from "react";
import { Link, useNavigate } from "react-router-dom";
import { motion } from "framer-motion";
import {
  Search,
  Star,
  MapPin,
  Filter,
  List,
  Map as MapIcon,
  Briefcase,
  Lock,
} from "lucide-react";
import Layout from "../../components/layout/Layout";
import Card from "../../components/common/Card";
import Button from "../../components/common/Button";
import MapBox from "../../components/maps/MapBox";
import AdvancedMap from "../../components/map/AdvancedMap";
import MapFilter from "../../components/map/MapFilter";
import SimpleLazyImage from "../../components/common/SimpleLazyImage";

import useUserStore from "../../store/userStore";
import useCraftsmenStore from "../../store/craftsmenStore";
import useThemeStore from "../../store/themeStore";
import SearchSuggestions from "../../components/search/SearchSuggestions";
import SearchFeedback from "../../components/search/SearchFeedback";
import { analyzeQuery, suggestQueries } from "../../services/nlpService";
import professionCategories from "../../data/professionCategories";
import { isAvailableNow } from "../../utils/availabilityUtils";
import { fetchStreetsFromOverpass } from "../../services/mapService";
import {
  getCurrentLocation,
  getLocationWithFeedback,
} from "../../services/locationService";

// استخراج جميع المهن من تصنيفات المهن
const professions = professionCategories.reduce((allProfessions, category) => {
  return [...allProfessions, ...category.professions];
}, []);

const SearchPage = () => {
  const navigate = useNavigate();
  const user = useUserStore((state) => state.user);
  const logout = useUserStore((state) => state.logout);
  const darkMode = useThemeStore((state) => state.darkMode);

  // التحقق من حالة تسجيل الدخول باستخدام متجر الحالة
  const isTokenExist = localStorage.getItem("token") !== null;

  const {
    filteredCraftsmen,
    fetchCraftsmen,
    filterCraftsmen,
  } = useCraftsmenStore();

  const [viewMode, setViewMode] = useState("map"); // 'list' or 'map'

  // إعادة تهيئة الخريطة عند تغيير وضع العرض
  useEffect(() => {
    if (viewMode === "map") {
      // تأخير قصير للسماح للواجهة بالتحديث
      setTimeout(() => {
        console.log("إعادة تهيئة الخريطة بعد تغيير وضع العرض إلى الخريطة");
        // محاكاة تغيير حجم النافذة
        window.dispatchEvent(new Event("resize"));
      }, 100);

      // تأخير آخر للتأكد من تطبيق التغييرات
      setTimeout(() => {
        window.dispatchEvent(new Event("resize"));
      }, 500);
    }
  }, [viewMode]);
  const [showFilters, setShowFilters] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [suggestions, setSuggestions] = useState([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  // البحث الذكي مفعل دائمًا بدون الحاجة لمتغير حالة
  const [searchCriteria, setSearchCriteria] = useState(null);
  const [showFeedback, setShowFeedback] = useState(false);
  const searchInputRef = useRef(null);
  const mapBoxRef = useRef(null);

  // تأكد من أن المهن المحددة تبدأ كمصفوفة فارغة
  const [selectedProfessions, setSelectedProfessions] = useState([]);

  // حالة لتخزين التصنيف المحدد
  const [selectedCategory, setSelectedCategory] = useState(null);

  // تأكد من عدم وجود مهنة محددة افتراضيًا عند تحميل الصفحة
  useEffect(() => {
    // إعادة تعيين المهن المحددة إلى مصفوفة فارغة
    setSelectedProfessions([]);
    // إعادة تعيين فلتر المهنة إلى قيمة فارغة
    setFilters((prev) => ({
      ...prev,
      profession: "",
      professions: [],
    }));
  }, []);

  const [selectedRating, setSelectedRating] = useState(0);

  // حالة تحديد الموقع
  const [locationStatus, setLocationStatus] = useState("loading"); // loading, success, error
  const [locationMessage, setLocationMessage] = useState("جاري تحديد موقعك...");

  const [filters, setFilters] = useState({
    profession: "", // للتوافق مع الكود القديم
    professions: [], // مصفوفة للمهن المتعددة
    available: false,
    rating: 0,
    // سيتم تحديد الموقع تلقائياً، والموقع الافتراضي هو دمشق في حالة الفشل
    location: { lng: 36.2765, lat: 33.5138 }, // Damascus, Syria - موقع افتراضي
    radius: 1,
  });

  // Estados para las calles, hospitales y mezquitas
  const [streets, setStreets] = useState([]);
  const [removedStreets, setRemovedStreets] = useState([]);
  const [showAllStreets, setShowAllStreets] = useState(false);

  // متغيرات حالة للمستشفيات
  const [hospitals, setHospitals] = useState([]);
  const [removedHospitals, setRemovedHospitals] = useState([]);
  const [showAllHospitals, setShowAllHospitals] = useState(false);

  // متغيرات حالة للمساجد
  const [mosques, setMosques] = useState([]);
  const [removedMosques, setRemovedMosques] = useState([]);
  const [showAllMosques, setShowAllMosques] = useState(false);

  // Estado para almacenar los craftsmen filtrados por el componente AdvancedMap
  const [mapFilteredCraftsmen, setMapFilteredCraftsmen] = useState([]);

  // حالة لتتبع ما إذا كان المستخدم قد قام بتحريك الدبوس أو تغيير النطاق أو ضغط على زر التطبيق
  const [hasInteracted, setHasInteracted] = useState(false);

  // حالة لتحكم في ظهور تنويه زر القائمة
  const [showListTooltip, setShowListTooltip] = useState(false);
  const listButtonRef = useRef(null);

  // استخدام Intersection Observer لمراقبة ظهور زر القائمة في الشاشة
  useEffect(() => {
    if (!listButtonRef.current) return;

    const observer = new IntersectionObserver(
      (entries) => {
        const [entry] = entries;
        // إذا أصبح الزر مرئيًا في الشاشة
        if (entry.isIntersecting) {
          setShowListTooltip(true);
          // إخفاء التلميح بعد 5 ثوانٍ
          const timer = setTimeout(() => {
            setShowListTooltip(false);
          }, 5000);
          return () => clearTimeout(timer);
        }
      },
      {
        root: null, // استخدام viewport
        threshold: 0.5, // عندما يكون 50% من العنصر مرئيًا
        rootMargin: "0px", // بدون هوامش إضافية
      }
    );

    observer.observe(listButtonRef.current);

    return () => {
      if (listButtonRef.current) {
        observer.unobserve(listButtonRef.current);
      }
    };
  }, []);

  // تحديد الموقع التلقائي عند تحميل الصفحة
  useEffect(() => {
    const initializeLocation = async () => {
      try {
        const location = await getLocationWithFeedback(
          (newLocation) => {
            // تحديث الموقع في الفلاتر
            setFilters((prev) => ({
              ...prev,
              location: { lat: newLocation.lat, lng: newLocation.lng },
            }));
          },
          (message, status) => {
            setLocationMessage(message);
            setLocationStatus(status);
          }
        );

        console.log("تم تحديد الموقع:", location);
      } catch (error) {
        console.error("خطأ في تحديد الموقع:", error);
        setLocationStatus("error");
        setLocationMessage("فشل في تحديد الموقع، تم استخدام الموقع الافتراضي");
      }
    };

    initializeLocation();
  }, []);

  useEffect(() => {
    // Fetch craftsmen once when component mounts
    fetchCraftsmen();

    // Apply initial filters
    filterCraftsmen(filters);

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // تأكد من تحديث البيانات عند تغيير حالة تسجيل الدخول
  useEffect(() => {
    // التحقق مما إذا كان قد تم التحقق من المصادقة بالفعل
    const authVerified = localStorage.getItem("authVerified");
    const user = useUserStore.getState().user;

    // إذا كان هناك توكن وتم التحقق من المصادقة بالفعل أو المستخدم موجود بالفعل، نقوم بتحميل البيانات
    if ((isTokenExist && authVerified === "true") || user) {
      console.log("تم التحقق من المصادقة مسبقًا، تحميل البيانات في صفحة البحث");
      fetchCraftsmen();
      filterCraftsmen(filters);
    }
  }, [isTokenExist, fetchCraftsmen, filterCraftsmen, filters]);

  // Efecto para actualizar el mapa cuando cambia la ubicación
  useEffect(() => {
    // Cuando cambia la ubicación, necesitamos actualizar el mapa
    console.log("Location changed:", filters.location);
    if (mapBoxRef.current && filters.location) {
      // Actualizar la ubicación del mapa cuando cambia filters.location
      mapBoxRef.current.updateLocation(filters.location);
    }
  }, [filters.location]);

  const handleFilterChange = (e) => {
    const { name, value, type, checked } = e.target;
    const newValue = type === "checkbox" ? checked : value;

    // تحديث الفلاتر
    setFilters({
      ...filters,
      [name]: newValue,
    });

    // إذا كان التغيير في التقييم، قم بتحديث selectedRating أيضًا
    if (name === "rating") {
      setSelectedRating(parseFloat(value));
    }

    // تطبيق الفلتر مباشرة
    filterCraftsmen({
      ...filters,
      [name]: newValue,
    });
  };

  const handleLocationSelect = (location) => {
    // تعيين حالة التفاعل إلى true عند تحريك الدبوس
    setHasInteracted(true);

    setFilters({
      ...filters,
      location,
    });
    // عند تغيير الموقع يدوياً، نقوم بتحديث الموقع في الخريطة
    // الخريطة ستتحرك تلقائياً بفضل مكون MapController الذي أضفناه

    // تحديث الشوارع والمساجد والمستشفيات عند تغيير الموقع
    // دالة لجلب البيانات من Geoapify Places API
    const fetchDataFromGeoapify = async (lat, lng, radius) => {
      try {
        // إظهار مؤشر التحميل
        setIsStreetsLoading(true);

        // استخدام خدمة Geoapify من خلال mapService
        const result = await fetchStreetsFromOverpass(lat, lng, radius);

        console.log("تم جلب البيانات من Geoapify:", result);

        return {
          streets: result.streets || [],
          hospitals: result.hospitals || [],
          mosques: result.mosques || [],
        };
      } catch (error) {
        console.error("خطأ في جلب البيانات من Geoapify:", error);
        return { streets: [], hospitals: [], mosques: [] };
      }
    };

    // استدعاء الدالة لجلب البيانات
    fetchDataFromGeoapify(location.lat, location.lng, filters.radius)
      .then((data) => {
        // تأخير لمدة ثانية لإظهار مؤشر التحميل
        setTimeout(() => {
          // تحديث الشوارع
          setStreets(data.streets || []);
          setRemovedStreets([]);

          // تحديث المساجد
          setMosques(data.mosques || []);
          setRemovedMosques([]);

          // تحديث المستشفيات
          setHospitals(data.hospitals || []);
          setRemovedHospitals([]);

          // إخفاء مؤشر التحميل
          setIsStreetsLoading(false);
        }, 1000);
      })
      .catch((error) => {
        console.error("خطأ في جلب البيانات:", error);
        // إخفاء مؤشر التحميل في حالة حدوث خطأ
        setTimeout(() => {
          setIsStreetsLoading(false);
        }, 1000);
      });
  };

  const handleRadiusChange = (radius) => {
    // Puede recibir el valor directamente o desde un evento
    const newRadius =
      typeof radius === "object" ? parseFloat(radius.target.value) : radius;

    // تعيين حالة التفاعل إلى true عند تغيير نطاق البحث
    setHasInteracted(true);

    // تحديث الفلاتر مع نطاق العمل الجديد
    const updatedFilters = {
      ...filters,
      radius: newRadius,
    };

    setFilters(updatedFilters);

    // تطبيق الفلتر مباشرة لتحديث الخريطة المتقدمة
    filterCraftsmen(updatedFilters);

    // تحديث الخريطة في MapBox إذا كانت متاحة
    if (mapBoxRef.current) {
      mapBoxRef.current.updateRadius(newRadius);
    }

    // تحديث الشوارع والمساجد والمستشفيات عند تغيير نطاق العمل
    // استدعاء دالة handleStreetsChange مع isPinMoved = true لتحديث البيانات
    handleStreetsChange(null, [], true);
  };

  const handleSearch = (e) => {
    e.preventDefault();

    // تنفيذ البحث الذكي أولاً إذا كان هناك نص بحث
    if (searchQuery.trim()) {
      // تحليل الاستعلام باستخدام اللغة الطبيعية
      const criteria = analyzeQuery(searchQuery);

      // طباعة معايير البحث للتصحيح
      console.log("معايير البحث المستخرجة:", criteria);

      // بناء معايير البحث الجديدة بناءً على نتائج التحليل
      let updatedProfessions = [...filters.professions];

      // إذا تم العثور على مهنة في البحث، أضفها إلى مصفوفة المهن
      if (
        criteria.profession &&
        !updatedProfessions.includes(criteria.profession)
      ) {
        updatedProfessions.push(criteria.profession);
      }

      const searchFilters = {
        ...filters,
        // تحديث المهنة إذا وجدت (للتوافق مع الكود القديم)
        profession: criteria.profession || filters.profession,

        // تحديث مصفوفة المهن
        professions: updatedProfessions,

        // تحديث التقييم إذا وجد
        rating: criteria.rating > 0 ? criteria.rating : filters.rating,

        // تحديث الموقع إذا وجد
        location: criteria.location || filters.location,
        // إذا تم تحديد موقع جديد، سيتم تحريك الخريطة تلقائياً إليه بفضل مكون MapController

        // تحديث نطاق البحث إذا وجد
        radius: criteria.radius || filters.radius,
      };

      // طباعة الفلاتر المحدثة للتصحيح
      console.log("الفلاتر المحدثة:", searchFilters);

      // تحديث الفلاتر
      setFilters(searchFilters);

      // تحديث المهن المحددة في واجهة المستخدم
      if (
        criteria.profession &&
        !selectedProfessions.includes(criteria.profession)
      ) {
        setSelectedProfessions([...selectedProfessions, criteria.profession]);
      }

      // إذا تم العثور على شارع أو حي، أضفه إلى قائمة الشوارع
      if (criteria.street && !streets.includes(criteria.street)) {
        setStreets((prev) => [...prev, criteria.street]);
      }

      // تنفيذ البحث باستخدام المعايير الجديدة
      filterCraftsmen(searchFilters);
      setShowSuggestions(false);

      // عرض التغذية الراجعة فقط إذا تم العثور على معلومات مفيدة
      const hasUsefulInfo =
        criteria.profession ||
        criteria.city ||
        criteria.street ||
        criteria.rating > 0 ||
        criteria.radius;

      if (hasUsefulInfo) {
        // حفظ معايير البحث لعرضها للمستخدم
        setSearchCriteria(criteria);
        setShowFeedback(true);

        // للتسجيل في وحدة التحكم
        let message = "تم البحث عن: ";
        if (criteria.profession)
          message += `\n- المهنة: ${criteria.profession}`;
        if (criteria.city) message += `\n- المدينة: ${criteria.city}`;
        if (criteria.street) message += `\n- المنطقة: ${criteria.street}`;
        if (criteria.hospital) message += `\n- المشفى: ${criteria.hospital}`;
        if (criteria.mosque) message += `\n- الجامع: ${criteria.mosque}`;
        if (criteria.rating > 0)
          message += `\n- التقييم: ${criteria.rating} نجوم`;
        if (criteria.radius) message += `\n- نطاق البحث: ${criteria.radius} كم`;

        console.log(message);
      }
    } else {
      // البحث باستخدام الفلاتر فقط
      console.log("تطبيق البحث بالفلاتر فقط:", filters);
      filterCraftsmen(filters);
    }
  };

  const handleResetFilters = async () => {
    console.log("إعادة ضبط جميع الفلاتر");

    // الحصول على الموقع الحالي أو استخدام الموقع الافتراضي
    let currentLocation = filters.location;
    try {
      const location = await getCurrentLocation();
      currentLocation = { lat: location.lat, lng: location.lng };
    } catch (error) {
      console.log("استخدام الموقع الحالي في إعادة الضبط");
    }

    const resetFilters = {
      profession: "",
      professions: [], // إعادة تعيين مصفوفة المهن
      available: false,
      rating: 0,
      location: currentLocation, // استخدام الموقع الحالي
      radius: 1,
    };

    setFilters(resetFilters);
    setSelectedProfessions([]); // إعادة تعيين المهن المحددة
    setSelectedCategory(null); // إعادة تعيين التصنيف المحدد
    setSearchQuery("");
    setSelectedRating(0); // إعادة تعيين التقييم المحدد

    // إعادة تعيين الشوارع والمستشفيات والمساجد
    setStreets([]);
    setRemovedStreets([]);
    setHospitals([]);
    setRemovedHospitals([]);
    setMosques([]);
    setRemovedMosques([]);

    // إعادة تعيين حالة عرض المزيد
    setShowAllStreets(false);
    setShowAllHospitals(false);
    setShowAllMosques(false);

    setSuggestions([]);
    setShowFeedback(false);
    setSearchCriteria(null);

    // إعادة تعيين حالة التفاعل
    setHasInteracted(false);

    // تطبيق الفلتر مباشرة
    console.log("تطبيق الفلتر بعد إعادة الضبط");
    filterCraftsmen(resetFilters);

    // لا نقوم بتغيير وضع العرض هنا
    // تم إزالة: setViewMode("list");
  };

  // إضافة حالة لمؤشر التحميل
  const [isStreetsLoading, setIsStreetsLoading] = useState(false);

  // دالة لتحديث الشوارع والمساجد والمستشفيات ضمن نطاق العمل
  const handleStreetsChange = (
    newData,
    removedStreetsList,
    isPinMoved = false
  ) => {
    // إذا كان isPinMoved صحيحًا، فهذا يعني أن المستخدم قام بتحريك الدبوس أو تغيير نطاق العمل
    if (isPinMoved) {
      // إظهار مؤشر التحميل
      setIsStreetsLoading(true);

      // دالة لجلب البيانات من Geoapify Places API
      const fetchDataFromGeoapify = async (lat, lng, radius) => {
        try {
          // استخدام خدمة Geoapify من خلال mapService
          const result = await fetchStreetsFromOverpass(lat, lng, radius);

          console.log(
            "تم جلب البيانات من Geoapify في handleStreetsChange:",
            result
          );

          return {
            streets: result.streets || [],
            hospitals: result.hospitals || [],
            mosques: result.mosques || [],
          };
        } catch (error) {
          console.error("خطأ في جلب البيانات من Geoapify:", error);
          return { streets: [], hospitals: [], mosques: [] };
        }
      };

      // استدعاء الدالة لجلب البيانات
      fetchDataFromGeoapify(
        filters.location.lat,
        filters.location.lng,
        filters.radius
      )
        .then((data) => {
          // تأخير لمدة ثانية لإظهار مؤشر التحميل
          setTimeout(() => {
            // تحديث الشوارع
            setStreets(data.streets || []);
            setRemovedStreets(removedStreetsList || []);

            // تحديث المساجد
            setMosques(data.mosques || []);
            setRemovedMosques([]);

            // تحديث المستشفيات
            setHospitals(data.hospitals || []);
            setRemovedHospitals([]);

            // إخفاء مؤشر التحميل
            setIsStreetsLoading(false);
          }, 1000);
        })
        .catch((error) => {
          console.error("خطأ في جلب البيانات:", error);
          // إخفاء مؤشر التحميل في حالة حدوث خطأ
          setTimeout(() => {
            setIsStreetsLoading(false);
          }, 1000);
        });
    } else {
      // تحديث البيانات مباشرة بدون مؤشر تحميل
      if (newData && typeof newData === "object" && newData.streets) {
        // تحديث الشوارع والمستشفيات والمساجد
        setStreets(newData.streets || []);
        setHospitals(newData.hospitals || []);
        setMosques(newData.mosques || []);
      } else {
        // إذا كانت البيانات عبارة عن مصفوفة، فهي شوارع فقط
        setStreets(Array.isArray(newData) ? newData : []);
      }

      setRemovedStreets(removedStreetsList || []);
    }
  };

  // دوال إزالة العناصر
  const handleRemoveStreet = (street) => {
    setRemovedStreets((prev) => [...prev, street]);
  };

  const handleRemoveHospital = (hospital) => {
    setRemovedHospitals((prev) => [...prev, hospital]);
  };

  const handleRemoveMosque = (mosque) => {
    setRemovedMosques((prev) => [...prev, mosque]);
  };

  // دوال التعامل مع الفلتر المتقدم
  const handleProfessionChange = (professions) => {
    setSelectedProfessions(professions);

    // تحديث الفلاتر بناءً على المهن المحددة
    const updatedFilters = {
      ...filters,
      professions: professions,
    };

    setFilters(updatedFilters);

    // تطبيق الفلتر مباشرة
    filterCraftsmen(updatedFilters);
  };

  // دالة لتبديل حالة اختيار المهنة (إضافة/إزالة)
  const handleProfessionToggle = (profession) => {
    console.log("تم النقر على المهنة:", profession);

    let updatedProfessions;
    if (selectedProfessions.includes(profession)) {
      // إزالة المهنة إذا كانت موجودة بالفعل
      console.log("إزالة المهنة:", profession);
      updatedProfessions = selectedProfessions.filter((p) => p !== profession);
    } else {
      // إضافة المهنة إذا لم تكن موجودة
      console.log("إضافة المهنة:", profession);
      updatedProfessions = [...selectedProfessions, profession];
    }

    // تحديث حالة المهن المحددة
    setSelectedProfessions(updatedProfessions);

    // تحديث الفلاتر
    const updatedFilters = {
      ...filters,
      professions: updatedProfessions,
    };

    console.log("الفلاتر المحدثة:", updatedFilters);
    setFilters(updatedFilters);

    // طباعة المهن المحددة للتصحيح
    console.log("المهن المحددة بعد التحديث:", updatedProfessions);

    // تطبيق الفلتر مباشرة
    console.log("تطبيق الفلتر مع المهن:", updatedProfessions);
    filterCraftsmen(updatedFilters);

    // تغيير وضع العرض إلى القائمة بعد تطبيق الفلتر
    setViewMode("list");
  };

  // دالة لمسح جميع المهن المحددة
  const handleClearProfessions = () => {
    console.log("مسح جميع المهن المحددة");

    setSelectedProfessions([]);
    setSelectedCategory(null); // إعادة تعيين التصنيف المحدد أيضًا

    // تحديث الفلاتر
    const updatedFilters = {
      ...filters,
      professions: [],
    };

    console.log("الفلاتر بعد المسح:", updatedFilters);
    setFilters(updatedFilters);

    // تطبيق الفلتر مباشرة
    console.log("تطبيق الفلتر بعد مسح المهن");
    filterCraftsmen(updatedFilters);

    // لا نقوم بتغيير وضع العرض هنا
    // تم إزالة: setViewMode("list");
  };

  const handleRatingChange = (rating) => {
    // تحديث قيمة التقييم المحددة
    setSelectedRating(rating);

    // تحديث فلتر التقييم في الفلاتر الرئيسية
    const updatedFilters = {
      ...filters,
      rating: rating,
    };

    setFilters(updatedFilters);

    // تطبيق الفلتر مباشرة
    filterCraftsmen(updatedFilters);

    console.log("تم تحديث التقييم إلى:", rating);
  };

  // Filter craftsmen by search query (name or profession)
  const searchFilteredCraftsmen = searchQuery
    ? filteredCraftsmen.filter((craftsman) => {
        // تحقق من الاسم
        if (
          craftsman.name &&
          craftsman.name.toLowerCase().includes(searchQuery.toLowerCase())
        ) {
          return true;
        }

        // تحقق من المهنة الرئيسية
        if (
          craftsman.profession &&
          craftsman.profession.toLowerCase().includes(searchQuery.toLowerCase())
        ) {
          return true;
        }

        // تحقق من التخصص
        if (
          craftsman.specialization &&
          craftsman.specialization
            .toLowerCase()
            .includes(searchQuery.toLowerCase())
        ) {
          return true;
        }

        // تحقق من مصفوفة المهن إذا كانت موجودة
        if (craftsman.professions && Array.isArray(craftsman.professions)) {
          // تحقق مما إذا كانت أي من المهن تحتوي على نص البحث
          const hasProfessionMatch = craftsman.professions.some(
            (profession) => {
              if (typeof profession === "string") {
                return profession
                  .toLowerCase()
                  .includes(searchQuery.toLowerCase());
              } else if (profession && profession.name) {
                return profession.name
                  .toLowerCase()
                  .includes(searchQuery.toLowerCase());
              }
              return false;
            }
          );

          if (hasProfessionMatch) {
            return true;
          }
        }

        // تحقق من مصفوفة التخصصات إذا كانت موجودة
        if (
          craftsman.specializations &&
          Array.isArray(craftsman.specializations)
        ) {
          const hasSpecializationMatch = craftsman.specializations.some(
            (spec) => {
              if (typeof spec === "string") {
                return spec.toLowerCase().includes(searchQuery.toLowerCase());
              } else if (spec && spec.name) {
                return spec.name
                  .toLowerCase()
                  .includes(searchQuery.toLowerCase());
              }
              return false;
            }
          );

          if (hasSpecializationMatch) {
            return true;
          }
        }

        return false;
      })
    : filteredCraftsmen;

  // استخدام المستخدم المحدث من المتجر
  const currentUser = useUserStore((state) => state.user);

  return (
    <Layout user={currentUser} onLogout={logout}>
      <div
        className={`min-h-screen ${
          darkMode
            ? "bg-gray-900"
            : "bg-gradient-to-br from-blue-50 to-indigo-100"
        } transition-colors duration-300`}
      >
        {/* Search Header */}
        <section
          className={`${
            darkMode
              ? "bg-gradient-to-r from-gray-800 to-gray-700"
              : "bg-gradient-to-r from-blue-700 to-indigo-800"
          } text-white py-8 shadow-lg transition-colors duration-300`}
        >
          <div className="container mx-auto px-4">
            <h1 className="text-2xl font-bold mb-4 relative inline-block">
              <span className="relative z-10">البحث عن حرفي</span>
              <span className="absolute bottom-0 left-0 right-0 h-2 bg-indigo-500 opacity-30 transform -rotate-1 z-0"></span>
            </h1>
            <div className="relative">
              {/* تنسيق مختلف للشاشات الصغيرة والكبيرة */}
              <div className="flex flex-col sm:flex-row gap-2 sm:gap-0">
                {/* حقل البحث */}
                <div className="relative flex-grow">
                  {/* للشاشات الكبيرة */}
                  <div className="hidden sm:block relative">
                    <div className="relative shadow-lg rounded-l-xl overflow-hidden">
                      <input
                        ref={searchInputRef}
                        type="text"
                        value={searchQuery}
                        onChange={(e) => {
                          setSearchQuery(e.target.value);
                          if (e.target.value.length >= 3) {
                            const newSuggestions = suggestQueries(
                              e.target.value
                            );
                            setSuggestions(newSuggestions);
                            setShowSuggestions(newSuggestions.length > 0);
                          } else {
                            setShowSuggestions(false);
                          }
                        }}
                        onKeyDown={(e) => {
                          if (e.key === "Enter") {
                            e.preventDefault();
                            handleSearch(e);
                          }
                        }}
                        onFocus={() => {
                          if (searchQuery.length >= 3) {
                            const newSuggestions = suggestQueries(searchQuery);
                            setSuggestions(newSuggestions);
                            setShowSuggestions(newSuggestions.length > 0);
                          }
                        }}
                        onBlur={() => {
                          setTimeout(() => setShowSuggestions(false), 200);
                        }}
                        placeholder="ابحث بلغتك الطبيعية مثل: أريد سباك لإصلاح تسريب في المنزل بدمشق..."
                        className={`w-full py-4 px-5 pr-12 rounded-r-none rounded-l-xl text-base ${
                          darkMode
                            ? "bg-gray-700/90 border-gray-600 text-white placeholder-gray-400"
                            : "bg-white/95 border-indigo-200 text-gray-800 placeholder-gray-500"
                        } focus:border-indigo-500 focus:ring-2 focus:ring-indigo-300 focus:ring-opacity-50 shadow-inner transition-all duration-300`}
                      />
                      <div
                        className={`absolute left-4 top-1/2 transform -translate-y-1/2 p-1.5 rounded-full ${
                          darkMode ? "bg-indigo-600/30" : "bg-indigo-100"
                        }`}
                      >
                        <Search
                          className={`${
                            darkMode ? "text-indigo-300" : "text-indigo-600"
                          }`}
                          size={20}
                        />
                      </div>
                    </div>
                  </div>

                  {/* للشاشات الصغيرة */}
                  <div className="sm:hidden relative">
                    <div className="relative shadow-lg rounded-xl overflow-hidden">
                      <input
                        ref={searchInputRef}
                        type="text"
                        value={searchQuery}
                        onChange={(e) => {
                          setSearchQuery(e.target.value);
                          if (e.target.value.length >= 3) {
                            const newSuggestions = suggestQueries(
                              e.target.value
                            );
                            setSuggestions(newSuggestions);
                            setShowSuggestions(newSuggestions.length > 0);
                          } else {
                            setShowSuggestions(false);
                          }
                        }}
                        onKeyDown={(e) => {
                          if (e.key === "Enter") {
                            e.preventDefault();
                            handleSearch(e);
                          }
                        }}
                        onFocus={() => {
                          if (searchQuery.length >= 3) {
                            const newSuggestions = suggestQueries(searchQuery);
                            setSuggestions(newSuggestions);
                            setShowSuggestions(newSuggestions.length > 0);
                          }
                        }}
                        onBlur={() => {
                          setTimeout(() => setShowSuggestions(false), 200);
                        }}
                        placeholder="ابحث بلغتك الطبيعية..."
                        className={`w-full py-3.5 px-5 pr-12 rounded-xl text-base ${
                          darkMode
                            ? "bg-gray-700/90 text-white placeholder-gray-400 border border-gray-600"
                            : "bg-white/95 text-gray-800 placeholder-gray-500 border border-indigo-200"
                        } focus:ring-2 focus:ring-indigo-500 focus:ring-opacity-50 shadow-inner transition-all duration-300`}
                      />
                      <div
                        className={`absolute left-4 top-1/2 transform -translate-y-1/2 p-1.5 rounded-full ${
                          darkMode ? "bg-indigo-600/30" : "bg-indigo-100"
                        }`}
                      >
                        <Search
                          className={`${
                            darkMode ? "text-indigo-300" : "text-indigo-600"
                          }`}
                          size={18}
                        />
                      </div>
                    </div>
                  </div>
                </div>

                {/* زر البحث */}
                <div className="sm:hidden">
                  <button
                    type="button"
                    onClick={handleSearch}
                    className={`w-full py-3.5 px-6 rounded-xl flex items-center justify-center ${
                      darkMode
                        ? "bg-gradient-to-r from-indigo-600 to-indigo-800 text-white"
                        : "bg-gradient-to-r from-blue-500 to-indigo-600 text-white"
                    } transition-all duration-300 shadow-lg hover:shadow-xl relative overflow-hidden group`}
                  >
                    <span className="relative z-10 flex items-center gap-2">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="18"
                        height="18"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      >
                        <path d="m21 21-6.05-6.05M17 10a7 7 0 1 1-14 0 7 7 0 0 1 14 0Z" />
                      </svg>
                      <span className="font-bold">بحث</span>
                    </span>
                    <span className="absolute inset-0 bg-white opacity-20 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700"></span>
                  </button>
                </div>

                <div className="hidden sm:block">
                  <button
                    type="button"
                    onClick={handleSearch}
                    className={`px-6 py-4 mr-2 rounded-l-xl rounded-r-none flex items-center justify-center ${
                      darkMode
                        ? "bg-gradient-to-r from-indigo-600 to-indigo-800 text-white"
                        : "bg-gradient-to-r from-blue-500 to-indigo-600 text-white"
                    } transition-all duration-300 shadow-lg hover:shadow-xl relative overflow-hidden group`}
                  >
                    <span className="relative z-10 flex items-center gap-2">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="18"
                        height="18"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      >
                        <path d="m21 21-6.05-6.05M17 10a7 7 0 1 1-14 0 7 7 0 0 1 14 0Z" />
                      </svg>
                      <span className="font-bold">بحث</span>
                    </span>
                    <span className="absolute inset-0 bg-white opacity-20 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700"></span>
                  </button>
                </div>
              </div>
              {/* عرض التغذية الراجعة عن نتائج تحليل البحث */}
              {showFeedback && searchCriteria && (
                <SearchFeedback
                  criteria={searchCriteria}
                  onClose={() => setShowFeedback(false)}
                />
              )}

              {showSuggestions && (
                <div className="relative ">
                  <SearchSuggestions
                    suggestions={suggestions}
                    onSelectSuggestion={(suggestion) => {
                      setSearchQuery(suggestion);
                      setShowSuggestions(false);
                      // تنفيذ البحث تلقائياً عند اختيار اقتراح
                      const criteria = analyzeQuery(suggestion);
                      const searchFilters = {
                        ...filters,
                        profession: criteria.profession || filters.profession,
                        // إذا تم العثور على مهنة في البحث، أضفها إلى مصفوفة المهن
                        professions: criteria.profession
                          ? [
                              ...filters.professions.filter(
                                (p) => p !== criteria.profession
                              ),
                              criteria.profession,
                            ]
                          : filters.professions,
                        rating:
                          criteria.rating > 0
                            ? criteria.rating
                            : filters.rating,
                        location: criteria.location || filters.location,
                        // إذا تم تحديد موقع جديد، سيتم تحريك الخريطة تلقائياً إليه بفضل مكون MapController
                        radius: criteria.radius || filters.radius,
                      };

                      // تحديث الفلاتر
                      setFilters(searchFilters);

                      // إذا تم العثور على شارع أو حي، أضفه إلى قائمة الشوارع
                      if (
                        criteria.street &&
                        !streets.includes(criteria.street)
                      ) {
                        setStreets((prev) => [...prev, criteria.street]);
                      }

                      // تنفيذ البحث
                      filterCraftsmen(searchFilters);

                      // عرض التغذية الراجعة
                      setSearchCriteria(criteria);
                      setShowFeedback(true);
                    }}
                  />
                </div>
              )}
            </div>
          </div>
        </section>

        {/* تنويه للمستخدمين غير المسجلين */}
        {!user && (
          <div className="login-notice">
            <div className="login-notice-icon">
              <Lock size={22} />
            </div>
            <div className="login-notice-content">
              <div className="login-notice-title">
                تنبيه: وصول محدود للمعلومات
              </div>
              <div className="login-notice-text">
                لا يمكنك الوصول إلى معلومات الحرفيين أو مشاهدة مواقعهم وأرقام
                هواتفهم بدون تسجيل الدخول. قم بتسجيل الدخول الآن للاستفادة من
                جميع مميزات المنصة والوصول الكامل لبيانات الحرفيين.
              </div>
              <div className="login-notice-buttons">
                <Link to="/login" className="login-notice-button login-button">
                  تسجيل الدخول
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="mr-1"
                  >
                    <path d="M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4"></path>
                    <polyline points="10 17 15 12 10 7"></polyline>
                    <line x1="15" y1="12" x2="3" y2="12"></line>
                  </svg>
                </Link>
                <Link
                  to="/register/client"
                  className="login-notice-button register-button"
                >
                  التسجيل كطالب خدمة
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="mr-1"
                  >
                    <path d="M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                    <circle cx="8.5" cy="7" r="4"></circle>
                    <line x1="20" y1="8" x2="20" y2="14"></line>
                    <line x1="23" y1="11" x2="17" y2="11"></line>
                  </svg>
                </Link>
              </div>
            </div>
          </div>
        )}

        <div className="container mx-auto px-4 py-8">
          <div className="flex flex-col md:flex-row gap-6">
            {/* Filters Sidebar */}
            <div
              className={`md:w-1/3 ${
                showFilters ? "block" : "hidden md:block"
              }`}
            >
              <Card
                className={`sticky top-4 p-4 overflow-hidden ${
                  darkMode
                    ? "bg-gray-800 text-gray-200"
                    : "bg-gradient-to-br from-blue-50 to-indigo-100"
                } shadow-lg transition-colors duration-300`}
              >
                <div className="flex justify-between items-center mb-4">
                  <h2
                    className={`text-xl font-bold ${
                      darkMode ? "text-indigo-300" : "text-indigo-800"
                    } relative inline-block transition-colors duration-300`}
                  >
                    <span className="relative z-10">الفلاتر</span>
                    <span
                      className={`absolute bottom-0 left-0 right-0 h-2 ${
                        darkMode ? "bg-indigo-500" : "bg-indigo-300"
                      } opacity-40 transform -rotate-1 z-0`}
                    ></span>
                  </h2>

                  {/* مؤشر حالة الموقع */}
                  {locationStatus !== "hidden" && (
                    <div
                      className={`mt-3 p-2 rounded-md text-sm ${
                        locationStatus === "loading"
                          ? darkMode
                            ? "bg-blue-900/30 text-blue-200"
                            : "bg-blue-50 text-blue-700"
                          : locationStatus === "success"
                          ? darkMode
                            ? "bg-green-900/30 text-green-200"
                            : "bg-green-50 text-green-700"
                          : locationStatus === "warning"
                          ? darkMode
                            ? "bg-yellow-900/30 text-yellow-200"
                            : "bg-yellow-50 text-yellow-700"
                          : darkMode
                          ? "bg-red-900/30 text-red-200"
                          : "bg-red-50 text-red-700"
                      }`}
                    >
                      <div className="flex items-center gap-2">
                        {locationStatus === "loading" && (
                          <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin"></div>
                        )}
                        {locationStatus === "success" && (
                          <svg
                            className="w-4 h-4"
                            fill="currentColor"
                            viewBox="0 0 20 20"
                          >
                            <path
                              fillRule="evenodd"
                              d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                              clipRule="evenodd"
                            />
                          </svg>
                        )}
                        {locationStatus === "warning" && (
                          <svg
                            className="w-4 h-4"
                            fill="currentColor"
                            viewBox="0 0 20 20"
                          >
                            <path
                              fillRule="evenodd"
                              d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                              clipRule="evenodd"
                            />
                          </svg>
                        )}
                        {locationStatus === "error" && (
                          <svg
                            className="w-4 h-4"
                            fill="currentColor"
                            viewBox="0 0 20 20"
                          >
                            <path
                              fillRule="evenodd"
                              d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                              clipRule="evenodd"
                            />
                          </svg>
                        )}
                        <span>{locationMessage}</span>
                      </div>
                    </div>
                  )}
                  <button
                    onClick={handleResetFilters}
                    className="text-indigo-600 font-medium hover:text-indigo-800 transition-colors duration-200 text-sm"
                  >
                    إعادة ضبط
                  </button>
                </div>

                <form
                  onSubmit={handleSearch}
                  onKeyDown={(e) => {
                    // Evitar que el formulario se envíe al presionar Enter en los campos individuales
                    // ya que queremos manejar esto en el campo de búsqueda principal
                    if (e.key === "Enter" && e.target.tagName !== "FORM") {
                      e.preventDefault();
                      handleSearch(e);
                    }
                  }}
                >
                  <div className="mb-4">
                    <label
                      className={`block ${
                        darkMode ? "text-gray-300" : "text-gray-700"
                      } font-medium mb-2 transition-colors duration-300`}
                    >
                      المهنة
                    </label>

                    {/* تصنيفات المهن */}
                    <div className="mb-3">
                      <h3
                        className={`text-sm font-medium mb-2 ${
                          darkMode ? "text-gray-400" : "text-gray-600"
                        }`}
                      >
                        اختر التصنيف:
                      </h3>
                      <div className="flex flex-wrap gap-2 mb-3">
                        {professionCategories.map((category) => (
                          <div
                            key={category.id}
                            onClick={() => {
                              // تحديد التصنيف المحدد
                              setSelectedCategory(
                                selectedCategory === category.id
                                  ? null
                                  : category.id
                              );
                            }}
                            className={`cursor-pointer px-3 py-2 rounded-md text-sm transition-all duration-200 ${
                              selectedCategory === category.id
                                ? darkMode
                                  ? "bg-indigo-700 text-white"
                                  : "bg-indigo-500 text-white"
                                : darkMode
                                ? "bg-gray-700 text-gray-300 hover:bg-gray-600"
                                : "bg-gray-100 text-gray-700 hover:bg-gray-200"
                            }`}
                          >
                            {category.name}
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* المهن ضمن التصنيف المحدد */}
                    {selectedCategory && (
                      <div className="mt-2">
                        <h3
                          className={`text-sm font-medium mb-2 ${
                            darkMode ? "text-gray-400" : "text-gray-600"
                          }`}
                        >
                          اختر المهنة:
                        </h3>
                        <div className="flex flex-wrap gap-2">
                          {professionCategories
                            .find((cat) => cat.id === selectedCategory)
                            ?.professions.map((profession) => (
                              <div
                                key={profession.id}
                                onClick={() =>
                                  handleProfessionToggle(profession.name)
                                }
                                className={`cursor-pointer px-3 py-2 rounded-md text-sm transition-all duration-200 ${
                                  selectedProfessions.includes(profession.name)
                                    ? darkMode
                                      ? "bg-indigo-700 text-white"
                                      : "bg-indigo-500 text-white"
                                    : darkMode
                                    ? "bg-gray-700 text-gray-300 hover:bg-gray-600"
                                    : "bg-gray-100 text-gray-700 hover:bg-gray-200"
                                }`}
                              >
                                {profession.name}
                              </div>
                            ))}
                        </div>
                      </div>
                    )}

                    {/* عرض المهن المحددة */}
                    {selectedProfessions.length > 0 && (
                      <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
                        <h3
                          className={`text-sm font-medium mb-2 ${
                            darkMode ? "text-gray-400" : "text-gray-600"
                          }`}
                        >
                          المهن المحددة:
                        </h3>
                        <div className="flex flex-wrap gap-2">
                          {selectedProfessions.map((profName, index) => (
                            <div
                              key={index}
                              className={`flex items-center px-3 py-2 rounded-md text-sm ${
                                darkMode
                                  ? "bg-indigo-700 text-white"
                                  : "bg-indigo-500 text-white"
                              }`}
                            >
                              <span>{profName}</span>
                              <button
                                onClick={() => {
                                  // إزالة المهنة من القائمة المحددة
                                  setSelectedProfessions(
                                    selectedProfessions.filter(
                                      (p) => p !== profName
                                    )
                                  );
                                  // تحديث الفلاتر
                                  setFilters((prev) => ({
                                    ...prev,
                                    professions: prev.professions.filter(
                                      (p) => p !== profName
                                    ),
                                  }));
                                }}
                                className="mr-2 text-white hover:text-red-200 focus:outline-none"
                              >
                                ×
                              </button>
                            </div>
                          ))}
                          <div
                            onClick={() => handleClearProfessions()}
                            className={`cursor-pointer px-3 py-2 rounded-md text-sm transition-all duration-200 ${
                              darkMode
                                ? "bg-red-700 text-white hover:bg-red-800"
                                : "bg-red-500 text-white hover:bg-red-600"
                            }`}
                          >
                            مسح الكل
                          </div>
                        </div>
                      </div>
                    )}
                  </div>

                  <div className="mb-4">
                    <label
                      className={`block ${
                        darkMode ? "text-gray-300" : "text-gray-700"
                      } font-medium mb-2 transition-colors duration-300`}
                    >
                      التقييم
                    </label>
                    <div className="flex items-center">
                      <input
                        type="range"
                        name="rating"
                        min="0"
                        max="5"
                        step="0.5"
                        value={filters.rating}
                        onChange={handleFilterChange}
                        className="w-full accent-indigo-500"
                      />
                      <span className="mr-2 min-w-[30px]">
                        {filters.rating}
                      </span>
                    </div>
                  </div>

                  {/* <div className="mb-4">
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        name="available"
                        checked={filters.available}
                        onChange={handleFilterChange}
                        className="ml-2"
                      />
                      <span>الحرفيون المتاحون في الوقت الحالي</span>
                    </label>
                  </div> */}

                  <div className="mb-4">
                    <div className="flex items-center justify-between mb-2">
                      <label
                        className={`${
                          darkMode ? "text-gray-300" : "text-gray-700"
                        } font-medium transition-colors duration-300`}
                      >
                        الموقع ونطاق البحث
                      </label>

                      {/* زر إعادة تحديد الموقع */}
                      <button
                        onClick={async () => {
                          setLocationStatus("loading");
                          setLocationMessage("جاري إعادة تحديد موقعك...");

                          try {
                            const location = await getLocationWithFeedback(
                              (newLocation) => {
                                setFilters((prev) => ({
                                  ...prev,
                                  location: {
                                    lat: newLocation.lat,
                                    lng: newLocation.lng,
                                  },
                                }));
                              },
                              (message, status) => {
                                setLocationMessage(message);
                                setLocationStatus(status);
                              }
                            );
                            console.log("تم إعادة تحديد الموقع:", location);
                          } catch (error) {
                            console.error("خطأ في إعادة تحديد الموقع:", error);
                            setLocationStatus("error");
                            setLocationMessage("فشل في إعادة تحديد الموقع");
                          }
                        }}
                        disabled={locationStatus === "loading"}
                        className={`group relative px-4 py-2 text-sm rounded-lg transition-all duration-300 flex items-center gap-2 font-medium shadow-lg hover:shadow-xl border border-transparent overflow-hidden ${
                          locationStatus === "loading"
                            ? darkMode
                              ? "bg-gray-700 text-gray-400 cursor-not-allowed shadow-none border-gray-600"
                              : "bg-gray-200 text-gray-500 cursor-not-allowed shadow-none border-gray-300"
                            : darkMode
                            ? "bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 hover:from-blue-700 hover:via-indigo-700 hover:to-purple-700 text-white border-blue-500 hover:border-blue-400"
                            : "bg-gradient-to-r from-blue-500 via-indigo-500 to-purple-500 hover:from-blue-600 hover:via-indigo-600 hover:to-purple-600 text-white border-blue-400 hover:border-blue-300"
                        }`}
                      >
                        {/* تأثير البريق */}
                        {locationStatus !== "loading" && (
                          <div className="absolute inset-0 -top-2 -bottom-2 bg-gradient-to-r from-transparent via-white/20 to-transparent transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700 ease-out"></div>
                        )}

                        {locationStatus === "loading" ? (
                          <>
                            <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin relative z-10"></div>
                            <span className="relative z-10">
                              جاري التحديد...
                            </span>
                          </>
                        ) : (
                          <>
                            {/* أيقونة GPS حديثة ومتطورة */}
                            <svg
                              className="w-5 h-5 drop-shadow-sm relative z-10"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              {/* دائرة خارجية للإشارة */}
                              <circle
                                cx="12"
                                cy="12"
                                r="10"
                                strokeWidth="1.5"
                                className="opacity-30"
                              />
                              {/* دائرة متوسطة */}
                              <circle
                                cx="12"
                                cy="12"
                                r="6"
                                strokeWidth="2"
                                className="opacity-60"
                              />
                              {/* النقطة المركزية */}
                              <circle
                                cx="12"
                                cy="12"
                                r="2"
                                fill="currentColor"
                                strokeWidth="0"
                              />
                              {/* خطوط الاتجاهات */}
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth="2"
                                d="M12 2v4M12 18v4M22 12h-4M6 12H2"
                                className="opacity-70"
                              />
                            </svg>
                            <span className="relative z-10">
                              إعادة تحديد الموقع
                            </span>
                          </>
                        )}
                      </button>
                    </div>
                    <div className="mb-2">
                      <MapBox
                        ref={mapBoxRef}
                        initialCenter={filters.location}
                        radius={filters.radius}
                        height="200px"
                        onLocationSelect={handleLocationSelect}
                        onRadiusChange={(radius) => handleRadiusChange(radius)}
                        onStreetsChange={handleStreetsChange}
                        showRadius={true}
                      />
                    </div>
                    <div className="flex items-center justify-between mb-2">
                      <div
                        className={`text-sm ${
                          darkMode ? "text-gray-400" : "text-gray-600"
                        } transition-colors duration-300`}
                      >
                        نطاق العمل:{" "}
                        <span className="font-bold text-indigo-600">
                          {filters.radius} كم
                        </span>
                      </div>
                    </div>
                    <input
                      type="range"
                      min="1"
                      max="5"
                      step="0.5"
                      value={filters.radius}
                      onChange={handleRadiusChange}
                      className="w-full accent-indigo-500 mb-2"
                    />

                    {/* تنويه للمستخدم - يظهر فقط على الشاشات الصغيرة */}
                    <div
                      className={`mt-2 p-3 rounded-lg text-sm md:hidden ${
                        darkMode
                          ? "bg-blue-900/30 text-blue-200 border border-blue-800/50"
                          : "bg-blue-50 text-blue-700 border border-blue-200"
                      }`}
                    >
                      <div className="flex items-start gap-2">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          className="w-5 h-5 mt-0.5 flex-shrink-0"
                        >
                          <circle cx="12" cy="12" r="10"></circle>
                          <line x1="12" y1="8" x2="12" y2="12"></line>
                          <line x1="12" y1="16" x2="12.01" y2="16"></line>
                        </svg>
                        <div>
                          <p className="font-medium">
                            مرر للأسفل لمشاهدة خريطة النتائج والحرفيين المتاحين
                          </p>
                          <div className="flex justify-center mt-2 animate-bounce">
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              viewBox="0 0 24 24"
                              fill="none"
                              stroke="currentColor"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              className="w-5 h-5"
                            >
                              <line x1="12" y1="5" x2="12" y2="19"></line>
                              <polyline points="19 12 12 19 5 12"></polyline>
                            </svg>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* قسم الشوارع - مع ارتفاع ثابت - مخفي على الشاشات الصغيرة */}
                  <div className="mb-4 h-[180px] hidden md:block">
                    <div className="mb-2">
                      <h3
                        className={`text-base font-bold ${
                          darkMode ? "text-indigo-300" : "text-indigo-800"
                        } relative inline-block transition-colors duration-300`}
                      >
                        <span className="relative z-10">
                          الشوارع ضمن نطاق العمل:
                        </span>
                        <span
                          className={`absolute bottom-0 left-0 right-0 h-1 ${
                            darkMode ? "bg-indigo-500" : "bg-indigo-300"
                          } opacity-40 transform -rotate-1 z-0`}
                        ></span>
                      </h3>
                      <div
                        className={`mt-1 text-xs ${
                          darkMode ? "text-gray-400" : "text-gray-600"
                        } flex items-center`}
                      >
                        إذا لم تظهر الشوارع، انتظر حتى يتم تحميلها. قد يتطلب ذلك
                        بضع ثوانٍ، حسب سرعة الاتصال.
                      </div>
                    </div>

                    {isStreetsLoading ? (
                      // مؤشر التحميل
                      <div className="h-[120px] flex flex-col items-center justify-center">
                        <div className="w-8 h-8 border-4 border-indigo-500 border-t-transparent rounded-full animate-spin mb-2"></div>
                        <p
                          className={`text-sm ${
                            darkMode ? "text-gray-300" : "text-gray-600"
                          }`}
                        >
                          جاري تحميل الشوارع...
                        </p>
                        <p
                          className={`text-xs mt-1 ${
                            darkMode ? "text-gray-400" : "text-gray-500"
                          }`}
                        >
                          يتم جلب البيانات من OpenStreetMap
                        </p>
                      </div>
                    ) : streets.length > 0 ? (
                      <div className="h-[120px] flex flex-col">
                        <div className="flex-1 flex flex-wrap gap-2 overflow-y-auto p-1">
                          {streets
                            .filter(
                              (street) => !removedStreets.includes(street)
                            )
                            .slice(0, showAllStreets ? 25 : 10)
                            .map((street, index) => (
                              <div
                                key={`street-${index}`}
                                className={`flex items-center ${
                                  darkMode
                                    ? "bg-gray-700 border-gray-600 text-gray-200"
                                    : "bg-blue-50 border-blue-200"
                                } rounded-full px-3 py-1 shadow-sm transition-colors duration-300`}
                              >
                                <span className="mr-1">{street}</span>
                                <button
                                  type="button"
                                  onClick={() => handleRemoveStreet(street)}
                                  className="ml-2 text-red-500 hover:text-red-700 focus:outline-none transition-colors duration-200"
                                >
                                  ×
                                </button>
                              </div>
                            ))}
                        </div>
                        {streets.filter(
                          (street) => !removedStreets.includes(street)
                        ).length > 10 && (
                          <div className="mt-2 text-center">
                            <button
                              type="button"
                              onClick={() => setShowAllStreets(!showAllStreets)}
                              className="text-indigo-600 hover:text-indigo-800 focus:outline-none transition-colors duration-200"
                            >
                              {showAllStreets ? "عرض أقل" : "عرض المزيد"}
                            </button>
                          </div>
                        )}
                      </div>
                    ) : (
                      <div className="h-[120px] flex items-center justify-center text-gray-500 italic">
                        لا توجد شوارع ضمن نطاق البحث الحالي
                      </div>
                    )}
                  </div>

                  {/* قسم المستشفيات - مع ارتفاع ثابت - مخفي على الشاشات الصغيرة */}
                  <div className="mb-4 h-[180px] hidden md:block">
                    <div className="mb-2">
                      <h3
                        className={`text-base font-bold ${
                          darkMode ? "text-indigo-300" : "text-indigo-800"
                        } relative inline-block transition-colors duration-300`}
                      >
                        <span className="relative z-10">
                          المستشفيات والعيادات ضمن نطاق العمل:
                        </span>
                        <span
                          className={`absolute bottom-0 left-0 right-0 h-1 ${
                            darkMode ? "bg-indigo-500" : "bg-indigo-300"
                          } opacity-40 transform -rotate-1 z-0`}
                        ></span>
                      </h3>
                    </div>

                    {isStreetsLoading ? (
                      // مؤشر التحميل
                      <div className="h-[120px] flex flex-col items-center justify-center">
                        <div className="w-8 h-8 border-4 border-indigo-500 border-t-transparent rounded-full animate-spin mb-2"></div>
                        <p
                          className={`text-sm ${
                            darkMode ? "text-gray-300" : "text-gray-600"
                          }`}
                        >
                          جاري تحميل المستشفيات والعيادات...
                        </p>
                        <p
                          className={`text-xs mt-1 ${
                            darkMode ? "text-gray-400" : "text-gray-500"
                          }`}
                        >
                          يتم جلب البيانات من OpenStreetMap
                        </p>
                      </div>
                    ) : hospitals.length > 0 ? (
                      <div className="h-[120px] flex flex-col">
                        <div className="flex-1 flex flex-wrap gap-2 overflow-y-auto p-1">
                          {hospitals
                            .filter(
                              (hospital) => !removedHospitals.includes(hospital)
                            )
                            .slice(0, showAllHospitals ? 25 : 10)
                            .map((hospital, index) => (
                              <div
                                key={`hospital-${index}`}
                                className={`flex items-center ${
                                  darkMode
                                    ? "bg-gray-700 border-gray-600 text-gray-200"
                                    : "bg-red-50 border-red-200"
                                } rounded-full px-3 py-1 shadow-sm transition-colors duration-300`}
                              >
                                <span className="mr-1">{hospital}</span>
                                <button
                                  type="button"
                                  onClick={() => handleRemoveHospital(hospital)}
                                  className="ml-2 text-red-500 hover:text-red-700 focus:outline-none transition-colors duration-200"
                                >
                                  ×
                                </button>
                              </div>
                            ))}
                        </div>
                        {hospitals.filter(
                          (hospital) => !removedHospitals.includes(hospital)
                        ).length > 10 && (
                          <div className="mt-2 text-center">
                            <button
                              type="button"
                              onClick={() =>
                                setShowAllHospitals(!showAllHospitals)
                              }
                              className="text-indigo-600 hover:text-indigo-800 focus:outline-none transition-colors duration-200"
                            >
                              {showAllHospitals ? "عرض أقل" : "عرض المزيد"}
                            </button>
                          </div>
                        )}
                      </div>
                    ) : (
                      <div className="h-[120px] flex items-center justify-center text-gray-500 italic">
                        لا توجد مستشفيات ضمن نطاق البحث الحالي
                      </div>
                    )}
                  </div>

                  {/* قسم المساجد - مع ارتفاع ثابت - مخفي على الشاشات الصغيرة */}
                  <div className="mb-4 h-[180px] hidden md:block">
                    <div className="mb-2">
                      <h3
                        className={`text-base font-bold ${
                          darkMode ? "text-indigo-300" : "text-indigo-800"
                        } relative inline-block transition-colors duration-300`}
                      >
                        <span className="relative z-10">
                          المساجد ضمن نطاق العمل:
                        </span>
                        <span
                          className={`absolute bottom-0 left-0 right-0 h-1 ${
                            darkMode ? "bg-indigo-500" : "bg-indigo-300"
                          } opacity-40 transform -rotate-1 z-0`}
                        ></span>
                      </h3>
                    </div>

                    {isStreetsLoading ? (
                      // مؤشر التحميل
                      <div className="h-[120px] flex flex-col items-center justify-center">
                        <div className="w-8 h-8 border-4 border-indigo-500 border-t-transparent rounded-full animate-spin mb-2"></div>
                        <p
                          className={`text-sm ${
                            darkMode ? "text-gray-300" : "text-gray-600"
                          }`}
                        >
                          جاري تحميل المساجد...
                        </p>
                        <p
                          className={`text-xs mt-1 ${
                            darkMode ? "text-gray-400" : "text-gray-500"
                          }`}
                        >
                          يتم جلب البيانات من OpenStreetMap
                        </p>
                      </div>
                    ) : mosques.length > 0 ? (
                      <div className="h-[120px] flex flex-col">
                        <div className="flex-1 flex flex-wrap gap-2 overflow-y-auto p-1">
                          {mosques
                            .filter(
                              (mosque) => !removedMosques.includes(mosque)
                            )
                            .slice(0, showAllMosques ? 25 : 10)
                            .map((mosque, index) => (
                              <div
                                key={`mosque-${index}`}
                                className={`flex items-center ${
                                  darkMode
                                    ? "bg-gray-700 border-gray-600 text-gray-200"
                                    : "bg-green-50 border-green-200"
                                } rounded-full px-3 py-1 shadow-sm transition-colors duration-300`}
                              >
                                <span className="mr-1">{mosque}</span>
                                <button
                                  type="button"
                                  onClick={() => handleRemoveMosque(mosque)}
                                  className="ml-2 text-red-500 hover:text-red-700 focus:outline-none transition-colors duration-200"
                                >
                                  ×
                                </button>
                              </div>
                            ))}
                        </div>
                        {mosques.filter(
                          (mosque) => !removedMosques.includes(mosque)
                        ).length > 10 && (
                          <div className="mt-2 text-center">
                            <button
                              type="button"
                              onClick={() => setShowAllMosques(!showAllMosques)}
                              className="text-indigo-600 hover:text-indigo-800 focus:outline-none transition-colors duration-200"
                            >
                              {showAllMosques ? "عرض أقل" : "عرض المزيد"}
                            </button>
                          </div>
                        )}
                      </div>
                    ) : (
                      <div className="h-[120px] flex items-center justify-center text-gray-500 italic">
                        لا توجد مساجد ضمن نطاق البحث الحالي
                      </div>
                    )}
                  </div>

                  {/* إخفاء زر تطبيق الفلاتر وعرض القائمة إذا لم يكن المستخدم مسجل الدخول */}
                  {isTokenExist && (
                    <Button
                      type="submit"
                      variant="primary"
                      fullWidth
                      onClick={(e) => {
                        // تنفيذ البحث أولاً
                        handleSearch(e);
                        // تعيين حالة التفاعل إلى true عند الضغط على زر التطبيق
                        setHasInteracted(true);
                        // ثم تغيير وضع العرض إلى القائمة
                        setViewMode("list");
                      }}
                      className={`text-white flex items-center justify-center gap-2 shadow-md hover:shadow-lg transition-all duration-200  relative overflow-hidden group py-3 px-4 ${
                        darkMode
                          ? "bg-gradient-to-r from-indigo-700 to-purple-800 hover:from-indigo-800 hover:to-purple-900"
                          : "bg-gradient-to-r from-blue-500 to-indigo-500 hover:from-blue-600 hover:to-indigo-600"
                      }`}
                    >
                      <span className="relative z-10 flex items-center justify-center gap-2">
                        <Filter size={18} className="ml-1" />
                        تطبيق الفلاتر وعرض القائمة
                      </span>
                      <span className="absolute inset-0 bg-white opacity-20 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700"></span>
                    </Button>
                  )}
                </form>
              </Card>
            </div>

            {/* Results */}
            <div className="md:w-2/3">
              <div className="flex justify-between items-center mb-6">
                <div className="flex items-center">
                  {(viewMode === "map" || hasInteracted) && (
                    <div className="flex items-center bg-blue-50 text-blue-700 py-1.5 px-3 rounded-lg shadow-sm">
                      <span className="font-bold ml-1">
                        {viewMode === "map"
                          ? mapFilteredCraftsmen.length
                          : searchFilteredCraftsmen.length}
                      </span>
                      <span className="font-medium">نتيجة</span>
                    </div>
                  )}
                </div>

                <div className="flex items-center bg-gray-100 p-1 rounded-lg shadow-sm">
                  {/* إخفاء زر عرض القائمة إذا لم يكن المستخدم مسجل الدخول */}
                  {isTokenExist && (
                    <div className="relative">
                      <button
                        id="list-view-button"
                        ref={listButtonRef}
                        onClick={() => setViewMode("list")}
                        className={`p-2 rounded-lg flex items-center justify-center ${
                          viewMode === "list"
                            ? "bg-gradient-to-r from-blue-500 to-indigo-600 text-white shadow-md"
                            : "bg-transparent text-gray-700 hover:bg-gray-200"
                        } transition-all duration-200 min-w-[40px]`}
                      >
                        <List size={20} />
                      </button>

                      {/* تنويه زر القائمة */}
                      {showListTooltip && (
                        <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 z-50 whitespace-nowrap">
                          <div
                            className={`relative px-2 py-1 rounded-md shadow-md text-[10px] font-medium
                            ${
                              darkMode
                                ? "bg-blue-600/95 text-white"
                                : "bg-blue-100 text-blue-700"
                            }
                            border ${
                              darkMode ? "border-blue-700" : "border-blue-200"
                            }`}
                          >
                            <span>عرض الحرفيين كقائمة</span>

                            {/* سهم صغير يشير إلى زر القائمة */}
                            <div
                              className={`absolute -bottom-1.5 left-1/2 transform -translate-x-1/2 w-2.5 h-2.5 rotate-45
                              ${
                                darkMode
                                  ? "bg-blue-600 border-r border-b border-blue-700"
                                  : "bg-blue-100 border-r border-b border-blue-200"
                              }`}
                            ></div>
                          </div>
                        </div>
                      )}
                    </div>
                  )}

                  <button
                    id="map-view-button"
                    onClick={() => setViewMode("map")}
                    className={`p-2 rounded-lg flex items-center justify-center ${
                      viewMode === "map"
                        ? "bg-gradient-to-r from-blue-500 to-indigo-600 text-white shadow-md"
                        : "bg-transparent text-gray-700 hover:bg-gray-200"
                    } transition-all duration-200 min-w-[40px] ${
                      isTokenExist ? "mr-1" : ""
                    }`}
                  >
                    <MapIcon size={20} />
                  </button>
                </div>
              </div>

              {viewMode === "list" ? (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {!hasInteracted ? (
                    // إذا لم يتفاعل المستخدم بعد، عرض رسالة توجيهية
                    <div className="col-span-2">
                      <Card
                        className={`p-6 text-center ${
                          darkMode
                            ? "bg-gray-800 text-gray-200"
                            : "bg-gradient-to-br from-blue-50 to-indigo-100"
                        } shadow-md transition-colors duration-300`}
                      >
                        <p
                          className={`${
                            darkMode ? "text-gray-400" : "text-gray-600"
                          } mb-4 transition-colors duration-300`}
                        >
                          لم يتم تحديد معايير البحث بعد. يرجى تحريك المؤشر أو
                          تعديل النطاق أو اضغط على زر "تطبيق الفلاتر وعرض
                          القائمة" لعرض النتائج
                        </p>
                      </Card>
                    </div>
                  ) : searchFilteredCraftsmen.length > 0 ? (
                    searchFilteredCraftsmen.map((craftsman) => (
                      <motion.div
                        key={craftsman.id || craftsman._id}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.3 }}
                      >
                        <Card
                          className={`overflow-hidden hover:shadow-lg transition-all duration-300 ${
                            darkMode
                              ? "bg-gray-800 text-gray-200 border border-gray-700"
                              : "bg-white border border-indigo-100/20"
                          } shadow-md rounded-xl`}
                        >
                          {/* تصميم جديد محسن مشابه للصورة المرفقة */}
                          <div className="p-4">
                            {/* الجزء العلوي: مؤشر الإتاحة والمهنة */}
                            <div className="flex justify-between items-center mb-3">
                              {/* المهنة مع أيقونة */}
                              <div className="flex items-center gap-2">
                                <div
                                  className={`p-1.5 rounded-md ${
                                    darkMode
                                      ? "bg-indigo-900/50"
                                      : "bg-indigo-100"
                                  }`}
                                >
                                  <Briefcase
                                    size={16}
                                    className={`${
                                      darkMode
                                        ? "text-indigo-300"
                                        : "text-indigo-700"
                                    }`}
                                  />
                                </div>
                                <span
                                  className={`font-semibold text-sm ${
                                    darkMode
                                      ? "text-indigo-300"
                                      : "text-indigo-700"
                                  }`}
                                >
                                  {craftsman.profession ||
                                    (craftsman.professions &&
                                      craftsman.professions[0]) ||
                                    "حرفي"}
                                </span>
                              </div>

                              {/* مؤشر الإتاحة - مرتبط بحالة الإتاحة والساعات */}
                              <div>
                                <span
                                  className={`px-3 py-1 rounded-full text-xs font-bold ${
                                    isAvailableNow(craftsman.workingHoursArray)
                                      ? "bg-green-500 text-white"
                                      : "bg-red-500 text-white"
                                  }`}
                                >
                                  {isAvailableNow(craftsman.workingHoursArray)
                                    ? "متاح"
                                    : "غير متاح"}
                                </span>
                              </div>
                            </div>

                            {/* صورة الحرفي والمعلومات الأساسية */}
                            <div className="flex items-center gap-4 mb-4">
                              {/* صورة الحرفي */}
                              <div className="relative w-[70px] h-[70px] flex-shrink-0">
                                <div className="absolute inset-0 rounded-full bg-gradient-to-br from-blue-500 to-indigo-600 shadow-lg"></div>
                                <div className="absolute inset-[2px] rounded-full overflow-hidden border-2 border-white/40">
                                  <SimpleLazyImage
                                    src={
                                      craftsman.profilePicture ||
                                      craftsman.image ||
                                      (craftsman.user &&
                                        (craftsman.user.profilePicture ||
                                          craftsman.user.image)) ||
                                      "/img/default-avatar-2-modified.svg"
                                    }
                                    alt={craftsman.name || "حرفي"}
                                    className="w-full h-full object-cover"
                                  />
                                </div>
                              </div>

                              {/* معلومات الحرفي */}
                              <div className="flex-1">
                                {/* اسم الحرفي */}
                                <h3
                                  className={`text-lg font-bold ${
                                    darkMode ? "text-white" : "text-gray-800"
                                  }`}
                                >
                                  {craftsman.name}
                                </h3>

                                {/* التقييم والتخصصات */}
                                <div className="flex flex-wrap items-center gap-3 mt-1">
                                  <div className="flex items-center">
                                    <Star
                                      size={16}
                                      className="text-yellow-500 fill-current ml-1"
                                    />
                                    <span className="text-sm font-bold">
                                      {craftsman.rating
                                        ? craftsman.rating.toFixed(1)
                                        : "5.0"}
                                    </span>
                                  </div>

                                  {/* التخصصات */}
                                  <div className="text-sm text-gray-600 truncate max-w-[180px]">
                                    {craftsman.specializations &&
                                    craftsman.specializations.length > 0
                                      ? craftsman.specializations
                                          .slice(0, 2)
                                          .join(" • ")
                                      : craftsman.specialization || ""}
                                    {craftsman.specializations &&
                                      craftsman.specializations.length > 2 &&
                                      "..."}
                                  </div>
                                </div>

                                {/* نطاق العمل */}
                                <div className="flex items-center mt-2">
                                  <MapPin
                                    size={14}
                                    className="text-indigo-600 flex-shrink-0 ml-1"
                                  />
                                  <span className="text-sm text-gray-600">
                                    نطاق العمل: {craftsman.workRadius || 1} كم
                                  </span>
                                </div>
                              </div>
                            </div>

                            {/* زر عرض الملف */}
                            <Button
                              variant="primary"
                              className="w-full bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white flex items-center justify-center gap-2 py-2.5 px-4 rounded-lg transition-all duration-300 shadow-md hover:shadow-lg"
                              onClick={() => {
                                // استخدام المعرف الصحيح (id أو _id) للتنقل إلى صفحة الملف الشخصي
                                const craftsmanId =
                                  craftsman._id || craftsman.id;
                                console.log(
                                  "التنقل إلى ملف الحرفي بالمعرف:",
                                  craftsmanId
                                );
                                navigate(`/profile/craftsman/${craftsmanId}`, {
                                  replace: true,
                                });
                              }}
                            >
                              <span className="font-bold">عرض الملف</span>
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                className="h-5 w-5 mr-1"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke="currentColor"
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth={2}
                                  d="M15 19l-7-7 7-7"
                                />
                              </svg>
                            </Button>
                          </div>
                        </Card>
                      </motion.div>
                    ))
                  ) : (
                    <div className="col-span-2">
                      <Card
                        className={`p-6 text-center ${
                          darkMode
                            ? "bg-gray-800 text-gray-200"
                            : "bg-gradient-to-br from-blue-50 to-indigo-100"
                        } shadow-md transition-colors duration-300`}
                      >
                        <p
                          className={`${
                            darkMode ? "text-gray-400" : "text-gray-600"
                          } mb-4 transition-colors duration-300`}
                        >
                          لم يتم العثور على نتائج
                        </p>
                        <Button
                          variant="secondary"
                          onClick={handleResetFilters}
                          className={`text-white flex items-center justify-center gap-2 shadow-md hover:shadow-lg transition-all duration-200  relative overflow-hidden group py-2 px-4 ${
                            darkMode
                              ? "bg-gradient-to-r from-indigo-700 to-purple-800 hover:from-indigo-800 hover:to-purple-900"
                              : "bg-gradient-to-r from-blue-500 to-indigo-500 hover:from-blue-600 hover:to-indigo-600"
                          }`}
                        >
                          <span className="relative z-10 flex items-center justify-center gap-2">
                            إعادة ضبط الفلاتر
                          </span>
                          <span className="absolute inset-0 bg-white opacity-20 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700"></span>
                        </Button>
                      </Card>
                    </div>
                  )}
                </div>
              ) : (
                <div className="flex flex-col gap-4">
                  {/* فلتر الخريطة المتقدم */}
                  <div className="flex flex-col gap-2">
                    <MapFilter
                      professions={professions.map((p) => p.name)}
                      selectedProfessions={selectedProfessions}
                      onProfessionChange={handleProfessionChange}
                      selectedRating={selectedRating}
                      onRatingChange={handleRatingChange}
                      searchRadius={filters.radius}
                      onRadiusChange={(radius) => {
                        console.log(
                          "تغيير نطاق العمل من فلتر الخريطة المتقدمة:",
                          radius
                        );
                        handleRadiusChange(radius);
                      }}
                    />
                  </div>

                  {/* الخريطة المتقدمة */}
                  <Card
                    className={`overflow-hidden ${
                      darkMode
                        ? "bg-gray-800 text-gray-200"
                        : "bg-gradient-to-br from-blue-50 to-indigo-100"
                    } shadow-md transition-colors duration-300`}
                  >
                    <div className="h-[600px]">
                      <AdvancedMap
                        craftsmen={filteredCraftsmen}
                        selectedProfessions={selectedProfessions}
                        selectedRating={selectedRating}
                        userLocation={
                          filters.location
                            ? {
                                latitude: filters.location.lat,
                                longitude: filters.location.lng,
                              }
                            : null
                        }
                        searchRadius={filters.radius * 1000} // تحويل من كم إلى متر
                        onFilteredCraftsmenChange={setMapFilteredCraftsmen}
                        onCraftsmanSelect={() => {
                          // لا نحتاج للقيام بأي شيء هنا لأن الزر يستخدم الآن مكون Link
                          // الذي سيتعامل مع التنقل بشكل صحيح
                        }}
                        showOnlyAvailable={filters.available}
                      />
                    </div>
                  </Card>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default SearchPage;
