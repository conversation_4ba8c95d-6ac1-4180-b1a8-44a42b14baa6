# 📱📧 نظام التحقق المزدوج - البريد الإلكتروني ورقم الهاتف

## 🎯 **نظرة عامة**

تم تطوير نظام تحقق مرن يسمح للمستخدمين باختيار طريقة التحقق المفضلة لديهم:
- **التحقق بالبريد الإلكتروني** (عبر Supabase)
- **التحقق برقم الهاتف** (عبر رمز SMS)

## ✨ **المزايا الجديدة**

### **🔄 مرونة في الاختيار**
- المستخدم يختار طريقة التحقق المناسبة له
- لا حاجة لإدخال كلا الطريقتين
- تجربة مستخدم محسنة ومرنة

### **📱 التحقق برقم الهاتف**
- إرسال رمز مكون من 6 أرقام
- التحقق الفوري من الرمز
- حفظ حالة التحقق تلقائياً

### **📧 التحقق بالبريد الإلكتروني**
- رابط تحقق آمن عبر Supabase
- التحقق في نفس الصفحة
- حفظ البيانات واستعادتها

## 🛠️ **التطبيق التقني**

### **1. واجهة المستخدم**

#### **أ. اختيار نوع التحقق**:
```jsx
<div className="flex gap-4 mb-4">
  <label className="flex items-center cursor-pointer">
    <input
      type="radio"
      name="verificationType"
      value="email"
      checked={verificationType === 'email'}
      onChange={(e) => setVerificationType(e.target.value)}
    />
    <span>البريد الإلكتروني</span>
  </label>
  <label className="flex items-center cursor-pointer">
    <input
      type="radio"
      name="verificationType"
      value="phone"
      checked={verificationType === 'phone'}
      onChange={(e) => setVerificationType(e.target.value)}
    />
    <span>رقم الهاتف</span>
  </label>
</div>
```

#### **ب. واجهة التحقق برقم الهاتف**:
```jsx
{verificationType === 'phone' && (
  <div className="mb-4">
    {/* إرسال رمز التحقق */}
    <LoadingButton
      onClick={sendPhoneVerificationCode}
      isLoading={phoneVerification.sending}
      disabled={!formData.phone}
    >
      إرسال رمز التحقق
    </LoadingButton>
    
    {/* إدخال رمز التحقق */}
    {phoneVerification.sent && !phoneVerification.verified && (
      <div className="flex items-center gap-2">
        <input
          type="text"
          placeholder="أدخل رمز التحقق (6 أرقام)"
          value={phoneVerification.otp}
          onChange={(e) => {
            const value = e.target.value.replace(/\D/g, '').slice(0, 6);
            setPhoneVerification(prev => ({
              ...prev,
              otp: value
            }));
          }}
          maxLength="6"
        />
        <LoadingButton
          onClick={verifyPhoneCode}
          isLoading={phoneVerification.verifying}
          disabled={phoneVerification.otp.length !== 6}
        >
          تحقق
        </LoadingButton>
      </div>
    )}
  </div>
)}
```

### **2. إدارة الحالة**

#### **أ. حالات التحقق**:
```jsx
// حالة التحقق من البريد الإلكتروني
const [emailVerification, setEmailVerification] = useState({
  sent: false,
  verified: false,
  sending: false,
  firebaseUser: null,
  checkingStatus: false,
});

// حالة التحقق من رقم الهاتف
const [phoneVerification, setPhoneVerification] = useState({
  sent: false,
  verified: false,
  sending: false,
  otp: '',
  verifying: false,
});

// نوع التحقق المختار
const [verificationType, setVerificationType] = useState('email');
```

### **3. دوال التحقق**

#### **أ. إرسال رمز التحقق للهاتف**:
```jsx
const sendPhoneVerificationCode = async () => {
  if (!formData.phone) {
    showToast("يرجى إدخال رقم الهاتف أولاً", "error");
    return;
  }

  setPhoneVerification(prev => ({ ...prev, sending: true }));

  try {
    const response = await otpService.sendOtpToPhone(formData.phone);
    
    if (response.success) {
      setPhoneVerification(prev => ({
        ...prev,
        sent: true,
        sending: false,
      }));
      showToast("تم إرسال رمز التحقق إلى رقم هاتفك", "success");
    }
  } catch (error) {
    setPhoneVerification(prev => ({ ...prev, sending: false }));
    showToast(error.message || "حدث خطأ أثناء إرسال رمز التحقق", "error");
  }
};
```

#### **ب. التحقق من رمز الهاتف**:
```jsx
const verifyPhoneCode = async () => {
  if (!phoneVerification.otp || phoneVerification.otp.length !== 6) {
    showToast("يرجى إدخال رمز التحقق المكون من 6 أرقام", "error");
    return;
  }

  setPhoneVerification(prev => ({ ...prev, verifying: true }));

  try {
    const response = await otpService.verifyOtp(formData.phone, phoneVerification.otp);
    
    if (response.success) {
      setPhoneVerification(prev => ({
        ...prev,
        verified: true,
        verifying: false,
      }));
      showToast("تم التحقق من رقم الهاتف بنجاح!", "success");
    }
  } catch (error) {
    setPhoneVerification(prev => ({ ...prev, verifying: false }));
    showToast(error.message || "رمز التحقق غير صحيح", "error");
  }
};
```

### **4. التحقق من صحة البيانات**

#### **التحقق المرن حسب النوع المختار**:
```jsx
const validateStep1 = () => {
  // التحقق من البريد الإلكتروني أو رقم الهاتف (واحد منهما على الأقل)
  const hasEmail = formData.email && formData.email.trim();
  const hasPhone = formData.phone && formData.phone.trim();
  
  if (!hasEmail && !hasPhone) {
    newErrors.email = "يجب إدخال البريد الإلكتروني أو رقم الهاتف للتحقق";
    isValid = false;
  } else {
    // إذا تم اختيار التحقق بالبريد الإلكتروني
    if (verificationType === 'email') {
      if (!hasEmail) {
        newErrors.email = "يرجى إدخال البريد الإلكتروني للتحقق";
        isValid = false;
      } else if (!emailVerification.verified) {
        newErrors.email = "يرجى التحقق من البريد الإلكتروني أولاً";
        isValid = false;
      }
    }
    
    // إذا تم اختيار التحقق برقم الهاتف
    if (verificationType === 'phone') {
      if (!hasPhone) {
        newErrors.phone = "يرجى إدخال رقم الهاتف للتحقق";
        isValid = false;
      } else if (!phoneVerification.verified) {
        newErrors.phone = "يرجى التحقق من رقم الهاتف أولاً";
        isValid = false;
      }
    }
  }
  
  return isValid;
};
```

### **5. حفظ واستعادة البيانات**

#### **حفظ شامل للحالات**:
```jsx
const formDataToSave = {
  ...formData,
  verificationType: verificationType,
  emailVerification: emailVerification,
  phoneVerification: phoneVerification,
  timestamp: Date.now(),
};
localStorage.setItem('craftsmanRegistrationData', JSON.stringify(formDataToSave));
```

#### **استعادة ذكية للحالات**:
```jsx
const { 
  verificationType: savedVerificationType,
  emailVerification: savedEmailVerification,
  phoneVerification: savedPhoneVerification,
  ...formDataOnly 
} = parsedData;

// استعادة نوع التحقق
if (savedVerificationType) {
  setVerificationType(savedVerificationType);
}

// استعادة حالة التحقق من البريد الإلكتروني
if (savedEmailVerification) {
  setEmailVerification(prev => ({
    ...prev,
    ...savedEmailVerification,
  }));
}

// استعادة حالة التحقق من الهاتف
if (savedPhoneVerification) {
  setPhoneVerification(prev => ({
    ...prev,
    ...savedPhoneVerification,
    otp: '', // لا نحفظ رمز التحقق لأسباب أمنية
  }));
}
```

## 🎯 **تدفق العمل**

### **✅ التحقق بالبريد الإلكتروني**:
```
المستخدم يختار "البريد الإلكتروني"
↓
يدخل البريد الإلكتروني
↓
يضغط "إرسال رابط التحقق"
↓
يتم إنشاء حساب مؤقت في Supabase
↓
يتم إرسال رابط التحقق
↓
المستخدم ينقر على الرابط
↓
يتم التحقق في نفس الصفحة
↓
يمكن المتابعة للخطوة التالية
```

### **✅ التحقق برقم الهاتف**:
```
المستخدم يختار "رقم الهاتف"
↓
يضغط "إرسال رمز التحقق"
↓
يتم إرسال رمز مكون من 6 أرقام
↓
المستخدم يدخل الرمز
↓
يضغط "تحقق"
↓
يتم التحقق من الرمز
↓
يمكن المتابعة للخطوة التالية
```

## 🚀 **النتيجة النهائية**

### **✅ مزايا النظام الجديد**:

1. **مرونة كاملة** - اختيار طريقة التحقق المناسبة
2. **تجربة محسنة** - واجهة بديهية وسهلة الاستخدام
3. **حفظ ذكي** - استعادة تلقائية لجميع الحالات
4. **أمان عالي** - عدم حفظ أرقام التحقق الحساسة
5. **تكامل سلس** - يعمل مع النظام الحالي بدون مشاكل
6. **دعم كامل** - للبريد الإلكتروني ورقم الهاتف
7. **رسائل واضحة** - إشعارات مفيدة ومفهومة
8. **تحقق موثوق** - باستخدام خدمات مختبرة

الآن المستخدمون لديهم حرية الاختيار في طريقة التحقق! 🎉
