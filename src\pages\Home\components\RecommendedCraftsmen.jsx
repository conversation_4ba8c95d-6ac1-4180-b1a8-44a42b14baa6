import React from "react";
import { Link } from "react-router-dom";
import { motion } from "framer-motion";
import { Star, MapPin, Briefcase, Award } from "lucide-react";
import Card from "../../../components/common/Card";
import Button from "../../../components/common/Button";
import SimpleLazyImage from "../../../components/common/SimpleLazyImage";
import useThemeStore from "../../../store/themeStore";

const RecommendedCraftsmen = ({ isLoadingCraftsmen, recommendedCraftsmen, userType }) => {
  const darkMode = useThemeStore((state) => state.darkMode);

  if (userType !== "client") {
    return null;
  }

  return (
    <section className="mb-8">
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center">
          <Award
            size={24}
            className={`ml-2 ${darkMode ? "text-indigo-400" : "text-indigo-600"}`}
          />
          <h2
            className={`text-2xl font-bold ${
              darkMode ? "text-indigo-300" : "text-indigo-800"
            } relative inline-block transition-colors duration-300`}
          >
            <span className="relative z-10">حرفيون موصى بهم</span>
            <span
              className={`absolute bottom-0 left-0 right-0 h-2 ${
                darkMode ? "bg-indigo-500" : "bg-indigo-300"
              } opacity-40 transform -rotate-1 z-0`}
            ></span>
          </h2>
        </div>
        <Link
          to="/search"
          className={`${
            darkMode
              ? "text-indigo-400 hover:text-indigo-300"
              : "text-indigo-600 hover:text-indigo-800"
          } font-medium transition-colors duration-200 flex items-center`}
        >
          عرض المزيد
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-4 w-4 mr-1"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M15 19l-7-7 7-7"
            />
          </svg>
        </Link>
      </div>

      {isLoadingCraftsmen ? (
        <Card
          className={`p-8 text-center ${
            darkMode
              ? "bg-gray-800/90 text-gray-200 border border-gray-700"
              : "bg-white/90 border border-indigo-100/60"
          } shadow-lg transition-colors duration-300 w-full rounded-xl backdrop-blur-sm`}
        >
          <div className="flex flex-col justify-center items-center py-6">
            {/* Spinner animado mejorado */}
            <div className="relative w-16 h-16 mb-4">
              {/* Círculo exterior */}
              <div className={`absolute inset-0 rounded-full ${
                darkMode ? "border-2 border-indigo-700" : "border-2 border-indigo-200"
              }`}></div>

              {/* Spinner animado */}
              <div className={`absolute inset-0 rounded-full border-2 border-t-transparent border-l-transparent ${
                darkMode ? "border-r-indigo-400 border-b-indigo-500" : "border-r-indigo-500 border-b-indigo-600"
              } animate-spin`}></div>

              {/* Punto central */}
              <div className={`absolute inset-0 m-auto w-2 h-2 rounded-full ${
                darkMode ? "bg-indigo-400" : "bg-indigo-500"
              }`}></div>
            </div>

            <p className={`${
              darkMode ? "text-indigo-300" : "text-indigo-700"
            } font-medium text-lg transition-colors duration-300`}>
              جاري تحميل الحرفيين...
            </p>
            <p className={`${
              darkMode ? "text-gray-400" : "text-gray-500"
            } text-sm mt-2 transition-colors duration-300 max-w-md`}>
              نحن نبحث عن أفضل الحرفيين المناسبين لك
            </p>
          </div>
        </Card>
      ) : recommendedCraftsmen.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {recommendedCraftsmen.map((craftsman, index) => (
            <motion.div
              key={craftsman.id || craftsman._id}
              whileHover={{ y: -5, boxShadow: "0 10px 25px -5px rgba(66, 56, 200, 0.1)" }}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
            >
              <Card
                className={`overflow-hidden ${
                  darkMode
                    ? "bg-gray-800 text-gray-200 border border-gray-700"
                    : "bg-white/95 border border-indigo-100/60"
                } shadow-lg hover:shadow-xl transition-all duration-300 rounded-xl backdrop-blur-sm ${
                  !darkMode && "hover:bg-gradient-to-b hover:from-white hover:to-indigo-50/30"
                }`}
              >
                <div className="relative h-36 overflow-hidden">
                  {/* Fondo con gradiente mejorado */}
                  <div className={`absolute inset-0 ${
                    darkMode
                      ? "bg-gradient-to-br from-indigo-800 via-indigo-900 to-purple-900"
                      : "bg-gradient-to-br from-blue-400 via-indigo-500 to-blue-600"
                  }`}></div>

                  {/* Patrón decorativo */}
                  <div className="absolute inset-0 opacity-10">
                    <div className="absolute top-0 left-0 w-full h-full"
                         style={{
                           backgroundImage: 'url("data:image/svg+xml,%3Csvg width=\'100\' height=\'100\' viewBox=\'0 0 100 100\' xmlns=\'http://www.w3.org/2000/svg\'%3E%3Cpath d=\'M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z\' fill=\'%23ffffff\' fill-opacity=\'0.1\' fill-rule=\'evenodd\'/%3E%3C/svg%3E")',
                           backgroundSize: '180px 180px'
                         }}>
                    </div>
                  </div>

                  {/* Efectos de luz */}
                  <div className="absolute -bottom-16 -left-16 w-48 h-48 rounded-full bg-white opacity-10 blur-xl"></div>
                  <div className="absolute -top-16 -right-16 w-48 h-48 rounded-full bg-white opacity-10 blur-xl"></div>

                  {/* Línea decorativa */}
                  <div className="absolute bottom-6 left-0 right-0 h-px bg-white opacity-20"></div>

                  {/* Etiqueta "جديد" */}
                  {!craftsman.rating && (
                    <div className="absolute top-4 right-4 bg-yellow-400 text-gray-900 text-xs font-bold px-2 py-1 rounded-full shadow-md transform rotate-3 z-10">
                      جديد
                    </div>
                  )}
                </div>

                <div className="relative px-5 pt-0 pb-5">
                  <div className="flex justify-between -mt-20 mb-6">
                    {/* Imagen de perfil mejorada */}
                    <div className="relative">
                      <div className="w-28 h-28 rounded-full overflow-hidden border-4 border-white dark:border-gray-800 shadow-lg relative z-10">
                        <SimpleLazyImage
                          src={
                            craftsman.profilePicture ||
                            craftsman.image ||
                            (craftsman.user && (craftsman.user.profilePicture || craftsman.user.image)) ||
                            "/img/default-avatar-2-modified.svg"
                          }
                          alt={
                            craftsman.name ||
                            (craftsman.user && craftsman.user.name) ||
                            "Craftsman"
                          }
                          className="w-full h-full object-cover"
                        />
                      </div>
                      {/* Efecto de resplandor detrás de la imagen */}
                      <div className="absolute -inset-1 rounded-full bg-white dark:bg-indigo-900/30 blur-sm opacity-70 z-0"></div>
                    </div>

                    {/* Calificación mejorada */}
                    <div className={`flex items-center justify-center h-9 px-4 mt-4 rounded-full shadow-md ${
                      darkMode
                        ? "bg-indigo-800/80 text-indigo-100 border border-indigo-700"
                        : "bg-white text-indigo-700 border border-indigo-100"
                    } backdrop-blur-sm`}>
                      <div className="relative">
                        <Star
                          size={16}
                          className={`${
                            darkMode ? "text-yellow-400" : "text-yellow-500"
                          } ml-1 fill-current`}
                        />
                        {/* Efecto de resplandor para la estrella */}
                        <div className="absolute inset-0 text-yellow-400 fill-current blur-sm opacity-70"></div>
                      </div>
                      <span className="text-sm font-bold">
                        {craftsman.rating ? craftsman.rating.toFixed(1) : "5.0"}{" "}
                        {craftsman.totalRatings
                          ? `(${craftsman.totalRatings})`
                          : "(1)"}
                      </span>
                    </div>
                  </div>

                  {/* Nombre del craftsman con efecto de subrayado */}
                  <div className="relative mb-4">
                    <h3
                      className={`text-xl font-bold ${
                        darkMode ? "text-indigo-300" : "text-indigo-700"
                      } transition-colors duration-300 inline-block`}
                    >
                      {craftsman.name ||
                        (craftsman.user && craftsman.user.name) ||
                        ""}
                    </h3>
                    <div className={`absolute -bottom-1 left-0 h-0.5 w-16 ${
                      darkMode ? "bg-indigo-500" : "bg-indigo-400"
                    } rounded-full`}></div>
                  </div>

                  {/* Profesión con icono mejorado */}
                  <div className={`flex items-center mb-3 p-2 rounded-lg ${
                    darkMode ? "bg-gray-800/50" : "bg-indigo-50/50"
                  }`}>
                    <div className={`p-1.5 rounded-md mr-2 ${
                      darkMode ? "bg-indigo-900/50" : "bg-indigo-100"
                    }`}>
                      <Briefcase
                        size={16}
                        className={`${
                          darkMode ? "text-indigo-300" : "text-indigo-600"
                        } flex-shrink-0`}
                      />
                    </div>
                    <p
                      className={`${
                        darkMode ? "text-indigo-300" : "text-indigo-700"
                      } text-sm font-medium transition-colors duration-300 mr-2`}
                    >
                      {craftsman.professions && craftsman.professions[0]}
                      {craftsman.specializations &&
                        craftsman.specializations[0] &&
                        ` - ${craftsman.specializations[0]}`}
                    </p>
                  </div>

                  {/* Ubicación con icono mejorado */}
                  {craftsman.location && craftsman.location.address && (
                    <div className={`flex items-start mb-5 p-2 rounded-lg ${
                      darkMode ? "bg-gray-800/50" : "bg-indigo-50/50"
                    }`}>
                      <div className={`p-1.5 rounded-md mr-2 ${
                        darkMode ? "bg-indigo-900/50" : "bg-indigo-100"
                      }`}>
                        <MapPin
                          size={16}
                          className={`${
                            darkMode ? "text-indigo-300" : "text-indigo-600"
                          } flex-shrink-0`}
                        />
                      </div>
                      <p
                        className={`${
                          darkMode ? "text-gray-300" : "text-gray-700"
                        } text-sm transition-colors duration-300 line-clamp-2`}
                      >
                        {craftsman.location.address}
                      </p>
                    </div>
                  )}

                  {/* Botón mejorado */}
                  <Link
                    to={`/profile/craftsman/${craftsman.id || craftsman._id}`}
                    className="block w-full mt-4"
                  >
                    <Button
                      variant="primary"
                      className={`w-full text-white flex items-center justify-center gap-2 shadow-lg hover:shadow-xl transition-all duration-300 relative overflow-hidden group py-3 px-4 rounded-lg ${
                        darkMode
                          ? "bg-gradient-to-r from-indigo-600 to-purple-700 hover:from-indigo-700 hover:to-purple-800"
                          : "bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700"
                      }`}
                    >
                      <span className="relative z-10 font-medium">عرض الملف</span>

                      {/* Icono de flecha con animación */}
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-5 w-5 mr-1 relative z-10 transition-transform duration-300 group-hover:-translate-x-1"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M15 19l-7-7 7-7"
                        />
                      </svg>

                      {/* Efecto de brillo al pasar el cursor */}
                      <span className="absolute inset-0 bg-white opacity-20 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700"></span>
                    </Button>
                  </Link>
                </div>
              </Card>
            </motion.div>
          ))}
        </div>
      ) : (
        <Card
          className={`p-8 text-center ${
            darkMode
              ? "bg-gray-800/90 text-gray-200 border border-gray-700"
              : "bg-white/90 border border-indigo-100/60"
          } shadow-lg transition-colors duration-300 rounded-xl backdrop-blur-sm`}
        >
          {/* Ilustración mejorada */}
          <div className="flex justify-center mb-6">
            <div className={`w-24 h-24 rounded-full flex items-center justify-center ${
              darkMode ? "bg-gray-700" : "bg-indigo-50"
            }`}>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
                className={`w-12 h-12 ${
                  darkMode ? "text-indigo-400" : "text-indigo-500"
                }`}
              >
                <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                <circle cx="9" cy="7" r="4"></circle>
                <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
                <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
              </svg>
            </div>
          </div>

          <h3 className={`text-xl font-bold mb-2 ${
            darkMode ? "text-indigo-300" : "text-indigo-700"
          }`}>
            لا يوجد حرفيين موصى بهم حالياً
          </h3>

          <p className={`${
            darkMode ? "text-gray-400" : "text-gray-600"
          } mb-6 transition-colors duration-300 max-w-md mx-auto`}>
            يمكنك البحث عن حرفيين في منطقتك واستكشاف الخدمات المتاحة
          </p>

          <Link to="/search" className="inline-block">
            <Button
              variant="primary"
              className={`text-white flex items-center justify-center gap-2 shadow-lg hover:shadow-xl transition-all duration-300 relative overflow-hidden group py-3 px-6 rounded-lg ${
                darkMode
                  ? "bg-gradient-to-r from-indigo-600 to-purple-700 hover:from-indigo-700 hover:to-purple-800"
                  : "bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700"
              }`}
            >
              <span className="relative z-10 font-medium">البحث عن حرفيين</span>

              {/* Icono de búsqueda */}
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5 mr-1 relative z-10"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                />
              </svg>

              {/* Efecto de brillo */}
              <span className="absolute inset-0 bg-white opacity-20 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700"></span>
            </Button>
          </Link>
        </Card>
      )}
    </section>
  );
};

export default RecommendedCraftsmen;
