import axios from "axios";
import { API_URL, BOOKING_STATUS, BOOKING_SETTINGS } from "./config";
import { getAuthToken } from "../utils/auth";

// خدمة إدارة الطلبات
const bookingService = {
  // جلب جميع الطلبات للمستخدم الحالي
  getUserBookings: async () => {
    try {
      const token = getAuthToken();
      const response = await axios.get(`${API_URL}/bookings/user`, {
        headers: { Authorization: `Bearer ${token}` },
      });
      return response.data;
    } catch (error) {
      console.error("Error fetching user bookings:", error);
      throw error;
    }
  },

  // جلب تفاصيل طلب محدد
  getBookingDetails: async (bookingId) => {
    try {
      const token = getAuthToken();
      const response = await axios.get(`${API_URL}/bookings/${bookingId}`, {
        headers: { Authorization: `Bearer ${token}` },
      });
      return response.data;
    } catch (error) {
      console.error("Error fetching booking details:", error);
      throw error;
    }
  },

  // إنشاء طلب جديد
  createBooking: async (bookingData) => {
    try {
      const token = getAuthToken();
      const response = await axios.post(`${API_URL}/bookings`, bookingData, {
        headers: { Authorization: `Bearer ${token}` },
      });
      return response.data;
    } catch (error) {
      console.error("Error creating booking:", error);
      throw error;
    }
  },

  // تحديث حالة الطلب
  updateBookingStatus: async (bookingId, status) => {
    try {
      const token = getAuthToken();
      const response = await axios.patch(
        `${API_URL}/bookings/${bookingId}/status`,
        { status },
        { headers: { Authorization: `Bearer ${token}` } }
      );
      return response.data;
    } catch (error) {
      console.error("Error updating booking status:", error);
      throw error;
    }
  },

  // إلغاء الطلب
  cancelBooking: async (bookingId) => {
    try {
      const token = getAuthToken();
      const response = await axios.patch(
        `${API_URL}/bookings/${bookingId}/status`,
        { status: BOOKING_STATUS.CANCELLED },
        { headers: { Authorization: `Bearer ${token}` } }
      );
      return response.data;
    } catch (error) {
      console.error("Error cancelling booking:", error);
      throw error;
    }
  },

  // تعديل الطلب
  updateBooking: async (bookingId, bookingData) => {
    try {
      const token = getAuthToken();
      const response = await axios.put(
        `${API_URL}/bookings/${bookingId}`,
        bookingData,
        { headers: { Authorization: `Bearer ${token}` } }
      );
      return response.data;
    } catch (error) {
      console.error("Error updating booking:", error);
      throw error;
    }
  },

  // تأكيد الطلب وإرساله للحرفي (تجاوز فترة التعديل)
  confirmBooking: async (bookingId) => {
    try {
      console.log(`تأكيد الطلب وإرساله فورًا: ${bookingId}`);
      const token = getAuthToken();

      // نستخدم نقطة نهاية تحديث الحالة بدلاً من نقطة النهاية المخصصة التي تسبب خطأ 500
      const response = await axios.put(
        `${API_URL}/bookings/${bookingId}/status`,
        {
          status: "pending",
          canEdit: false,
          visibleToCraftsman: true // إضافة معلمة لجعل الطلب مرئي للحرفي
        },
        { headers: { Authorization: `Bearer ${token}` } }
      );

      console.log("استجابة تأكيد الطلب:", response.data);
      return response.data;
    } catch (error) {
      console.error("Error confirming booking:", error);
      console.error("تفاصيل الخطأ:", {
        message: error.message,
        status: error.response?.status,
        data: error.response?.data,
        customMessage: error.customMessage,
      });

      throw error;
    }
  },

  // التحقق مما إذا كان الطلب قابل للتعديل (ضمن فترة التعديل)
  canEditBooking: (booking) => {
    if (!booking) return false;

    // إذا كانت حالة الطلب ليست "مسودة" أو "قيد الانتظار"، فلا يمكن تعديله
    if (booking.status !== BOOKING_STATUS.DRAFT && booking.status !== BOOKING_STATUS.PENDING) {
      return false;
    }

    try {
      // حساب الوقت المنقضي منذ إنشاء الطلب (بالدقائق)
      const createdAt = new Date(booking.createdAt);
      const now = new Date();
      const diffInMinutes = Math.floor((now - createdAt) / (1000 * 60));

      // طباعة معلومات التصحيح
      console.log("التحقق من إمكانية تعديل الطلب:", {
        bookingId: booking.id,
        createdAt: createdAt.toLocaleString(),
        now: now.toLocaleString(),
        diffInMinutes: diffInMinutes,
        editWindowMinutes: BOOKING_SETTINGS.EDIT_WINDOW_MINUTES,
        canEdit: diffInMinutes <= BOOKING_SETTINGS.EDIT_WINDOW_MINUTES
      });

      // يمكن تعديل الطلب إذا كان الوقت المنقضي أقل من أو يساوي فترة التعديل المسموح بها
      return diffInMinutes <= BOOKING_SETTINGS.EDIT_WINDOW_MINUTES;
    } catch (error) {
      console.error("خطأ في التحقق من إمكانية تعديل الطلب:", error);
      return false; // في حالة حدوث خطأ، نفترض أنه لا يمكن تعديل الطلب
    }
  },

  // حساب الوقت المتبقي لتعديل الطلب (بالدقائق)
  getRemainingEditTime: (booking) => {
    if (!booking) return 0;

    try {
      // حساب الوقت المنقضي منذ إنشاء الطلب (بالدقائق)
      const createdAt = new Date(booking.createdAt);
      const now = new Date();
      const diffInMinutes = Math.floor((now - createdAt) / (1000 * 60));

      // حساب الوقت المتبقي
      const remainingMinutes = Math.max(0, BOOKING_SETTINGS.EDIT_WINDOW_MINUTES - diffInMinutes);

      return remainingMinutes;
    } catch (error) {
      console.error("خطأ في حساب الوقت المتبقي لتعديل الطلب:", error);
      return 0; // في حالة حدوث خطأ، نفترض أنه لا يوجد وقت متبقي
    }
  },

  // تم إزالة وظيفة التحقق من الطلبات المنتهية وإلغائها تلقائيًا
};

export default bookingService;
