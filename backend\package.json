{"name": "jobscope-backend", "version": "1.0.0", "description": "Backend para la aplicación JobScope", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "seed": "node src/utils/seedData.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["jobscope", "backend", "api"], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"axios": "^1.9.0", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "express-async-handler": "^1.2.0", "express-validator": "^7.2.1", "firebase": "^11.6.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.15.1", "morgan": "^1.10.0", "multer": "^1.4.5-lts.2", "node-cron": "^3.0.3", "node-fetch": "^3.3.2", "nodemailer": "^6.10.1"}, "devDependencies": {"nodemon": "^3.1.10"}}