import React, { useEffect } from "react";
import { Bell } from "lucide-react";
import useNotificationStore from "../../store/notificationStore";
import useThemeStore from "../../store/themeStore";
import useUserStore from "../../store/userStore";

const NotificationBell = ({ onClick }) => {
  const unreadCount = useNotificationStore((state) => state.unreadCount);
  const notifications = useNotificationStore((state) => state.notifications);
  const fetchNotifications = useNotificationStore(
    (state) => state.fetchNotifications
  );
  const isLoading = useNotificationStore((state) => state.isLoading);
  const darkMode = useThemeStore((state) => state.darkMode);
  const { user, isAuthenticated } = useUserStore();

  // تم إزالة تتبع طلبات الحجز المعلقة بناءً على طلب المستخدم

  // تحميل الإشعارات من الخادم عند تحميل المكون (فقط إذا كان المستخدم مسجل دخول)
  useEffect(() => {
    // التحقق من تسجيل الدخول قبل جلب الإشعارات
    if (!user || !isAuthenticated) {
      console.log("المستخدم غير مسجل دخول، تخطي جلب الإشعارات");
      return;
    }

    const loadNotifications = async () => {
      try {
        await fetchNotifications();
      } catch (error) {
        console.log("تعذر تحميل الإشعارات في NotificationBell:", error.message);
      }
    };

    loadNotifications();

    // تفعيل التحديث التلقائي للإشعارات كل دقيقة (فقط للمستخدمين المسجلين)
    const intervalId = setInterval(() => {
      if (user && isAuthenticated) {
        loadNotifications();
      }
    }, 60000); // 60 ثانية

    return () => clearInterval(intervalId);
  }, [fetchNotifications, user, isAuthenticated]);

  // طباعة معلومات التصحيح عند تغير عدد الإشعارات غير المقروءة
  useEffect(() => {}, [unreadCount, notifications, isLoading]);

  return (
    <div className="relative">
      <button
        onClick={onClick}
        className={`relative p-2 rounded-full transition-all duration-200 ${
          darkMode
            ? "hover:bg-indigo-700/50 text-yellow-300 hover:text-yellow-200"
            : "hover:bg-yellow-50 text-white hover:text-yellow-300"
        } transform hover:scale-105`}
        aria-label="الإشعارات"
      >
        <Bell
          size={20}
          className={`transition-colors duration-200 ${
            isLoading ? "animate-pulse" : ""
          }`}
        />

        {unreadCount > 0 && (
          <span className="notification-badge">
            {unreadCount > 9 ? "9+" : unreadCount}
          </span>
        )}
      </button>

      {/* تم إزالة إشعار النقطة الخضراء بناءً على طلب المستخدم */}
    </div>
  );
};

export default NotificationBell;
