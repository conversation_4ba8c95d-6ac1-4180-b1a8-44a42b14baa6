const mongoose = require("mongoose");
const Profession = require("../models/profession.model");
require("dotenv").config();

// بيانات المهن والتخصصات من الفرونت إند
const professionsData = [
  {
    name: "كهربائي",
    specializations: [
      "تمديدات منزلية",
      "صيانة كهربائية",
      "تركيب أنظمة إنارة",
      "لوحات كهربائية",
      "تركيب مولدات",
      "صيانة مصاعد",
      "أنظمة طاقة شمسية",
      "أتمتة منزلية",
    ],
    icon: "bolt",
  },
  {
    name: "سباك",
    specializations: [
      "تمديدات صحية",
      "صيانة وتركيب",
      "معالجة تسربات",
      "تركيب أدوات صحية",
      "صيانة سخانات",
      "تركيب خزانات مياه",
      "تركيب مضخات",
      "صيانة شبكات الصرف",
    ],
    icon: "droplet",
  },
  {
    name: "نجار",
    specializations: [
      "أثاث منزلي",
      "أبواب ونوافذ",
      "ديكورات خشبية",
      "مطابخ",
      "غرف نوم",
      "خزائن حائط",
      "أثاث مكتبي",
      "ترميم أثاث قديم",
    ],
    icon: "hammer",
  },
  {
    name: "دهان",
    specializations: [
      "دهانات داخلية",
      "دهانات خارجية",
      "دهانات حديثة",
      "ديكورات جبسية",
      "ورق جدران",
      "دهانات زخرفية",
      "دهانات إيبوكسي",
      "دهانات مقاومة للرطوبة",
    ],
    icon: "brush",
  },
  {
    name: "مصمم ديكور",
    specializations: [
      "تصميم داخلي",
      "تصميم واجهات",
      "استشارات ديكور",
      "تصميم مساحات تجارية",
      "تصميم مكاتب",
      "تصميم حدائق",
      "تصميم إضاءة",
      "تصميم ثلاثي الأبعاد",
    ],
    icon: "palette",
  },
  {
    name: "ميكانيكي",
    specializations: [
      "صيانة سيارات",
      "كهرباء سيارات",
      "ميكانيك عام",
      "صيانة محركات",
      "تبديل زيوت",
      "إصلاح فرامل",
      "ضبط زوايا",
      "صيانة تكييف سيارات",
    ],
    icon: "wrench",
  },
  {
    name: "حداد",
    specializations: [
      "أبواب وشبابيك",
      "هياكل معدنية",
      "أعمال الألمنيوم",
      "درابزين",
      "بوابات حديدية",
      "حماية نوافذ",
      "هناجر",
      "أعمال ستانلس ستيل",
    ],
    icon: "hammer",
  },
  {
    name: "بناء",
    specializations: [
      "بناء جدران",
      "تبليط",
      "أعمال إسمنتية",
      "ترميم",
      "تشطيبات",
      "قصارة",
      "عزل مائي",
      "عزل حراري",
    ],
    icon: "home",
  },
  {
    name: "مكيفات",
    specializations: [
      "تركيب",
      "صيانة",
      "تنظيف",
      "إصلاح",
      "شحن غاز",
      "تركيب وحدات مركزية",
      "صيانة دورية",
      "استبدال قطع",
    ],
    icon: "wind",
  },
  {
    name: "خياط",
    specializations: [
      "ملابس رجالية",
      "ملابس نسائية",
      "تفصيل وخياطة",
      "تعديل ملابس",
      "خياطة ستائر",
      "خياطة مفروشات",
      "تطريز",
      "تصميم أزياء",
    ],
    icon: "scissors",
  },
  {
    name: "طباخ",
    specializations: [
      "مأكولات شرقية",
      "حلويات",
      "مأكولات غربية",
      "مشاوي",
      "معجنات",
      "طبخ منزلي",
      "طعام صحي",
      "مناسبات وحفلات",
    ],
    icon: "chef-hat",
  },
  {
    name: "مزارع",
    specializations: [
      "زراعة خضروات",
      "زراعة أشجار مثمرة",
      "تقليم أشجار",
      "تركيب أنظمة ري",
      "مكافحة آفات",
      "تنسيق حدائق",
      "زراعة عضوية",
      "إنتاج شتلات",
    ],
    icon: "flower",
  },
  {
    name: "مصلح أجهزة كهربائية",
    specializations: [
      "غسالات",
      "ثلاجات",
      "أفران",
      "مكيفات",
      "تلفزيونات",
      "أجهزة صغيرة",
      "سخانات مياه",
      "مكانس كهربائية",
    ],
    icon: "tv",
  },
  {
    name: "مصلح موبايلات وكمبيوتر",
    specializations: [
      "إصلاح هواتف",
      "إصلاح حواسيب",
      "تغيير شاشات",
      "إزالة كلمات المرور",
      "استعادة البيانات",
      "إصلاح شبكات",
    ],
    icon: "smartphone",
  },
  {
    name: "سائق",
    specializations: [
      "توصيل ركاب",
      "نقل بضائع",
      "نقل أثاث",
      "رحلات بين المدن",
      "توصيل طلبات",
      "سيارات خاصة",
      "شاحنات",
      "حافلات",
    ],
    icon: "car",
  },
  {
    name: "مصور",
    specializations: [
      "تصوير مناسبات",
      "تصوير منتجات",
      "تصوير عقارات",
      "تصوير فوتوغرافي",
      "تصوير فيديو",
      "مونتاج",
      "تصوير جوي",
      "تصوير وثائقي",
    ],
    icon: "camera",
  },
  {
    name: "معلم",
    specializations: [
      "رياضيات",
      "فيزياء",
      "كيمياء",
      "لغة عربية",
      "لغة إنجليزية",
      "علوم",
      "تاريخ وجغرافيا",
      "تقوية دراسية",
    ],
    icon: "book",
  },
  {
    name: "حلاق",
    specializations: [
      "قص شعر رجالي",
      "حلاقة ذقن",
      "تصفيف شعر نسائي",
      "صبغ شعر",
      "تسريحات",
      "علاجات شعر",
      "ماكياج",
      "عناية بالبشرة",
    ],
    icon: "scissors",
  },
  {
    name: "تركيب و صيانة ألمنيوم",
    specializations: [
      "تركيب نوافذ ألمنيوم",
      "أبواب ألمنيوم",
      "واجهات زجاجية",
      "مطابخ ألمنيوم",
      "غرف زجاجية",
      "شتر ودرابزين",
      "صيانة ألمنيوم",
      "تركيب سكك وأقفال",
    ],
    icon: "wrench",
  },
  {
    name: "معلم سيراميك",
    specializations: [
      "تبليط أرضيات",
      "تبليط جدران حمامات",
      "قص وتشكيل السيراميك",
      "تركيب بورسلان",
      "تركيب غرانيت",
      "تنسيق فواصل",
      "تركيب سيراميك ثلاثي الأبعاد",
      "تصليح وتعديل بلاط",
    ],
    icon: "square",
  },
  {
    name: "عامل نظافة",
    specializations: [
      "تنظيف منازل",
      "تنظيف مكاتب",
      "تنظيف واجهات زجاجية",
      "تنظيف خزانات مياه",
      "غسيل سجاد",
      "تنظيف مطاعم ومحلات",
      "تعقيم وتعطير",
      "تنظيف ما بعد البناء",
    ],
    icon: "spray-can",
  },
  {
    name: "عامل توصيل",
    specializations: [
      "توصيل طلبات طعام",
      "توصيل مستندات",
      "توصيل أدوية",
      "توصيل منتجات من متاجر",
      "توصيل ضمن المدينة",
      "توصيل سريع",
      "دراجة نارية",
      "سيارة صغيرة",
    ],
    icon: "truck",
  },
  {
    name: "عامل صيانة عامة",
    specializations: [
      "تصليح أثاث منزلي",
      "صيانة أبواب ونوافذ",
      "صيانة حمامات ومطابخ",
      "تصليح تسربات",
      "تصليح أقفال",
      "تثبيت أثاث",
      "صيانة دورية",
      "تركيب إكسسوارات منزلية",
    ],
    icon: "tools",
  },
  {
    name: "فني تبريد وتكييف",
    specializations: [
      "تركيب برادات",
      "صيانة وحدات تبريد",
      "تعبئة غاز تبريد",
      "صيانة برادات تجارية",
      "أنظمة تبريد صناعي",
      "فحص أعطال",
      "تركيب غرف تبريد",
      "تركيب وحدات تبريد مركزية",
    ],
    icon: "snowflake",
  },
  {
    name: "عامل بناء",
    specializations: [
      "بناء جدران حجر",
      "بناء جدران بلوك",
      "قصارة (لياسة)",
      "صب أعمدة وأسقف",
      "تركيب حجارة واجهات",
      "فورمجي (خشب تسليح)",
      "أعمال ترميم",
      "رفع سقايل (سقالات)",
    ],
    icon: "hard-hat",
  },
  {
    name: "فني تمديدات صحية",
    specializations: [
      "تركيب مغاسل",
      "تمديد أنابيب مياه",
      "صيانة تسريبات",
      "تصليح مضخات",
      "تركيب سخانات",
      "فحص ضغط المياه",
      "تنظيف مجاري",
      "تجهيز حمامات جديدة",
    ],
    icon: "droplet",
  },
  {
    name: "فني لحام وحدادة",
    specializations: [
      "لحام أبواب حديد",
      "لحام شبابيك",
      "لحام سلالم",
      "تصنيع قواعد معدنية",
      "لحام بالغاز",
      "لحام كهرباء",
      "قص وتشكيل الحديد",
      "صيانة هياكل معدنية",
    ],
    icon: "flame",
  },
  {
    name: "حدّاد متخصص في الأبواب والنوافذ",
    specializations: [
      "تصنيع أبواب حديد",
      "شبابيك حديد مزخرفة",
      "بوابات خارجية",
      "تركيب أبواب أمنية",
      "درابزينات وسلالم",
      "أبواب محلات",
      "صيانة لحام",
      "دهان الحديد",
    ],
    icon: "door-open",
  },
  // خدمات الصحة والجمال
  {
    name: "طبيب",
    specializations: [
      "طب عام",
      "طب أسنان",
      "طب عيون",
      "طب أطفال",
      "طب باطني",
      "طب جلدية",
      "طب نسائية وتوليد",
      "طب عظام",
    ],
    icon: "stethoscope",
  },
  {
    name: "ممرض",
    specializations: [
      "رعاية منزلية",
      "حقن وتضميد",
      "رعاية مسنين",
      "رعاية أطفال",
      "قياس ضغط وسكر",
      "تركيب محاليل",
      "رعاية ما بعد العمليات",
      "إسعافات أولية",
    ],
    icon: "heart-pulse",
  },
  {
    name: "معالج فيزيائي",
    specializations: [
      "علاج إصابات رياضية",
      "علاج آلام الظهر",
      "علاج ما بعد الكسور",
      "تأهيل حركي",
      "علاج تشنجات عضلية",
      "علاج مشاكل المفاصل",
      "تدليك علاجي",
      "علاج طبيعي منزلي",
    ],
    icon: "activity",
  },
  {
    name: "خبير تجميل",
    specializations: [
      "مكياج احترافي",
      "تصفيف شعر",
      "عناية بالبشرة",
      "مكياج عرائس",
      "حناء وتزيين",
      "قص وصبغ شعر",
      "علاجات تجميلية",
      "مانيكير وباديكير",
    ],
    icon: "sparkles",
  },
  // خدمات الطعام والضيافة
  {
    name: "شيف",
    specializations: [
      "مأكولات شرقية",
      "مأكولات غربية",
      "حلويات",
      "مشاوي",
      "مأكولات بحرية",
      "معجنات",
      "طبخ منزلي",
      "طعام صحي",
    ],
    icon: "chef-hat",
  },
  {
    name: "نادل",
    specializations: [
      "خدمة مطاعم",
      "خدمة حفلات",
      "خدمة مناسبات",
      "خدمة مؤتمرات",
      "خدمة كوكتيل",
      "خدمة بوفيه",
      "خدمة كبار الشخصيات",
      "خدمة فنادق",
    ],
    icon: "coffee",
  },
  {
    name: "منسق حفلات",
    specializations: [
      "تنسيق أعراس",
      "تنسيق مناسبات",
      "تنسيق مؤتمرات",
      "تنسيق حفلات أطفال",
      "تنسيق حفلات تخرج",
      "تنسيق معارض",
      "تنسيق ديكور حفلات",
      "تنسيق بوفيهات",
    ],
    icon: "calendar",
  },
  // خدمات الإلكترونيات والتقنية
  {
    name: "فني إلكترونيات",
    specializations: [
      "إصلاح أجهزة كهربائية",
      "إصلاح أجهزة إلكترونية",
      "إصلاح أجهزة منزلية",
      "إصلاح أجهزة صوت",
      "إصلاح أجهزة تلفاز",
      "إصلاح أجهزة ألعاب",
      "إصلاح أجهزة طبية",
      "تركيب أنظمة إلكترونية",
    ],
    icon: "cpu",
  },
  {
    name: "مصمم مواقع",
    specializations: [
      "تصميم مواقع تعريفية",
      "تصميم متاجر إلكترونية",
      "تصميم مواقع خدمية",
      "تصميم مواقع شخصية",
      "تصميم مواقع تعليمية",
      "تصميم مواقع إخبارية",
      "تصميم واجهات مستخدم",
      "تطوير مواقع ووردبريس",
    ],
    icon: "globe",
  },
  {
    name: "مطور تطبيقات",
    specializations: [
      "تطوير تطبيقات أندرويد",
      "تطوير تطبيقات آيفون",
      "تطوير تطبيقات ويب",
      "تطوير تطبيقات سطح المكتب",
      "تطوير ألعاب",
      "تطوير تطبيقات تعليمية",
      "تطوير تطبيقات خدمية",
      "صيانة وتحديث تطبيقات",
    ],
    icon: "code",
  },
];

// دالة الاتصال بقاعدة البيانات
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI);
    console.log("تم الاتصال بقاعدة البيانات بنجاح");
  } catch (error) {
    console.error("خطأ في الاتصال بقاعدة البيانات:", error);
    process.exit(1);
  }
};

// دالة تحديث المهن
const updateProfessions = async () => {
  try {
    console.log("بدء عملية تحديث المهن...");

    // حذف جميع المهن الموجودة
    console.log("حذف المهن الموجودة...");
    await Profession.deleteMany({});
    console.log("تم حذف جميع المهن الموجودة");

    // إضافة المهن الجديدة
    console.log("إضافة المهن الجديدة...");
    const createdProfessions = await Profession.insertMany(professionsData);
    console.log(`تم إضافة ${createdProfessions.length} مهنة بنجاح`);

    // عرض المهن المضافة
    console.log("\nالمهن المضافة:");
    createdProfessions.forEach((profession, index) => {
      console.log(
        `${index + 1}. ${profession.name} - ${
          profession.specializations.length
        } تخصص`
      );
    });

    console.log("\nتم تحديث المهن بنجاح!");
  } catch (error) {
    console.error("خطأ في تحديث المهن:", error);
  }
};

// تشغيل السكريبت
const runScript = async () => {
  await connectDB();
  await updateProfessions();
  await mongoose.connection.close();
  console.log("تم إغلاق الاتصال بقاعدة البيانات");
  process.exit(0);
};

// تشغيل السكريبت إذا تم استدعاؤه مباشرة
if (require.main === module) {
  runScript();
}

module.exports = { updateProfessions, professionsData };
