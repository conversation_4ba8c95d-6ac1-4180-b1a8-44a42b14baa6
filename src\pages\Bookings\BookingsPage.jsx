import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { motion, AnimatePresence } from "framer-motion";
import Layout from "../../components/layout/Layout";
import Card from "../../components/common/Card";
import Button from "../../components/common/Button";
import useUserStore from "../../store/userStore";
import useBookingStore from "../../store/bookingStore";
import useThemeStore from "../../store/themeStore";
import useReviewStore from "../../store/reviewStore";
import { Link } from "react-router-dom";
import LoginRedirect from "../../components/auth/LoginRedirect";
import BookingDetailsModal from "../../components/bookings/BookingDetailsModal";
import BookingEditModal from "../../components/bookings/BookingEditModal";
import bookingService from "../../services/bookingService";
import toastUtils from "../../utils/toastUtils";
import {
  BOOKING_STATUS,
  BOOKING_SETTINGS,
  BOOKING_STATUS_MESSAGES,
  BOOKING_STATUS_DETAILED_MESSAGES,
} from "../../services/config";
import SimpleReviewsList from "../../components/reviews/SimpleReviewsList";
import { Star, Award, Clock, DollarSign, MessageCircle } from "lucide-react";
import { reviewService } from "../../services/api";

// مكون لعرض التقييمات
const ReviewSection = ({ booking, darkMode }) => {
  const [review, setReview] = useState(null);
  const [loading, setLoading] = useState(true);
  const getReviewById = useReviewStore((state) => state.getReviewById);
  const fetchReviewById = useReviewStore((state) => state.fetchReviewById);

  // تحميل التقييم عند عرض الطلب
  useEffect(() => {
    const loadReview = async () => {
      if (booking.reviewId) {
        setLoading(true);

        // استخراج المعرف الفعلي إذا كان كائنًا
        let actualReviewId = booking.reviewId;

        // إذا كان reviewId كائنًا، نحاول استخراج المعرف منه
        if (typeof booking.reviewId === "object" && booking.reviewId !== null) {
          actualReviewId = booking.reviewId.id || booking.reviewId._id;
        }

        // إذا لم نتمكن من استخراج معرف صالح، نعود
        if (!actualReviewId) {
          console.warn(
            "لم يتم العثور على معرف صالح في الكائن:",
            booking.reviewId
          );
          setLoading(false);
          return;
        }

        // تحويل معرف التقييم إلى نص للمقارنة
        const reviewIdStr = String(actualReviewId);

        try {
          // أولاً، محاولة الحصول على التقييم من الذاكرة المحلية
          const localReview = await getReviewById(reviewIdStr);

          if (localReview) {
            setReview(localReview);
            setLoading(false);
          } else {
            // إذا لم يتم العثور على التقييم محليًا، تحميله من الخادم
            try {
              const serverReview = await reviewService.getReviewById(
                reviewIdStr
              );

              if (serverReview) {
                setReview(serverReview);

                // إضافة التقييم إلى متجر التقييمات
                const { reviews } = useReviewStore.getState();
                const reviewExists = reviews.some(
                  (r) =>
                    (r.id &&
                      String(r.id) ===
                        String(serverReview.id || serverReview._id)) ||
                    (r._id &&
                      String(r._id) ===
                        String(serverReview.id || serverReview._id))
                );

                if (!reviewExists) {
                  useReviewStore.setState({
                    reviews: [...reviews, serverReview],
                  });
                }
              }
              setLoading(false);
            } catch (error) {
              console.error("خطأ في تحميل التقييم من الخادم:", error);
              setLoading(false);
            }
          }
        } catch (error) {
          console.error("خطأ في تحميل التقييم:", error);
          setLoading(false);
        }
      } else {
        setLoading(false);
      }
    };

    loadReview();
  }, [booking.reviewId, getReviewById]);

  // التحقق من وجود التقييم وأنه ليس افتراضيًا
  if (loading) {
    return (
      <div
        className={`p-4 rounded-lg shadow-md mt-4 ${
          darkMode
            ? "bg-gradient-to-br from-indigo-900/40 to-blue-900/30 border border-indigo-800/50"
            : "bg-gradient-to-br from-indigo-50 to-blue-50 border border-indigo-100"
        } transition-colors duration-300`}
      >
        <div className="flex justify-center items-center py-4">
          <div
            className={`animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 ${
              darkMode ? "border-indigo-400" : "border-indigo-600"
            }`}
          ></div>
          <span
            className={`mr-3 ${
              darkMode ? "text-indigo-300" : "text-indigo-700"
            }`}
          >
            جاري تحميل التقييم...
          </span>
        </div>
      </div>
    );
  }

  if (!review || review.isDefaultReview) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -10 }}
        transition={{ duration: 0.3 }}
        className={`p-4 rounded-lg shadow-md mt-4 ${
          darkMode
            ? "bg-gradient-to-br from-indigo-900/40 to-blue-900/30 border border-indigo-800/50"
            : "bg-gradient-to-br from-indigo-50 to-blue-50 border border-indigo-100"
        } transition-colors duration-300 backdrop-blur-sm`}
      >
        {/* رأس التقييم */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center">
            <div
              className={`p-1.5 rounded-full ${
                darkMode ? "bg-indigo-700/50" : "bg-indigo-100"
              } mr-2`}
            >
              <Award
                size={18}
                className={`${
                  darkMode ? "text-indigo-300" : "text-indigo-600"
                }`}
              />
            </div>
            <span
              className={`text-sm font-bold ${
                darkMode ? "text-indigo-300" : "text-indigo-700"
              } transition-colors duration-300`}
            >
              تقييم الخدمة
            </span>
          </div>

          <div className="flex items-center bg-opacity-80 rounded-full px-2 py-1">
            <div className="flex">
              {[1, 2, 3, 4, 5].map((star) => (
                <Star
                  key={star}
                  size={16}
                  className={`${
                    star <= review.overallRating
                      ? darkMode
                        ? "text-yellow-400 fill-yellow-400"
                        : "text-yellow-500 fill-yellow-500"
                      : darkMode
                      ? "text-gray-600"
                      : "text-gray-300"
                  }`}
                />
              ))}
            </div>
            <span
              className={`text-sm font-bold mr-1 ${
                darkMode ? "text-yellow-400" : "text-yellow-600"
              } transition-colors duration-300`}
            >
              {review.overallRating}/5
            </span>
          </div>
        </div>

        {/* تفاصيل التقييم */}
        <div
          className={`p-3 rounded-lg mb-3 ${
            darkMode
              ? "bg-indigo-900/20 border border-indigo-800/30"
              : "bg-white/80 border border-indigo-100/80"
          }`}
        >
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            {/* جودة العمل */}
            <div className="flex items-center">
              <div
                className={`p-1 rounded-full ${
                  darkMode ? "bg-blue-800/30" : "bg-blue-100"
                } ml-2`}
              >
                <Award
                  size={14}
                  className={`${darkMode ? "text-blue-400" : "text-blue-600"}`}
                />
              </div>
              <span
                className={`text-xs font-medium ${
                  darkMode ? "text-gray-300" : "text-gray-700"
                } ml-1 w-20`}
              >
                جودة العمل:
              </span>
              <div className="flex">
                {[1, 2, 3, 4, 5].map((star) => (
                  <Star
                    key={star}
                    size={14}
                    className={`${
                      star <= review.qualityRating
                        ? darkMode
                          ? "text-yellow-400 fill-yellow-400"
                          : "text-yellow-500 fill-yellow-500"
                        : darkMode
                        ? "text-gray-600"
                        : "text-gray-300"
                    }`}
                  />
                ))}
              </div>
            </div>

            {/* الالتزام بالوقت */}
            <div className="flex items-center">
              <div
                className={`p-1 rounded-full ${
                  darkMode ? "bg-purple-800/30" : "bg-purple-100"
                } ml-2`}
              >
                <Clock
                  size={14}
                  className={`${
                    darkMode ? "text-purple-400" : "text-purple-600"
                  }`}
                />
              </div>
              <span
                className={`text-xs font-medium ${
                  darkMode ? "text-gray-300" : "text-gray-700"
                } ml-1 w-20`}
              >
                الالتزام بالوقت:
              </span>
              <div className="flex">
                {[1, 2, 3, 4, 5].map((star) => (
                  <Star
                    key={star}
                    size={14}
                    className={`${
                      star <= review.punctualityRating
                        ? darkMode
                          ? "text-yellow-400 fill-yellow-400"
                          : "text-yellow-500 fill-yellow-500"
                        : darkMode
                        ? "text-gray-600"
                        : "text-gray-300"
                    }`}
                  />
                ))}
              </div>
            </div>

            {/* القيمة مقابل السعر */}
            <div className="flex items-center">
              <div
                className={`p-1 rounded-full ${
                  darkMode ? "bg-green-800/30" : "bg-green-100"
                } ml-2`}
              >
                <DollarSign
                  size={14}
                  className={`${
                    darkMode ? "text-green-400" : "text-green-600"
                  }`}
                />
              </div>
              <span
                className={`text-xs font-medium ${
                  darkMode ? "text-gray-300" : "text-gray-700"
                } ml-1 w-20`}
              >
                القيمة مقابل السعر:
              </span>
              <div className="flex">
                {[1, 2, 3, 4, 5].map((star) => (
                  <Star
                    key={star}
                    size={14}
                    className={`${
                      star <= review.priceRating
                        ? darkMode
                          ? "text-yellow-400 fill-yellow-400"
                          : "text-yellow-500 fill-yellow-500"
                        : darkMode
                        ? "text-gray-600"
                        : "text-gray-300"
                    }`}
                  />
                ))}
              </div>
            </div>

            {/* التواصل */}
            <div className="flex items-center">
              <div
                className={`p-1 rounded-full ${
                  darkMode ? "bg-yellow-800/30" : "bg-yellow-100"
                } ml-2`}
              >
                <MessageCircle
                  size={14}
                  className={`${
                    darkMode ? "text-yellow-400" : "text-yellow-600"
                  }`}
                />
              </div>
              <span
                className={`text-xs font-medium ${
                  darkMode ? "text-gray-300" : "text-gray-700"
                } ml-1 w-20`}
              >
                التواصل:
              </span>
              <div className="flex">
                {[1, 2, 3, 4, 5].map((star) => (
                  <Star
                    key={star}
                    size={14}
                    className={`${
                      star <= review.communicationRating
                        ? darkMode
                          ? "text-yellow-400 fill-yellow-400"
                          : "text-yellow-500 fill-yellow-500"
                        : darkMode
                        ? "text-gray-600"
                        : "text-gray-300"
                    }`}
                  />
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* تعليق التقييم */}
        {review.comment && (
          <div
            className={`p-3 rounded-lg ${
              darkMode
                ? "bg-gray-800/40 border border-gray-700/50"
                : "bg-white/90 border border-gray-100"
            } transition-colors duration-300`}
          >
            <div className="flex items-start mb-2">
              <MessageCircle
                size={14}
                className={`mt-0.5 ml-1.5 ${
                  darkMode ? "text-blue-400" : "text-blue-600"
                }`}
              />
              <span
                className={`text-xs font-medium ${
                  darkMode ? "text-blue-400" : "text-blue-600"
                } transition-colors duration-300`}
              >
                تعليق العميل
              </span>
            </div>
            <p
              className={`text-sm ${
                darkMode ? "text-gray-300" : "text-gray-700"
              } transition-colors duration-300 leading-relaxed pr-2`}
            >
              {review.comment}
            </p>
          </div>
        )}

        {/* تاريخ التقييم */}
        {review.createdAt && (
          <div className="mt-3 text-left">
            <span
              className={`text-xs ${
                darkMode ? "text-gray-500" : "text-gray-500"
              } transition-colors duration-300`}
            >
              {new Date(review.createdAt).toLocaleDateString("ar-EG", {
                year: "numeric",
                month: "short",
                day: "numeric",
              })}
            </span>
          </div>
        )}
      </motion.div>
    </AnimatePresence>
  );
};

const BookingsPage = () => {
  const navigate = useNavigate();
  const user = useUserStore((state) => state.user);
  const userType = useUserStore((state) => state.userType);
  const logout = useUserStore((state) => state.logout);
  const darkMode = useThemeStore((state) => state.darkMode);

  const { getUserBookings } = useBookingStore();
  const [bookings, setBookings] = useState([]);
  const [activeTab, setActiveTab] = useState("all"); // all, pending, accepted, completed, cancelled
  const [selectedBooking, setSelectedBooking] = useState(null);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [editableBookings, setEditableBookings] = useState({});
  const [isLoading, setIsLoading] = useState(true); // إضافة حالة التحميل

  // إعدادات ترقيم الصفحات
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(5); // عدد العناصر في كل صفحة

  // Función para formatear la hora en formato de 12 horas con indicador AM/PM en árabe
  const formatTime = (timeString) => {
    // Convertir el formato de 24 horas a formato de 12 horas con indicador AM/PM en árabe
    const [hours, minutes] = timeString.split(":");
    const hour = parseInt(hours, 10);
    const ampm = hour >= 12 ? "م" : "ص"; // م para PM, ص para AM
    const hour12 = hour % 12 || 12; // Convertir 0 a 12
    return `${hour12}:${minutes} ${ampm}`;
  };

  // Función para verificar si una reserva pendiente ha expirado
  const checkBookingExpired = (booking) => {
    // إذا لم تكن الحالة "قيد الانتظار"، فلا داعي للتحقق
    if (booking.status !== "pending") return false;

    try {
      // تحويل التاريخ والوقت إلى كائن Date
      const bookingDate = new Date(`${booking.date}T${booking.time}`);
      const now = new Date();

      // لا نعتبر الطلب منتهيًا إلا إذا كان الوقت المحدد قد مر بالفعل
      // وكان الفرق أكثر من 5 دقائق (للسماح بهامش خطأ)
      const timeDiffInMinutes = (now - bookingDate) / (1000 * 60);
      return timeDiffInMinutes > 5;
    } catch (error) {
      console.error("خطأ في التحقق من انتهاء صلاحية الطلب:", error);
      return false; // في حالة حدوث خطأ، نفترض أن الطلب لم ينته
    }
  };

  // تم إزالة وظيفة التحقق من الطلبات المنتهية وإلغائها تلقائيًا

  useEffect(() => {
    if (user) {
      // تعيين حالة التحميل إلى true عند بدء تحميل البيانات
      setIsLoading(true);

      // تحميل الحجوزات من الخادم أولاً
      const loadBookings = async () => {
        try {
          // إذا كان المستخدم حرفي، نتأكد من تحميل بيانات الحرفي أولاً
          if (userType === "craftsman") {
            try {
              // استخدام _id أو id أو uid، أيهما متاح
              const userId = user._id || user.id || user.uid;
            } catch (error) {
              console.error("خطأ في تحميل بيانات الحرفي:", error);
            }
          }

          // تحميل الحجوزات
          await useBookingStore.getState().fetchBookings();

          // تحديث الإشعارات بعد تحميل الطلبات (مع معالجة الأخطاء)
          try {
            const { default: useNotificationStore } = await import(
              "../../store/notificationStore"
            );

            // التحقق من تسجيل الدخول قبل جلب الإشعارات
            if (user && localStorage.getItem("token")) {
              await useNotificationStore.getState().fetchNotifications();
            } else {
              console.log("المستخدم غير مسجل دخول، تخطي جلب الإشعارات");
            }
          } catch (error) {
            console.log("تعذر تحديث الإشعارات:", error.message);
          }
        } catch (error) {
          console.error("خطأ في تحميل الحجوزات من الخادم:", error);
        }

        // طباعة جميع الحجوزات في المتجر للتصحيح
        const allBookings = useBookingStore.getState().bookings;

        // استخدام _id أو id أو uid، أيهما متاح
        const userId = user._id || user.id || user.uid;

        // حتى لو كان معرف المستخدم غير موجود، سنستمر في عرض الحجوزات
        if (!userId) {
        }

        // Get user bookings
        const userBookings = getUserBookings(userId, userType);

        // تحديث الحالة مباشرة بدون التحقق من الطلبات المنتهية
        setBookings(userBookings);

        // إيقاف التحميل بعد الانتهاء من جلب البيانات
        setTimeout(() => {
          setIsLoading(false);
        }, 200); // تأخير بسيط لإظهار حالة التحميل
      };

      loadBookings();
    }
  }, [user, userType, getUserBookings]);

  if (!user) {
    return <LoginRedirect />;
  }

  // Filter bookings based on active tab
  // Las reservas ya vienen ordenadas por fecha de creación desde el store
  const filteredBookings = bookings.filter((booking) => {
    if (activeTab === "all") return true;
    // إذا كان التبويب "cancelled"، نعرض الطلبات الملغية والطلبات الملغية تلقائياً
    if (activeTab === "cancelled") {
      return (
        booking.status === "cancelled" || booking.status === "cancelled_expired"
      );
    }
    return booking.status === activeTab;
  });

  // حساب إجمالي عدد الصفحات
  const totalPages = Math.ceil(filteredBookings.length / itemsPerPage);

  // الحصول على الطلبات للصفحة الحالية
  const currentBookings = filteredBookings.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  // التأكد من أن الصفحة الحالية ضمن النطاق المسموح به
  useEffect(() => {
    if (currentPage > totalPages && totalPages > 0) {
      setCurrentPage(1);
    }
  }, [activeTab, totalPages, currentPage]);

  // دالة للانتقال إلى صفحة محددة
  const goToPage = (pageNumber) => {
    setCurrentPage(pageNumber);
    // التمرير إلى أعلى الصفحة
    window.scrollTo({ top: 0, behavior: "smooth" });
  };

  // دالة للتحقق من إمكانية تعديل الطلب
  const canEditBooking = (booking) => {
    if (!booking || userType !== "client") return false;
    return bookingService.canEditBooking(booking);
  };

  // دالة لحساب الوقت المتبقي لتعديل الطلب
  const getRemainingEditTime = (booking) => {
    if (!booking) return 0;
    return bookingService.getRemainingEditTime(booking);
  };

  // دالة لفتح نموذج تعديل الطلب
  const handleEditBooking = (booking) => {
    if (canEditBooking(booking)) {
      setSelectedBooking(booking);
      setShowEditModal(true);
    } else {
      showToast(
        "لا يمكن تعديل الطلب بعد انتهاء فترة التعديل المسموح بها",
        "error"
      );
    }
  };

  // دالة لإلغاء الطلب
  const handleCancelBooking = async (booking) => {
    if (!booking || userType !== "client") return;

    if (!canEditBooking(booking)) {
      toastUtils.showToast(
        "لا يمكن إلغاء الطلب بعد انتهاء فترة التعديل المسموح بها",
        "error"
      );
      return;
    }

    try {
      await bookingService.cancelBooking(booking.id);

      // تحديث حالة الطلب محلياً
      const updatedBookings = bookings.map((b) =>
        b.id === booking.id ? { ...b, status: BOOKING_STATUS.CANCELLED } : b
      );

      setBookings(updatedBookings);
      toastUtils.showToast("تم إلغاء الطلب بنجاح", "success");
    } catch (error) {
      console.error("خطأ في إلغاء الطلب:", error);
      toastUtils.showToast("حدث خطأ أثناء إلغاء الطلب", "error");
    }
  };

  // دالة لحفظ التعديلات على الطلب
  const handleSaveBooking = async (updatedBooking) => {
    try {
      // تحديث الطلب في القائمة
      const updatedBookings = bookings.map((b) =>
        b.id === updatedBooking.id ? { ...b, ...updatedBooking } : b
      );

      setBookings(updatedBookings);
      toastUtils.showToast("تم تحديث الطلب بنجاح", "success");
    } catch (error) {
      console.error("خطأ في تحديث الطلب:", error);
      toastUtils.showToast("حدث خطأ أثناء تحديث الطلب", "error");
    }
  };

  return (
    <Layout user={user} onLogout={logout}>
      <div
        className={`min-h-screen py-8 ${
          darkMode
            ? "bg-gray-900"
            : "bg-gradient-to-br from-blue-50 to-indigo-100"
        } transition-colors duration-300`}
      >
        <div className="container mx-auto px-4">
          <div className="mb-8">
            <h1
              className={`text-3xl font-bold mb-2 ${
                darkMode ? "text-indigo-300" : "text-indigo-800"
              } relative inline-block transition-colors duration-300`}
            >
              <span className="relative z-10">
                {userType === "client" ? "طلباتي" : "طلبات الخدمة"}
              </span>
              <span
                className={`absolute bottom-0 left-0 right-0 h-2 ${
                  darkMode ? "bg-indigo-500" : "bg-indigo-300"
                } opacity-40 transform -rotate-1 z-0`}
              ></span>
            </h1>
            <p
              className={`${
                darkMode ? "text-indigo-400" : "text-indigo-600"
              } transition-colors duration-300`}
            >
              {userType === "client"
                ? "إدارة طلبات الخدمة التي قمت بتقديمها للحرفيين"
                : "إدارة طلبات الخدمة المقدمة إليك من العملاء"}
            </p>
          </div>

          {/* Tabs and Items Per Page */}
          <div className="mb-6 flex flex-wrap justify-between">
            <div className="flex flex-wrap gap-2">
              <button
                onClick={() => setActiveTab("all")}
                className={`px-4 py-2 rounded-md transition-all duration-200 ${
                  activeTab === "all"
                    ? `bg-gradient-to-r ${
                        darkMode
                          ? "from-indigo-700 to-purple-800"
                          : "from-blue-500 to-indigo-500"
                      } text-white shadow-md`
                    : `${
                        darkMode
                          ? "bg-gray-800 text-indigo-300 hover:bg-gray-700"
                          : "bg-white text-indigo-700 hover:bg-indigo-50"
                      }`
                }`}
              >
                جميع الطلبات ({bookings.length})
              </button>
              <button
                onClick={() => setActiveTab("pending")}
                className={`px-4 py-2 rounded-md transition-all duration-200 ${
                  activeTab === "pending"
                    ? `bg-gradient-to-r ${
                        darkMode
                          ? "from-yellow-600 to-amber-700"
                          : "from-yellow-400 to-amber-500"
                      } text-white shadow-md`
                    : `${
                        darkMode
                          ? "bg-gray-800 text-yellow-400 hover:bg-gray-700"
                          : "bg-white text-yellow-700 hover:bg-yellow-50"
                      }`
                }`}
              >
                قيد الانتظار (
                {bookings.filter((b) => b.status === "pending").length})
              </button>
              <button
                onClick={() => setActiveTab("accepted")}
                className={`px-4 py-2 rounded-md transition-all duration-200 ${
                  activeTab === "accepted"
                    ? `bg-gradient-to-r ${
                        darkMode
                          ? "from-blue-600 to-cyan-700"
                          : "from-blue-500 to-cyan-600"
                      } text-white shadow-md`
                    : `${
                        darkMode
                          ? "bg-gray-800 text-blue-400 hover:bg-gray-700"
                          : "bg-white text-blue-700 hover:bg-blue-50"
                      }`
                }`}
              >
                تم قبولها (
                {bookings.filter((b) => b.status === "accepted").length})
              </button>
              <button
                onClick={() => setActiveTab("completed")}
                className={`px-4 py-2 rounded-md transition-all duration-200 ${
                  activeTab === "completed"
                    ? `bg-gradient-to-r ${
                        darkMode
                          ? "from-green-600 to-emerald-700"
                          : "from-green-500 to-emerald-600"
                      } text-white shadow-md`
                    : `${
                        darkMode
                          ? "bg-gray-800 text-green-400 hover:bg-gray-700"
                          : "bg-white text-green-700 hover:bg-green-50"
                      }`
                }`}
              >
                مكتملة (
                {bookings.filter((b) => b.status === "completed").length})
              </button>
              <button
                onClick={() => setActiveTab("cancelled")}
                className={`px-4 py-2 rounded-md transition-all duration-200 ${
                  activeTab === "cancelled"
                    ? `bg-gradient-to-r ${
                        darkMode
                          ? "from-red-600 to-rose-700"
                          : "from-red-500 to-rose-600"
                      } text-white shadow-md`
                    : `${
                        darkMode
                          ? "bg-gray-800 text-red-400 hover:bg-gray-700"
                          : "bg-white text-red-700 hover:bg-red-50"
                      }`
                }`}
              >
                ألغيت (
                {
                  bookings.filter(
                    (b) =>
                      b.status === "cancelled" ||
                      b.status === "cancelled_expired"
                  ).length
                }
                )
              </button>
            </div>

            {/* خيار تغيير عدد العناصر في كل صفحة */}
            {filteredBookings.length > 5 && (
              <div
                className={`flex items-center gap-2 ${
                  darkMode ? "text-gray-300" : "text-gray-700"
                }`}
              >
                <span className="text-sm">عدد الطلبات في الصفحة:</span>
                <select
                  value={itemsPerPage}
                  onChange={(e) => {
                    setItemsPerPage(Number(e.target.value));
                    setCurrentPage(1); // إعادة تعيين الصفحة الحالية عند تغيير عدد العناصر
                  }}
                  className={`rounded-md border px-2 py-1 text-sm ${
                    darkMode
                      ? "bg-gray-700 border-gray-600 text-white"
                      : "bg-white border-gray-300 text-gray-700"
                  }`}
                >
                  <option value={5}>5</option>
                  <option value={10}>10</option>
                  <option value={15}>15</option>
                  <option value={20}>20</option>
                </select>
              </div>
            )}
          </div>

          {/* حالة التحميل */}
          {isLoading ? (
            <div className="flex flex-col items-center justify-center py-12">
              <div
                className={`w-16 h-16 border-4 border-t-4 rounded-full animate-spin mb-4 ${
                  darkMode
                    ? "border-gray-700 border-t-indigo-500"
                    : "border-indigo-100 border-t-indigo-500"
                }`}
              ></div>
              <p
                className={`text-lg font-medium ${
                  darkMode ? "text-indigo-300" : "text-indigo-700"
                }`}
              >
                جاري تحميل الطلبات...
              </p>
            </div>
          ) : filteredBookings.length > 0 ? (
            <div className="space-y-4">
              {currentBookings.map((booking) => (
                <motion.div
                  key={booking.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3 }}
                >
                  <Card
                    className={`p-6 ${
                      darkMode
                        ? "bg-gray-800 text-gray-200"
                        : "bg-gradient-to-br from-blue-50 to-indigo-100"
                    } shadow-md hover:shadow-lg transition-all duration-300`}
                  >
                    <div className="flex flex-col md:flex-row justify-between items-start gap-4">
                      <div className="flex-1 w-full overflow-hidden">
                        <div className="flex items-center mb-2">
                          <h3
                            className={`text-xl font-bold ${
                              darkMode ? "text-indigo-300" : "text-indigo-800"
                            } ml-2 transition-colors duration-300`}
                          >
                            {userType === "client"
                              ? booking.craftsmanName
                              : booking.clientName}
                          </h3>
                          <span
                            className={`px-3 py-1 rounded-full text-sm font-medium shadow-sm ${
                              booking.status === "completed"
                                ? "bg-gradient-to-r from-green-100 to-green-200 text-green-800 border border-green-300"
                                : booking.status === "accepted"
                                ? "bg-gradient-to-r from-blue-100 to-blue-200 text-blue-800 border border-blue-300"
                                : booking.status === "pending"
                                ? "bg-gradient-to-r from-yellow-100 to-yellow-200 text-yellow-800 border border-yellow-300"
                                : booking.status === "rejected"
                                ? "bg-gradient-to-r from-red-100 to-red-200 text-red-800 border border-red-300"
                                : booking.status === "cancelled_expired"
                                ? "bg-gradient-to-r from-orange-100 to-orange-200 text-orange-800 border border-orange-300"
                                : "bg-gradient-to-r from-gray-100 to-gray-200 text-gray-800 border border-gray-300"
                            }`}
                          >
                            {booking.status === "completed"
                              ? userType === "client"
                                ? "تم إكمال طلبك"
                                : "تم إكمال الطلب"
                              : booking.status === "accepted"
                              ? userType === "client"
                                ? "تمت الموافقة على طلبك"
                                : "تمت الموافقة على الطلب"
                              : booking.status === "pending"
                              ? userType === "client"
                                ? "طلبك قيد الانتظار"
                                : "طلب جديد بانتظار الرد"
                              : booking.status === "rejected"
                              ? userType === "client"
                                ? "رفض الحرفي طلبك"
                                : "تم رفض الطلب من قبلك"
                              : booking.status === "cancelled_expired"
                              ? userType === "client"
                                ? "تم إلغاء طلبك تلقائياً - الموعد قد انتهى"
                                : "تم إلغاء الطلب تلقائياً - الموعد قد انتهى"
                              : userType === "client"
                              ? "تم إلغاء طلبك"
                              : "تم إلغاء الطلب"}
                          </span>
                        </div>
                        {userType === "client" && (
                          <div className="mb-2">
                            <p
                              className={`text-sm ${
                                darkMode ? "text-indigo-400" : "text-indigo-600"
                              } transition-colors duration-300`}
                            >
                              {booking.craftsmanProfession ||
                                (booking.craftsman?.professions &&
                                  booking.craftsman.professions[0]) ||
                                ""}
                              {(booking.craftsmanSpecialization ||
                                (booking.craftsman?.specializations &&
                                  booking.craftsman.specializations[0])) &&
                                ` - ${booking.craftsmanSpecialization ||
                                  booking.craftsman.specializations[0]}`}
                            </p>
                          </div>
                        )}

                        <div className="mb-4 flex flex-wrap gap-4">
                          <div
                            className={`px-3 py-2 rounded-md shadow-sm ${
                              darkMode
                                ? "bg-gray-700/80 border border-gray-600"
                                : "bg-white/80 border border-indigo-100"
                            } transition-colors duration-300`}
                          >
                            <span
                              className={`block text-sm ${
                                darkMode ? "text-indigo-300" : "text-indigo-500"
                              } transition-colors duration-300`}
                            >
                              التاريخ
                            </span>
                            <span
                              className={`font-medium ${
                                darkMode ? "text-indigo-200" : "text-indigo-700"
                              } transition-colors duration-300`}
                            >
                              {booking.endDate
                                ? `${new Date(booking.date).toLocaleDateString(
                                    "ar-SY"
                                  )} - ${new Date(
                                    booking.endDate
                                  ).toLocaleDateString("ar-SY")}`
                                : new Date(booking.date).toLocaleDateString(
                                    "ar-SY"
                                  )}
                            </span>
                          </div>

                          <div
                            className={`px-3 py-2 rounded-md shadow-sm ${
                              darkMode
                                ? "bg-gray-700/80 border border-gray-600"
                                : "bg-white/80 border border-indigo-100"
                            } transition-colors duration-300`}
                          >
                            <span
                              className={`block text-sm ${
                                darkMode ? "text-indigo-300" : "text-indigo-500"
                              } transition-colors duration-300`}
                            >
                              الوقت
                            </span>
                            <span
                              className={`font-medium ${
                                darkMode ? "text-indigo-200" : "text-indigo-700"
                              } transition-colors duration-300`}
                            >
                              {booking.endTime
                                ? `${formatTime(booking.time)} - ${formatTime(
                                    booking.endTime
                                  )}`
                                : formatTime(booking.time)}
                            </span>
                          </div>
                        </div>

                        <div
                          className={`p-3 rounded-md shadow-sm mb-4 ${
                            darkMode
                              ? "bg-gray-700/80 border border-gray-600"
                              : "bg-white/80 border border-indigo-100"
                          } transition-colors duration-300`}
                        >
                          <span
                            className={`block text-sm mb-1 ${
                              darkMode ? "text-indigo-300" : "text-indigo-500"
                            } transition-colors duration-300`}
                          >
                            وصف المشكلة
                          </span>
                          <div className="max-h-24 overflow-y-auto">
                            <p
                              className={`${
                                darkMode ? "text-indigo-200" : "text-indigo-700"
                              } transition-colors duration-300 break-words`}
                            >
                              {booking.description}
                            </p>
                          </div>
                        </div>

                        {booking.reviewId && (
                          <ReviewSection
                            booking={booking}
                            darkMode={darkMode}
                          />
                        )}
                      </div>

                      <div className="md:self-center mt-2 md:mt-0 flex flex-col gap-2">
                        {/* زر عرض التفاصيل فقط */}
                        <Button
                          variant="primary"
                          className={`text-white flex items-center justify-center gap-2 shadow-md hover:shadow-lg transition-all duration-200 relative overflow-hidden group py-2 px-4 ${
                            darkMode
                              ? "bg-gradient-to-r from-indigo-700 to-purple-800 hover:from-indigo-800 hover:to-purple-900"
                              : "bg-gradient-to-r from-blue-500 to-indigo-500 hover:from-blue-600 hover:to-indigo-600"
                          }`}
                          onClick={() => {
                            setSelectedBooking(booking);
                            setShowDetailsModal(true);
                          }}
                        >
                          <span className="relative z-10">عرض التفاصيل</span>
                          <span className="absolute inset-0 bg-white opacity-20 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700"></span>
                        </Button>
                      </div>
                    </div>
                  </Card>
                </motion.div>
              ))}

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="mt-8 flex flex-col items-center">
                  {/* معلومات الصفحة */}
                  <div
                    className={`mb-3 text-sm ${
                      darkMode ? "text-gray-300" : "text-gray-600"
                    }`}
                  >
                    عرض {(currentPage - 1) * itemsPerPage + 1} إلى{" "}
                    {Math.min(
                      currentPage * itemsPerPage,
                      filteredBookings.length
                    )}{" "}
                    من أصل {filteredBookings.length} طلب
                  </div>

                  {/* أزرار التنقل بين الصفحات */}
                  <div
                    className={`flex items-center gap-2 rounded-lg p-2 ${
                      darkMode ? "bg-gray-800" : "bg-white"
                    } shadow-md`}
                  >
                    {/* زر الصفحة السابقة */}
                    <button
                      onClick={() => goToPage(currentPage - 1)}
                      disabled={currentPage === 1}
                      className={`w-10 h-10 flex items-center justify-center rounded-md transition-colors ${
                        currentPage === 1
                          ? `${
                              darkMode
                                ? "bg-gray-700 text-gray-500"
                                : "bg-gray-100 text-gray-400"
                            } cursor-not-allowed`
                          : `${
                              darkMode
                                ? "bg-gray-700 text-white hover:bg-gray-600"
                                : "bg-indigo-100 text-indigo-700 hover:bg-indigo-200"
                            }`
                      }`}
                      aria-label="الصفحة السابقة"
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-5 w-5"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                      >
                        <path
                          fillRule="evenodd"
                          d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </button>

                    {/* أزرار الصفحات */}
                    {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                      // حساب أرقام الصفحات التي سيتم عرضها
                      let pageNum;
                      if (totalPages <= 5) {
                        // إذا كان إجمالي الصفحات 5 أو أقل، عرض جميع الصفحات
                        pageNum = i + 1;
                      } else if (currentPage <= 3) {
                        // إذا كانت الصفحة الحالية في البداية
                        pageNum = i + 1;
                      } else if (currentPage >= totalPages - 2) {
                        // إذا كانت الصفحة الحالية في النهاية
                        pageNum = totalPages - 4 + i;
                      } else {
                        // إذا كانت الصفحة الحالية في المنتصف
                        pageNum = currentPage - 2 + i;
                      }

                      return (
                        <button
                          key={pageNum}
                          onClick={() => goToPage(pageNum)}
                          className={`w-10 h-10 flex items-center justify-center rounded-md transition-colors ${
                            currentPage === pageNum
                              ? `${
                                  darkMode
                                    ? "bg-indigo-600 text-white"
                                    : "bg-indigo-500 text-white"
                                }`
                              : `${
                                  darkMode
                                    ? "bg-gray-700 text-white hover:bg-gray-600"
                                    : "bg-indigo-100 text-indigo-700 hover:bg-indigo-200"
                                }`
                          }`}
                          aria-label={`الصفحة ${pageNum}`}
                          aria-current={
                            currentPage === pageNum ? "page" : undefined
                          }
                        >
                          {pageNum}
                        </button>
                      );
                    })}

                    {/* زر الصفحة التالية */}
                    <button
                      onClick={() => goToPage(currentPage + 1)}
                      disabled={currentPage === totalPages}
                      className={`w-10 h-10 flex items-center justify-center rounded-md transition-colors ${
                        currentPage === totalPages
                          ? `${
                              darkMode
                                ? "bg-gray-700 text-gray-500"
                                : "bg-gray-100 text-gray-400"
                            } cursor-not-allowed`
                          : `${
                              darkMode
                                ? "bg-gray-700 text-white hover:bg-gray-600"
                                : "bg-indigo-100 text-indigo-700 hover:bg-indigo-200"
                            }`
                      }`}
                      aria-label="الصفحة التالية"
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-5 w-5 transform rotate-180"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                      >
                        <path
                          fillRule="evenodd"
                          d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </button>
                  </div>
                </div>
              )}
            </div>
          ) : (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <Card
                className={`p-8 text-center ${
                  darkMode
                    ? "bg-gray-800 text-gray-200"
                    : "bg-gradient-to-br from-blue-50 to-indigo-100"
                } shadow-md transition-colors duration-300`}
              >
                <div className="flex flex-col items-center">
                  <div
                    className={`w-20 h-20 mb-4 flex items-center justify-center rounded-full ${
                      darkMode ? "bg-gray-700" : "bg-indigo-100"
                    }`}
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className={`h-10 w-10 ${
                        darkMode ? "text-indigo-400" : "text-indigo-500"
                      }`}
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                      />
                    </svg>
                  </div>

                  <h3
                    className={`text-xl font-bold mb-2 ${
                      darkMode ? "text-indigo-300" : "text-indigo-700"
                    }`}
                  >
                    {activeTab === "all"
                      ? "لا توجد طلبات"
                      : activeTab === "pending"
                      ? "لا توجد طلبات قيد الانتظار"
                      : activeTab === "accepted"
                      ? "لا توجد طلبات تمت الموافقة عليها"
                      : activeTab === "completed"
                      ? "لا توجد طلبات مكتملة"
                      : activeTab === "cancelled"
                      ? "لا توجد طلبات ملغية"
                      : "لا توجد طلبات مرفوضة"}
                  </h3>

                  <p
                    className={`${
                      darkMode ? "text-indigo-400" : "text-indigo-600"
                    } mb-6 max-w-md mx-auto transition-colors duration-300`}
                  >
                    {userType === "client"
                      ? "يمكنك البحث عن حرفيين وتقديم طلبات خدمة جديدة من خلال صفحة البحث"
                      : "ستظهر هنا طلبات الخدمة المقدمة إليك من العملاء عندما يتم تقديمها"}
                  </p>

                  {activeTab === "all" && userType === "client" && (
                    <Link to="/search">
                      <Button
                        variant="primary"
                        className={`text-white flex items-center justify-center gap-2 shadow-md hover:shadow-lg transition-all duration-200 relative overflow-hidden group py-2 px-6 ${
                          darkMode
                            ? "bg-gradient-to-r from-indigo-700 to-purple-800 hover:from-indigo-800 hover:to-purple-900"
                            : "bg-gradient-to-r from-blue-500 to-indigo-500 hover:from-blue-600 hover:to-indigo-600"
                        }`}
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-5 w-5"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                          />
                        </svg>
                        <span className="relative z-10">ابحث عن حرفي</span>
                        <span className="absolute inset-0 bg-white opacity-20 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700"></span>
                      </Button>
                    </Link>
                  )}
                </div>
              </Card>
            </motion.div>
          )}
        </div>
      </div>

      {/* Modal de detalles del pedido */}
      {showDetailsModal && selectedBooking && (
        <BookingDetailsModal
          booking={selectedBooking}
          onClose={() => {
            setShowDetailsModal(false);
            setSelectedBooking(null);
            // Actualizar la lista de pedidos después de cerrar el modal
            const userBookings = getUserBookings(user.id, userType);
            setBookings(userBookings);
          }}
          userType={userType}
        />
      )}

      {/* نموذج تعديل الطلب */}
      {showEditModal && selectedBooking && (
        <BookingEditModal
          booking={selectedBooking}
          onClose={() => {
            setShowEditModal(false);
            setSelectedBooking(null);
            // تحديث قائمة الطلبات بعد إغلاق النموذج
            const userBookings = getUserBookings(user.id, userType);
            setBookings(userBookings);
          }}
          onSave={handleSaveBooking}
        />
      )}
    </Layout>
  );
};

export default BookingsPage;
