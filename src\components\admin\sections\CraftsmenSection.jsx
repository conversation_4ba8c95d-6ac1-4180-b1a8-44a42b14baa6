import React, { useState, useEffect } from "react";
import Button from "../../common/Button";
import useThemeStore from "../../../store/themeStore";
import { adminService } from "../../../services/api";
import {
  Briefcase,
  UserPlus,
  Search,
  Filter,
  Edit,
  Trash2,
  Eye,
  Star,
  MapPin,
  EyeOff,
  Phone,
  Calendar,
  Clock,
  PhoneCall,
  Shield,
} from "lucide-react";
import toast from "react-hot-toast";

const CraftsmenSection = () => {
  const darkMode = useThemeStore((state) => state.darkMode);
  const [craftsmen, setCraftsmen] = useState([]);
  const [stats, setStats] = useState({
    totalCraftsmen: 0,
    activeCraftsmen: 0,
    newCraftsmenThisMonth: 0,
    averageRating: 0,
  });
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [filterStatus, setFilterStatus] = useState("all");
  const [selectedCraftsman, setSelectedCraftsman] = useState(null);
  const [showCraftsmanModal, setShowCraftsmanModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showExpiryModal, setShowExpiryModal] = useState(false);
  const [expiryDate, setExpiryDate] = useState("");
  const [editFormData, setEditFormData] = useState({
    isActive: true,
    hideContactInfo: false,
  });

  // Pagination states
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // جلب البيانات عند تحميل المكون
  useEffect(() => {
    fetchCraftsmen();
    fetchStats();
  }, []);

  // جلب قائمة الحرفيين
  const fetchCraftsmen = async () => {
    try {
      setLoading(true);
      const craftsmenData = await adminService.getAllCraftsmen();
      setCraftsmen(craftsmenData);
    } catch (error) {
      console.error("خطأ في جلب الحرفيين:", error);
      if (error.response?.status === 500) {
        toast.error("خطأ في الخادم - يتم العمل على إصلاح المشكلة");
      } else {
        toast.error("فشل في جلب قائمة الحرفيين");
      }
      // عرض قائمة فارغة بدلاً من عدم عرض شيء
      setCraftsmen([]);
    } finally {
      setLoading(false);
    }
  };

  // جلب الإحصائيات
  const fetchStats = async () => {
    try {
      const statsData = await adminService.getDashboardStats();
      setStats({
        totalCraftsmen: statsData.totalCraftsmen,
        activeCraftsmen: statsData.totalCraftsmen, // يمكن تحسين هذا لاحقاً
        newCraftsmenThisMonth: 0, // يمكن إضافة هذا لاحقاً
        averageRating: 4.5, // يمكن حساب هذا من التقييمات
      });
    } catch (error) {
      console.error("خطأ في جلب الإحصائيات:", error);
    }
  };

  // تصفية الحرفيين
  const filteredCraftsmen = craftsmen.filter((craftsman) => {
    // إذا لم يكن هناك مصطلح بحث، اعرض جميع الحرفيين
    const matchesSearch =
      !searchTerm ||
      craftsman.user?.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      craftsman.user?.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      craftsman.user?.phone?.includes(searchTerm) ||
      craftsman.professions?.some((prof) => {
        if (typeof prof === "string") {
          return prof.toLowerCase().includes(searchTerm.toLowerCase());
        }
        return prof.name?.toLowerCase().includes(searchTerm.toLowerCase());
      });

    const matchesFilter =
      filterStatus === "all" ||
      (filterStatus === "active" && craftsman.user?.isActive) ||
      (filterStatus === "inactive" && !craftsman.user?.isActive);

    return matchesSearch && matchesFilter;
  });

  // Pagination logic
  const totalItems = filteredCraftsmen.length;
  const totalPages = Math.ceil(totalItems / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentCraftsmen = filteredCraftsmen.slice(startIndex, endIndex);

  // Reset to first page when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm, filterStatus]);

  // تحديث حالة الحرفي
  const toggleCraftsmanStatus = async (craftsmanId, currentStatus) => {
    try {
      await adminService.updateCraftsman(craftsmanId, {
        isActive: !currentStatus,
      });
      toast.success(
        `تم ${!currentStatus ? "تفعيل" : "إلغاء تفعيل"} الحرفي بنجاح`
      );
      fetchCraftsmen(); // إعادة جلب البيانات
    } catch (error) {
      console.error("خطأ في تحديث حالة الحرفي:", error);
      toast.error("فشل في تحديث حالة الحرفي");
    }
  };

  // تحديث إعدادات إخفاء معلومات الاتصال
  const toggleContactVisibility = async (craftsmanId, currentHideStatus) => {
    try {
      if (!currentHideStatus) {
        // إذا كان سيتم إخفاء المعلومات، اعرض مودال اختيار التاريخ
        setSelectedCraftsman({ _id: craftsmanId });
        setShowExpiryModal(true);
      } else {
        // إذا كان سيتم إظهار المعلومات، قم بالتحديث مباشرة
        await adminService.updateCraftsman(craftsmanId, {
          hideContactInfo: false,
          hideContactInfoExpiry: null,
        });
        toast.success("تم إظهار معلومات الاتصال بنجاح");
        fetchCraftsmen();
      }
    } catch (error) {
      console.error("خطأ في تحديث إعدادات الاتصال:", error);
      toast.error("فشل في تحديث إعدادات الاتصال");
    }
  };

  // تحديث إعدادات الإخفاء مع تاريخ الانتهاء
  const updateContactVisibilityWithExpiry = async () => {
    try {
      const updateData = {
        hideContactInfo: true,
      };

      if (expiryDate) {
        updateData.hideContactInfoExpiry = new Date(expiryDate);
      }

      await adminService.updateCraftsman(selectedCraftsman._id, updateData);

      const expiryText = expiryDate
        ? ` حتى ${new Date(expiryDate).toLocaleDateString("ar-SA")}`
        : " بدون تاريخ انتهاء";

      toast.success(`تم إخفاء معلومات الاتصال${expiryText}`);

      setShowExpiryModal(false);
      setExpiryDate("");
      setSelectedCraftsman(null);
      fetchCraftsmen();
    } catch (error) {
      console.error("خطأ في تحديث إعدادات الاتصال:", error);
      toast.error("فشل في تحديث إعدادات الاتصال");
    }
  };

  // حذف حرفي
  const deleteCraftsman = async (craftsmanId, craftsmanName) => {
    if (window.confirm(`هل أنت متأكد من حذف الحرفي "${craftsmanName}"؟`)) {
      try {
        await adminService.deleteCraftsman(craftsmanId);
        toast.success("تم حذف الحرفي بنجاح");
        fetchCraftsmen(); // إعادة جلب البيانات
      } catch (error) {
        console.error("خطأ في حذف الحرفي:", error);
        toast.error("فشل في حذف الحرفي");
      }
    }
  };

  // عرض تفاصيل الحرفي
  const viewCraftsman = (craftsman) => {
    setSelectedCraftsman(craftsman);
    setShowCraftsmanModal(true);
  };

  // تعديل الحرفي
  const editCraftsman = (craftsman) => {
    setSelectedCraftsman(craftsman);
    setEditFormData({
      isActive: craftsman.user?.isActive || false,
      hideContactInfo: craftsman.hideContactInfo || false,
    });
    setShowEditModal(true);
  };

  // حفظ تعديلات الحرفي
  const saveEditCraftsman = async () => {
    try {
      // تحديث حالة النشاط في جدول المستخدمين
      if (editFormData.isActive !== selectedCraftsman.user?.isActive) {
        await adminService.updateUser(selectedCraftsman.user._id, {
          isActive: editFormData.isActive,
        });
      }

      // تحديث إعدادات إخفاء معلومات الاتصال في جدول الحرفيين
      if (editFormData.hideContactInfo !== selectedCraftsman.hideContactInfo) {
        await adminService.updateCraftsman(selectedCraftsman._id, {
          hideContactInfo: editFormData.hideContactInfo,
          hideContactInfoExpiry: editFormData.hideContactInfo ? null : null,
        });
      }

      toast.success("تم تحديث بيانات الحرفي بنجاح");
      setShowEditModal(false);
      fetchCraftsmen(); // إعادة جلب البيانات
    } catch (error) {
      console.error("خطأ في تحديث بيانات الحرفي:", error);
      toast.error("فشل في تحديث بيانات الحرفي");
    }
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h3
          className={`text-xl font-bold ${
            darkMode ? "text-indigo-300" : "text-indigo-800"
          } relative`}
        >
          <span className="relative z-10">إدارة الحرفيين</span>
          <span
            className={`absolute bottom-0 left-0 right-0 h-2 ${
              darkMode ? "bg-indigo-500" : "bg-indigo-300"
            } opacity-40 transform -rotate-1 z-0`}
          ></span>
        </h3>
        <Button
          variant="primary"
          className={`${
            darkMode
              ? "bg-gradient-to-r from-[#3730A3] to-[#4238C8] hover:from-[#322e92] hover:to-[#3b32b4]"
              : "bg-gradient-to-r from-[#4238C8] to-[#3730A3] hover:from-[#3b32b4] hover:to-[#322e92]"
          } text-white transition-all duration-200 shadow-md hover:shadow-lg relative overflow-hidden group flex items-center gap-2`}
        >
          <UserPlus size={18} />
          <span className="relative z-10">إضافة حرفي جديد</span>
          <span className="absolute inset-0 bg-white opacity-20 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700"></span>
        </Button>
      </div>

      {/* إحصائيات الحرفيين */}
      <div
        className={`mb-6 p-4 rounded-lg ${
          darkMode
            ? "bg-gray-700"
            : "bg-gradient-to-r from-indigo-50 to-blue-50"
        } border ${darkMode ? "border-gray-600" : "border-indigo-100"}`}
      >
        <div className="flex justify-between items-center mb-4">
          <h4
            className={`font-bold ${
              darkMode ? "text-white" : "text-indigo-700"
            }`}
          >
            إحصائيات الحرفيين
          </h4>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div
            className={`p-4 rounded-lg ${
              darkMode
                ? "bg-gray-800"
                : "bg-gradient-to-br from-white to-indigo-50/50"
            } border ${
              darkMode ? "border-gray-700" : "border-indigo-100"
            } shadow-sm`}
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-500 text-sm mb-1">إجمالي الحرفيين</p>
                <p
                  className={`text-2xl font-bold ${
                    darkMode ? "text-indigo-300" : "text-indigo-600"
                  }`}
                >
                  {loading ? "..." : stats.totalCraftsmen}
                </p>
              </div>
              <Briefcase
                className={`${
                  darkMode ? "text-indigo-300" : "text-indigo-600"
                }`}
                size={24}
              />
            </div>
          </div>
          <div
            className={`p-4 rounded-lg ${
              darkMode
                ? "bg-gray-800"
                : "bg-gradient-to-br from-white to-green-50/50"
            } border ${
              darkMode ? "border-gray-700" : "border-green-100"
            } shadow-sm`}
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-500 text-sm mb-1">الحرفيين النشطين</p>
                <p
                  className={`text-2xl font-bold ${
                    darkMode ? "text-green-300" : "text-green-600"
                  }`}
                >
                  {loading ? "..." : stats.activeCraftsmen}
                </p>
              </div>
              <Briefcase
                className={`${darkMode ? "text-green-300" : "text-green-600"}`}
                size={24}
              />
            </div>
          </div>
          <div
            className={`p-4 rounded-lg ${
              darkMode
                ? "bg-gray-800"
                : "bg-gradient-to-br from-white to-purple-50/50"
            } border ${
              darkMode ? "border-gray-700" : "border-purple-100"
            } shadow-sm`}
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-500 text-sm mb-1">حرفيين جدد</p>
                <p
                  className={`text-2xl font-bold ${
                    darkMode ? "text-purple-300" : "text-purple-600"
                  }`}
                >
                  {loading ? "..." : stats.newCraftsmenThisMonth}
                </p>
                <p className="text-xs text-gray-500">هذا الشهر</p>
              </div>
              <UserPlus
                className={`${
                  darkMode ? "text-purple-300" : "text-purple-600"
                }`}
                size={24}
              />
            </div>
          </div>
          <div
            className={`p-4 rounded-lg ${
              darkMode
                ? "bg-gray-800"
                : "bg-gradient-to-br from-white to-yellow-50/50"
            } border ${
              darkMode ? "border-gray-700" : "border-yellow-100"
            } shadow-sm`}
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-500 text-sm mb-1">متوسط التقييم</p>
                <p
                  className={`text-2xl font-bold ${
                    darkMode ? "text-yellow-300" : "text-yellow-600"
                  }`}
                >
                  {loading ? "..." : stats.averageRating}
                </p>
              </div>
              <Star
                className={`${
                  darkMode ? "text-yellow-300" : "text-yellow-600"
                }`}
                size={24}
              />
            </div>
          </div>
        </div>
      </div>

      {/* أدوات البحث والتصفية */}
      <div
        className={`mb-6 p-4 rounded-lg ${
          darkMode ? "bg-gray-700" : "bg-white"
        } border ${darkMode ? "border-gray-600" : "border-gray-200"} shadow-sm`}
      >
        <div className="flex flex-col md:flex-row gap-4">
          {/* شريط البحث */}
          <div className="flex-1 relative">
            <Search
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400"
              size={20}
            />
            <input
              type="text"
              placeholder="البحث بالاسم أو المهنة..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className={`w-full pr-10 pl-4 py-2 rounded-lg border ${
                darkMode
                  ? "bg-gray-800 border-gray-600 text-white placeholder-gray-400"
                  : "bg-gray-50 border-gray-300 text-gray-900 placeholder-gray-500"
              } focus:outline-none focus:ring-2 focus:ring-indigo-500`}
            />
          </div>

          {/* تصفية الحالة */}
          <div className="relative">
            <Filter
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400"
              size={20}
            />
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              className={`pr-10 pl-4 py-2 rounded-lg border ${
                darkMode
                  ? "bg-gray-800 border-gray-600 text-white"
                  : "bg-gray-50 border-gray-300 text-gray-900"
              } focus:outline-none focus:ring-2 focus:ring-indigo-500`}
            >
              <option value="all">جميع الحرفيين</option>
              <option value="active">النشطين</option>
              <option value="inactive">غير النشطين</option>
            </select>
          </div>
        </div>
      </div>

      {/* جدول الحرفيين */}
      <div
        className={`rounded-lg ${
          darkMode ? "bg-gray-700" : "bg-white"
        } border ${
          darkMode ? "border-gray-600" : "border-gray-200"
        } shadow-sm overflow-hidden`}
      >
        <div
          className={`px-6 py-4 border-b ${
            darkMode
              ? "border-gray-600 bg-gray-800"
              : "border-gray-200 bg-gray-50"
          }`}
        >
          <h4
            className={`font-semibold ${
              darkMode ? "text-white" : "text-gray-800"
            }`}
          >
            قائمة الحرفيين ({totalItems} حرفي - عرض {startIndex + 1}-
            {Math.min(endIndex, totalItems)})
          </h4>
        </div>

        {loading ? (
          <div className="flex justify-center items-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
            <span
              className={`mr-3 ${darkMode ? "text-gray-300" : "text-gray-600"}`}
            >
              جاري التحميل...
            </span>
          </div>
        ) : filteredCraftsmen.length === 0 ? (
          <div className="text-center py-12">
            <Briefcase
              className={`mx-auto h-12 w-12 ${
                darkMode ? "text-gray-400" : "text-gray-300"
              } mb-4`}
            />
            <p className={`${darkMode ? "text-gray-300" : "text-gray-600"}`}>
              {searchTerm || filterStatus !== "all"
                ? "لا توجد نتائج مطابقة للبحث"
                : "لا يوجد حرفيين"}
            </p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className={darkMode ? "bg-gray-800" : "bg-gray-50"}>
                <tr>
                  <th
                    className={`px-6 py-3 text-right text-xs font-medium ${
                      darkMode ? "text-gray-300" : "text-gray-500"
                    } uppercase tracking-wider`}
                  >
                    الحرفي
                  </th>
                  <th
                    className={`px-6 py-3 text-right text-xs font-medium ${
                      darkMode ? "text-gray-300" : "text-gray-500"
                    } uppercase tracking-wider`}
                  >
                    المهن
                  </th>
                  <th
                    className={`px-6 py-3 text-right text-xs font-medium ${
                      darkMode ? "text-gray-300" : "text-gray-500"
                    } uppercase tracking-wider`}
                  >
                    التقييم
                  </th>
                  <th
                    className={`px-6 py-3 text-right text-xs font-medium ${
                      darkMode ? "text-gray-300" : "text-gray-500"
                    } uppercase tracking-wider`}
                  >
                    الحالة
                  </th>
                  <th
                    className={`px-6 py-3 text-right text-xs font-medium ${
                      darkMode ? "text-gray-300" : "text-gray-500"
                    } uppercase tracking-wider`}
                  >
                    إعدادات الاتصال
                  </th>
                  <th
                    className={`px-6 py-3 text-right text-xs font-medium ${
                      darkMode ? "text-gray-300" : "text-gray-500"
                    } uppercase tracking-wider`}
                  >
                    الإجراءات
                  </th>
                </tr>
              </thead>
              <tbody
                className={`${darkMode ? "bg-gray-700" : "bg-white"} divide-y ${
                  darkMode ? "divide-gray-600" : "divide-gray-200"
                }`}
              >
                {currentCraftsmen.map((craftsman) => (
                  <tr
                    key={craftsman._id}
                    className={`hover:${
                      darkMode ? "bg-gray-600" : "bg-gray-50"
                    } transition-colors`}
                  >
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div
                          className={`text-sm font-medium ${
                            darkMode ? "text-white" : "text-gray-900"
                          }`}
                        >
                          {craftsman.user?.name}
                        </div>
                        <div
                          className={`text-sm ${
                            darkMode ? "text-gray-300" : "text-gray-500"
                          }`}
                        >
                          {craftsman.user?.phone || craftsman.user?.email}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex flex-wrap gap-1">
                        {craftsman.professions
                          ?.slice(0, 2)
                          .map((profession, index) => (
                            <span
                              key={index}
                              className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800"
                            >
                              {profession}
                            </span>
                          ))}
                        {craftsman.professions?.length > 2 && (
                          <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">
                            +{craftsman.professions.length - 2}
                          </span>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <Star className="text-yellow-400 ml-1" size={16} />
                        <span
                          className={`text-sm ${
                            darkMode ? "text-gray-300" : "text-gray-900"
                          }`}
                        >
                          {craftsman.averageRating || "0.0"}
                        </span>
                        <span
                          className={`text-xs ${
                            darkMode ? "text-gray-400" : "text-gray-500"
                          } mr-1`}
                        >
                          ({craftsman.reviewsCount || 0})
                        </span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span
                        className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          craftsman.user?.isActive
                            ? "bg-green-100 text-green-800"
                            : "bg-red-100 text-red-800"
                        }`}
                      >
                        {craftsman.user?.isActive ? "نشط" : "غير نشط"}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center justify-center gap-2">
                        {/* زر إعدادات الاتصال */}
                        <button
                          onClick={() =>
                            toggleContactVisibility(
                              craftsman._id,
                              craftsman.hideContactInfo
                            )
                          }
                          className={`p-2 rounded-lg transition-colors ${
                            craftsman.hideContactInfo
                              ? darkMode
                                ? "bg-red-700 text-white hover:bg-red-600"
                                : "bg-red-600 text-white hover:bg-red-700"
                              : darkMode
                              ? "bg-green-700 text-white hover:bg-green-600"
                              : "bg-green-600 text-white hover:bg-green-700"
                          }`}
                          title={
                            craftsman.hideContactInfo
                              ? "معلومات الاتصال مخفية - اضغط للإظهار"
                              : "معلومات الاتصال ظاهرة - اضغط للإخفاء"
                          }
                        >
                          {craftsman.hideContactInfo ? (
                            <Shield size={16} />
                          ) : (
                            <PhoneCall size={16} />
                          )}
                        </button>

                        {/* أيقونة التاريخ - تظهر فقط إذا كان هناك تاريخ انتهاء */}
                        {craftsman.hideContactInfo &&
                          craftsman.hideContactInfoExpiry && (
                            <div
                              className={`p-2 rounded-lg ${
                                darkMode
                                  ? "bg-orange-700 text-orange-200"
                                  : "bg-orange-100 text-orange-700"
                              }`}
                              title={`ينتهي في: ${new Date(
                                craftsman.hideContactInfoExpiry
                              ).toLocaleDateString("ar-SA")}`}
                            >
                              <Clock size={16} />
                            </div>
                          )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex items-center gap-2">
                        <button
                          onClick={() => viewCraftsman(craftsman)}
                          className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                          title="عرض التفاصيل"
                        >
                          <Eye size={16} />
                        </button>
                        <button
                          onClick={() => editCraftsman(craftsman)}
                          className="p-2 text-indigo-600 hover:bg-indigo-50 rounded-lg transition-colors"
                          title="تعديل"
                        >
                          <Edit size={16} />
                        </button>
                        <button
                          onClick={() =>
                            deleteCraftsman(craftsman._id, craftsman.user?.name)
                          }
                          className="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                          title="حذف"
                        >
                          <Trash2 size={16} />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Pagination Controls */}
      {!loading && filteredCraftsmen.length > 0 && (
        <div
          className={`mt-6 flex flex-col sm:flex-row justify-between items-center gap-4 p-4 rounded-lg ${
            darkMode ? "bg-gray-700" : "bg-white"
          } border ${
            darkMode ? "border-gray-600" : "border-gray-200"
          } shadow-sm`}
        >
          {/* Items per page selector */}
          <div className="flex items-center gap-2">
            <span
              className={`text-sm ${
                darkMode ? "text-gray-300" : "text-gray-600"
              }`}
            >
              عرض
            </span>
            <select
              value={itemsPerPage}
              onChange={(e) => {
                setItemsPerPage(Number(e.target.value));
                setCurrentPage(1);
              }}
              className={`px-3 py-1 rounded border ${
                darkMode
                  ? "bg-gray-800 border-gray-600 text-white"
                  : "bg-white border-gray-300 text-gray-900"
              } focus:outline-none focus:ring-2 focus:ring-indigo-500`}
            >
              <option value={5}>5</option>
              <option value={10}>10</option>
              <option value={20}>20</option>
              <option value={50}>50</option>
            </select>
            <span
              className={`text-sm ${
                darkMode ? "text-gray-300" : "text-gray-600"
              }`}
            >
              من {totalItems} حرفي
            </span>
          </div>

          {/* Page info */}
          <div
            className={`text-sm ${
              darkMode ? "text-gray-300" : "text-gray-600"
            }`}
          >
            صفحة {currentPage} من {totalPages}
          </div>

          {/* Pagination buttons */}
          <div className="flex items-center gap-1">
            <button
              onClick={() => setCurrentPage(1)}
              disabled={currentPage === 1}
              className={`px-3 py-1 rounded ${
                currentPage === 1
                  ? "bg-gray-200 text-gray-400 cursor-not-allowed"
                  : darkMode
                  ? "bg-gray-600 text-white hover:bg-gray-500"
                  : "bg-gray-100 text-gray-700 hover:bg-gray-200"
              } transition-colors`}
            >
              الأولى
            </button>

            <button
              onClick={() => setCurrentPage(currentPage - 1)}
              disabled={currentPage === 1}
              className={`px-3 py-1 rounded ${
                currentPage === 1
                  ? "bg-gray-200 text-gray-400 cursor-not-allowed"
                  : darkMode
                  ? "bg-gray-600 text-white hover:bg-gray-500"
                  : "bg-gray-100 text-gray-700 hover:bg-gray-200"
              } transition-colors`}
            >
              السابق
            </button>

            {/* Page numbers */}
            {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
              let pageNum;
              if (totalPages <= 5) {
                pageNum = i + 1;
              } else if (currentPage <= 3) {
                pageNum = i + 1;
              } else if (currentPage >= totalPages - 2) {
                pageNum = totalPages - 4 + i;
              } else {
                pageNum = currentPage - 2 + i;
              }

              return (
                <button
                  key={pageNum}
                  onClick={() => setCurrentPage(pageNum)}
                  className={`px-3 py-1 rounded ${
                    currentPage === pageNum
                      ? "bg-indigo-600 text-white"
                      : darkMode
                      ? "bg-gray-600 text-white hover:bg-gray-500"
                      : "bg-gray-100 text-gray-700 hover:bg-gray-200"
                  } transition-colors`}
                >
                  {pageNum}
                </button>
              );
            })}

            <button
              onClick={() => setCurrentPage(currentPage + 1)}
              disabled={currentPage === totalPages}
              className={`px-3 py-1 rounded ${
                currentPage === totalPages
                  ? "bg-gray-200 text-gray-400 cursor-not-allowed"
                  : darkMode
                  ? "bg-gray-600 text-white hover:bg-gray-500"
                  : "bg-gray-100 text-gray-700 hover:bg-gray-200"
              } transition-colors`}
            >
              التالي
            </button>

            <button
              onClick={() => setCurrentPage(totalPages)}
              disabled={currentPage === totalPages}
              className={`px-3 py-1 rounded ${
                currentPage === totalPages
                  ? "bg-gray-200 text-gray-400 cursor-not-allowed"
                  : darkMode
                  ? "bg-gray-600 text-white hover:bg-gray-500"
                  : "bg-gray-100 text-gray-700 hover:bg-gray-200"
              } transition-colors`}
            >
              الأخيرة
            </button>
          </div>
        </div>
      )}

      {/* مودال عرض تفاصيل الحرفي */}
      {showCraftsmanModal && selectedCraftsman && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div
            className={`${
              darkMode ? "bg-gray-800" : "bg-white"
            } rounded-lg p-6 max-w-lg w-full mx-4 shadow-xl`}
          >
            <div className="flex justify-between items-center mb-4">
              <h3
                className={`text-lg font-bold ${
                  darkMode ? "text-white" : "text-gray-900"
                }`}
              >
                تفاصيل الحرفي
              </h3>
              <button
                onClick={() => setShowCraftsmanModal(false)}
                className={`text-gray-500 hover:text-gray-700 ${
                  darkMode ? "hover:text-gray-300" : ""
                }`}
              >
                ✕
              </button>
            </div>
            <div className="space-y-3">
              <div>
                <span className="font-semibold">الاسم: </span>
                <span>{selectedCraftsman.user?.name}</span>
              </div>
              <div>
                <span className="font-semibold">البريد الإلكتروني: </span>
                <span>{selectedCraftsman.user?.email}</span>
              </div>
              {selectedCraftsman.user?.phone && (
                <div>
                  <span className="font-semibold">الهاتف: </span>
                  <span>{selectedCraftsman.user?.phone}</span>
                </div>
              )}
              <div>
                <span className="font-semibold">المهن: </span>
                <div className="flex flex-wrap gap-1 mt-1">
                  {selectedCraftsman.professions?.map((profession, index) => (
                    <span
                      key={index}
                      className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800"
                    >
                      {profession}
                    </span>
                  ))}
                </div>
              </div>
              <div>
                <span className="font-semibold">التقييم: </span>
                <div className="flex items-center">
                  <Star className="text-yellow-400 ml-1" size={16} />
                  <span>{selectedCraftsman.averageRating || "0.0"}</span>
                  <span className="text-gray-500 mr-1">
                    ({selectedCraftsman.reviewsCount || 0} تقييم)
                  </span>
                </div>
              </div>
              <div>
                <span className="font-semibold">الموقع: </span>
                <span>{selectedCraftsman.location?.address || "غير محدد"}</span>
              </div>
              <div>
                <span className="font-semibold">الحالة: </span>
                <span
                  className={`px-2 py-1 rounded text-xs ${
                    selectedCraftsman.user?.isActive
                      ? "bg-green-100 text-green-800"
                      : "bg-red-100 text-red-800"
                  }`}
                >
                  {selectedCraftsman.user?.isActive ? "نشط" : "غير نشط"}
                </span>
              </div>
              <div>
                <span className="font-semibold">إعدادات الاتصال: </span>
                <span
                  className={`px-2 py-1 rounded text-xs ${
                    selectedCraftsman.hideContactInfo
                      ? "bg-red-100 text-red-800"
                      : "bg-green-100 text-green-800"
                  }`}
                >
                  {selectedCraftsman.hideContactInfo ? "مخفية" : "ظاهرة"}
                </span>
                {selectedCraftsman.hideContactInfo &&
                  selectedCraftsman.hideContactInfoExpiry && (
                    <div className="mt-2 flex items-center gap-2">
                      <Clock size={14} className="text-orange-500" />
                      <span className="text-xs text-gray-600">
                        ينتهي في:{" "}
                        {new Date(
                          selectedCraftsman.hideContactInfoExpiry
                        ).toLocaleString("ar-SA")}
                      </span>
                    </div>
                  )}
              </div>
            </div>
            <div className="mt-6 flex justify-end">
              <button
                onClick={() => setShowCraftsmanModal(false)}
                className="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors"
              >
                إغلاق
              </button>
            </div>
          </div>
        </div>
      )}

      {/* مودال تعديل الحرفي */}
      {showEditModal && selectedCraftsman && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div
            className={`${
              darkMode ? "bg-gray-800" : "bg-white"
            } rounded-lg p-6 max-w-md w-full mx-4 shadow-xl`}
          >
            <div className="flex justify-between items-center mb-4">
              <h3
                className={`text-lg font-bold ${
                  darkMode ? "text-white" : "text-gray-900"
                }`}
              >
                تعديل الحرفي
              </h3>
              <button
                onClick={() => setShowEditModal(false)}
                className={`text-gray-500 hover:text-gray-700 ${
                  darkMode ? "hover:text-gray-300" : ""
                }`}
              >
                ✕
              </button>
            </div>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-1">الاسم</label>
                <input
                  type="text"
                  defaultValue={selectedCraftsman.user?.name}
                  className={`w-full px-3 py-2 border rounded-lg ${
                    darkMode
                      ? "bg-gray-700 border-gray-600 text-white"
                      : "bg-white border-gray-300"
                  }`}
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">
                  البريد الإلكتروني
                </label>
                <input
                  type="email"
                  defaultValue={selectedCraftsman.user?.email}
                  className={`w-full px-3 py-2 border rounded-lg ${
                    darkMode
                      ? "bg-gray-700 border-gray-600 text-white"
                      : "bg-white border-gray-300"
                  }`}
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">الحالة</label>
                <select
                  value={editFormData.isActive}
                  onChange={(e) =>
                    setEditFormData({
                      ...editFormData,
                      isActive: e.target.value === "true",
                    })
                  }
                  className={`w-full px-3 py-2 border rounded-lg ${
                    darkMode
                      ? "bg-gray-700 border-gray-600 text-white"
                      : "bg-white border-gray-300"
                  }`}
                >
                  <option value={true}>نشط</option>
                  <option value={false}>غير نشط</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">
                  إعدادات الاتصال
                </label>
                <select
                  value={editFormData.hideContactInfo}
                  onChange={(e) =>
                    setEditFormData({
                      ...editFormData,
                      hideContactInfo: e.target.value === "true",
                    })
                  }
                  className={`w-full px-3 py-2 border rounded-lg ${
                    darkMode
                      ? "bg-gray-700 border-gray-600 text-white"
                      : "bg-white border-gray-300"
                  }`}
                >
                  <option value={false}>إظهار معلومات الاتصال</option>
                  <option value={true}>إخفاء معلومات الاتصال</option>
                </select>
              </div>
            </div>
            <div className="mt-6 flex justify-end gap-2">
              <button
                onClick={() => setShowEditModal(false)}
                className="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors"
              >
                إلغاء
              </button>
              <button
                onClick={saveEditCraftsman}
                className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
              >
                حفظ
              </button>
            </div>
          </div>
        </div>
      )}

      {/* مودال اختيار تاريخ انتهاء الصلاحية */}
      {showExpiryModal && selectedCraftsman && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div
            className={`${
              darkMode ? "bg-gray-800" : "bg-white"
            } rounded-lg p-6 max-w-md w-full mx-4 shadow-xl`}
          >
            <div className="flex justify-between items-center mb-4">
              <h3
                className={`text-lg font-bold ${
                  darkMode ? "text-white" : "text-gray-900"
                } flex items-center gap-2`}
              >
                <Shield className="text-red-500" size={20} />
                إخفاء معلومات الاتصال
              </h3>
              <button
                onClick={() => {
                  setShowExpiryModal(false);
                  setExpiryDate("");
                  setSelectedCraftsman(null);
                }}
                className={`text-gray-500 hover:text-gray-700 ${
                  darkMode ? "hover:text-gray-300" : ""
                }`}
              >
                ✕
              </button>
            </div>

            <div className="space-y-4">
              <div
                className={`p-4 rounded-lg ${
                  darkMode ? "bg-gray-700" : "bg-yellow-50"
                } border ${darkMode ? "border-gray-600" : "border-yellow-200"}`}
              >
                <div className="flex items-center gap-2 mb-2">
                  <Clock className="text-orange-500" size={16} />
                  <span
                    className={`text-sm font-medium ${
                      darkMode ? "text-yellow-300" : "text-orange-700"
                    }`}
                  >
                    تحديد تاريخ انتهاء الإخفاء (اختياري)
                  </span>
                </div>
                <p
                  className={`text-xs ${
                    darkMode ? "text-gray-300" : "text-gray-600"
                  }`}
                >
                  إذا لم تحدد تاريخ، ستبقى المعلومات مخفية حتى تقوم بإظهارها
                  يدوياً
                </p>
              </div>

              <div>
                <label
                  className={`block text-sm font-medium mb-2 ${
                    darkMode ? "text-gray-300" : "text-gray-700"
                  }`}
                >
                  تاريخ انتهاء الإخفاء
                </label>
                <input
                  type="datetime-local"
                  value={expiryDate}
                  onChange={(e) => setExpiryDate(e.target.value)}
                  min={new Date().toISOString().slice(0, 16)}
                  className={`w-full px-3 py-2 border rounded-lg ${
                    darkMode
                      ? "bg-gray-700 border-gray-600 text-white"
                      : "bg-white border-gray-300"
                  } focus:outline-none focus:ring-2 focus:ring-indigo-500`}
                />
              </div>

              <div
                className={`p-3 rounded-lg ${
                  darkMode ? "bg-red-900/30" : "bg-red-50"
                } border ${darkMode ? "border-red-700/50" : "border-red-200"}`}
              >
                <p
                  className={`text-sm ${
                    darkMode ? "text-red-300" : "text-red-700"
                  }`}
                >
                  <strong>تنبيه:</strong> سيتم إخفاء جميع معلومات الاتصال وأزرار
                  الحجز للحرفي
                </p>
              </div>
            </div>

            <div className="mt-6 flex justify-end gap-2">
              <button
                onClick={() => {
                  setShowExpiryModal(false);
                  setExpiryDate("");
                  setSelectedCraftsman(null);
                }}
                className={`px-4 py-2 rounded-lg transition-colors ${
                  darkMode
                    ? "bg-gray-600 text-white hover:bg-gray-500"
                    : "bg-gray-500 text-white hover:bg-gray-600"
                }`}
              >
                إلغاء
              </button>
              <button
                onClick={updateContactVisibilityWithExpiry}
                className={`px-4 py-2 rounded-lg transition-colors flex items-center gap-2 ${
                  darkMode
                    ? "bg-red-700 text-white hover:bg-red-600"
                    : "bg-red-600 text-white hover:bg-red-700"
                }`}
              >
                <Shield size={16} />
                إخفاء المعلومات
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CraftsmenSection;
