import React, { useState, useRef, useEffect } from "react";
import { Link, useLocation, useNavigate } from "react-router-dom";
import {
  User,
  LogOut,
  Settings,
  Search,
  Home,
  BookUser,
  Moon,
  Sun,
  Globe,
  X,
  Menu,
} from "lucide-react";
import useThemeStore from "../../store/themeStore";
import useTranslation from "../../hooks/useTranslation";
import NotificationBell from "../notifications/NotificationBell";
import NotificationDropdown from "../notifications/NotificationDropdown";
import useSiteSettingsStore from "../../store/siteSettingsStore";

const Header = ({ user, onLogout }) => {
  const darkMode = useThemeStore((state) => state.darkMode);
  const toggleDarkMode = useThemeStore((state) => state.toggleDarkMode);
  const location = useLocation();
  const navigate = useNavigate();
  const isLoggedIn = !!user;
  const { t, isRTL } = useTranslation();

  // جلب إعدادات الموقع
  const { settings, fetchSettings } = useSiteSettingsStore();

  // حالة إظهار/إخفاء قائمة الإشعارات
  const [showNotifications, setShowNotifications] = useState(false);

  // حالة إظهار/إخفاء القائمة المنسدلة للهاتف المحمول
  const [showMobileMenu, setShowMobileMenu] = useState(false);

  // حالة إظهار/إخفاء الهيدر عند التمرير
  const [isHeaderVisible, setIsHeaderVisible] = useState(true);

  // تخزين موضع التمرير السابق
  const [prevScrollPos, setPrevScrollPos] = useState(0);

  // مرجع للقائمة المنسدلة وزر الإشعارات
  const notificationRef = useRef(null);

  // مرجع للقائمة المنسدلة للهاتف المحمول
  const mobileMenuRef = useRef(null);

  // جلب إعدادات الموقع عند تحميل المكون
  useEffect(() => {
    fetchSettings();
  }, [fetchSettings]);

  // إضافة مستمع للنقرات خارج قائمة الإشعارات
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        notificationRef.current &&
        !notificationRef.current.contains(event.target)
      ) {
        setShowNotifications(false);
      }
    };

    // إضافة مستمع الأحداث عند ظهور القائمة
    if (showNotifications) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    // إزالة مستمع الأحداث عند إخفاء القائمة أو إزالة المكون
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [showNotifications]);

  // إضافة مستمع للنقرات خارج القائمة المنسدلة للهاتف المحمول
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        mobileMenuRef.current &&
        !mobileMenuRef.current.contains(event.target)
      ) {
        setShowMobileMenu(false);
      }
    };

    // إضافة مستمع الأحداث عند ظهور القائمة
    if (showMobileMenu) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    // إزالة مستمع الأحداث عند إخفاء القائمة أو إزالة المكون
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [showMobileMenu]);

  // إضافة مستمع لحدث التمرير لإظهار/إخفاء الهيدر
  useEffect(() => {
    // دالة معالجة حدث التمرير
    const handleScroll = () => {
      // الحصول على موضع التمرير الحالي
      const currentScrollPos = window.scrollY;

      // التحقق مما إذا كان المستخدم يستخدم شاشة صغيرة (جوال)
      const isMobile = window.innerWidth < 768;

      // إذا كان المستخدم يستخدم شاشة كبيرة، اجعل الهيدر مرئيًا دائمًا
      if (!isMobile) {
        setIsHeaderVisible(true);
        return;
      }

      // إذا كان المستخدم في أعلى الصفحة، اجعل الهيدر مرئيًا دائمًا
      if (currentScrollPos < 100) {
        setIsHeaderVisible(true);
        setPrevScrollPos(currentScrollPos);
        return;
      }

      // إظهار الهيدر عند التمرير للأعلى وإخفاؤه عند التمرير للأسفل
      const isScrollingUp = prevScrollPos > currentScrollPos;

      setIsHeaderVisible(isScrollingUp);
      setPrevScrollPos(currentScrollPos);
    };

    // إضافة مستمع لحدث التمرير
    window.addEventListener("scroll", handleScroll);

    // إزالة مستمع الحدث عند إزالة المكون
    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, [prevScrollPos]);

  return (
    <header
      className={`${
        darkMode
          ? "bg-gray-900"
          : "bg-gradient-to-r from-indigo-700 to-blue-900"
      } text-white py-4 shadow-xl shadow-black/20 dark:shadow-black/40 fixed top-0 left-0 right-0 z-[100] transition-all duration-300 ${
        isHeaderVisible ? "translate-y-0" : "-translate-y-full"
      } md:relative md:translate-y-0`}
    >
      <style>{`
        @keyframes pulse {
          0% {
            box-shadow: 0 0 0 0 rgba(99, 102, 241, 0.4);
          }
          70% {
            box-shadow: 0 0 0 10px rgba(99, 102, 241, 0);
          }
          100% {
            box-shadow: 0 0 0 0 rgba(99, 102, 241, 0);
          }
        }
      `}</style>
      <div className="container mx-auto px-4 flex justify-between items-center">
        <div className="flex items-center gap-4">
          <Link
            to="/"
            className="flex items-center text-2xl font-bold relative group"
          >
            <div>
              {(() => {
                // استخدام اسم الموقع من الإعدادات أو القيمة الافتراضية
                const siteName = settings?.siteName || "JobScope";

                // تقسيم الاسم إلى جزأين للتلوين
                let part1 = "Job";
                let part2 = "Scope";

                if (siteName) {
                  const nameParts = siteName.split(" ");
                  if (nameParts.length > 1) {
                    part1 = nameParts[0];
                    part2 = nameParts.slice(1).join(" ");
                  } else if (nameParts[0]) {
                    const singleWord = nameParts[0];
                    const midPoint = Math.ceil(singleWord.length / 2);
                    part1 = singleWord.substring(0, midPoint);
                    part2 = singleWord.substring(midPoint);
                  }
                }

                return (
                  <>
                    <span className="bg-clip-text text-transparent bg-gradient-to-r from-yellow-400 to-orange-500">
                      {part1}
                    </span>
                    <span className="bg-clip-text text-transparent bg-gradient-to-r from-indigo-400 to-blue-500">
                      {part2}
                    </span>
                  </>
                );
              })()}
              <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-yellow-400 to-blue-500 transition-all duration-300 group-hover:w-full"></span>
            </div>
          </Link>
        </div>

        <nav className="hidden md:flex items-center space-x-6 space-x-reverse">
          <button
            onClick={toggleDarkMode}
            className={`flex items-center justify-center gap-1 px-3 py-1.5 rounded-full ${
              darkMode
                ? "bg-yellow-500/20 text-yellow-300 hover:bg-yellow-500/30"
                : "bg-indigo-500/20 text-indigo-100 hover:bg-indigo-500/30"
            } transition-colors duration-200`}
            aria-label={darkMode ? t("lightMode") : t("darkMode")}
          >
            {darkMode ? (
              <>
                <Sun size={18} className="animate-spin-slow" />
                <span className="text-sm font-medium">{t("lightMode")}</span>
              </>
            ) : (
              <>
                <Moon size={18} className="animate-pulse" />
                <span className="text-sm font-medium">{t("darkMode")}</span>
              </>
            )}
          </button>
          {isLoggedIn ? (
            <>
              <Link
                to="/home"
                className={`flex items-center gap-1 transition-colors duration-200 ${
                  location.pathname === "/home"
                    ? "text-yellow-300 font-medium"
                    : "text-white hover:text-yellow-200"
                }`}
              >
                <Home size={18} />
                <span>{t("home")}</span>
              </Link>
              <Link
                to="/search"
                className={`flex items-center gap-1 transition-colors duration-200 ${
                  location.pathname === "/search"
                    ? "text-yellow-300 font-medium"
                    : "text-white hover:text-yellow-200"
                }`}
              >
                <Search size={18} />
                <span>{t("search")}</span>
              </Link>
              <Link
                to="/profile/my"
                className={`flex items-center gap-1 transition-colors duration-200 ${
                  location.pathname === "/profile/my"
                    ? "text-yellow-300 font-medium"
                    : "text-white hover:text-yellow-200"
                }`}
              >
                <User size={18} />
                <span>{t("profile")}</span>
              </Link>
              <Link
                to="/bookings"
                className={`flex items-center gap-1 transition-colors duration-200 ${
                  location.pathname === "/bookings"
                    ? "text-yellow-300 font-medium"
                    : "text-white hover:text-yellow-200"
                }`}
              >
                <BookUser size={18} />
                <span>{t("myRequests")}</span>
              </Link>
              <Link
                to="/settings"
                className={`flex items-center gap-1 transition-colors duration-200 ${
                  location.pathname === "/settings"
                    ? "text-yellow-300 font-medium"
                    : "text-white hover:text-yellow-200"
                }`}
              >
                <Settings size={18} />
                <span>{t("settings")}</span>
              </Link>

              {/* زر الإشعارات */}
              <div className="relative" ref={notificationRef}>
                <NotificationBell
                  onClick={() => setShowNotifications(!showNotifications)}
                />
                <NotificationDropdown
                  isOpen={showNotifications}
                  onClose={() => setShowNotifications(false)}
                />
              </div>

              <button
                onClick={() => {
                  onLogout();
                  navigate("/logout-redirect");
                }}
                className="flex items-center gap-1 text-white hover:text-white transition-all duration-200 hover:scale-105 transform border border-transparent bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 px-3 py-2 rounded-md relative overflow-hidden group shadow-md hover:shadow-lg"
              >
                <span className="relative z-10 flex items-center gap-1">
                  <LogOut size={18} />
                  <span>{t("logout")}</span>
                </span>
                <span className="absolute inset-0 bg-white opacity-20 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700"></span>
              </button>
            </>
          ) : (
            <>
              <Link
                to="/login"
                className="bg-gradient-to-r from-indigo-500 to-indigo-700 text-white px-4 py-2 rounded-md hover:from-indigo-600 hover:to-indigo-800 transition-all duration-200 hover:shadow-md relative overflow-hidden group"
                style={{ animation: "pulse 2s infinite" }}
              >
                <span className="relative text-white z-10">تسجيل الدخول</span>
                <span className="absolute inset-0 bg-white opacity-20 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700"></span>
              </Link>
              <Link
                to="/register/craftsman"
                className="bg-gradient-to-r from-yellow-400 to-orange-500 text-white px-4 py-2 rounded-md hover:from-yellow-600 hover:to-orange-600 transition-all duration-200 hover:shadow-md relative overflow-hidden group"
                style={{ animation: "pulse 2s infinite" }}
              >
                <span className="relative text-white z-10">تسجيل كحرفي</span>
                <span className="absolute inset-0 bg-white opacity-20 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700"></span>
              </Link>
            </>
          )}
        </nav>

        <div className="md:hidden" ref={mobileMenuRef}>
          {/* زر القائمة المنسدلة للهاتف المحمول */}
          <button
            onClick={() => setShowMobileMenu(!showMobileMenu)}
            className="text-white hover:text-yellow-300 transition-colors duration-200 p-2 rounded-md hover:bg-indigo-800/50"
          >
            {showMobileMenu ? <X size={24} /> : <Menu size={24} />}
          </button>

          {/* القائمة المنسدلة للهاتف المحمول */}
          {showMobileMenu && (
            <div className="absolute top-full left-0 right-0 mt-1 z-50">
              <div
                className={`${
                  darkMode
                    ? "bg-gray-800 border-gray-700"
                    : "bg-indigo-700 border-indigo-600"
                } border-t shadow-lg transition-all duration-300 py-3`}
              >
                <div className="container mx-auto px-4">
                  <div className="flex flex-col space-y-3">
                    {/* زر تبديل الوضع المظلم */}
                    <button
                      onClick={toggleDarkMode}
                      className={`flex items-center justify-between w-full px-4 py-2 rounded-md ${
                        darkMode
                          ? "bg-yellow-500/20 text-yellow-300 hover:bg-yellow-500/30"
                          : "bg-indigo-500/20 text-indigo-100 hover:bg-indigo-500/30"
                      } transition-colors duration-200`}
                    >
                      <span className="text-sm font-medium">
                        {darkMode ? t("lightMode") : t("darkMode")}
                      </span>
                      {darkMode ? (
                        <Sun size={18} className="animate-spin-slow" />
                      ) : (
                        <Moon size={18} className="animate-pulse" />
                      )}
                    </button>

                    {/* روابط التنقل حسب حالة تسجيل الدخول */}
                    {isLoggedIn ? (
                      <>
                        <Link
                          to="/home"
                          className={`flex items-center justify-between px-4 py-2 rounded-md ${
                            location.pathname === "/home"
                              ? "bg-indigo-600/50 text-yellow-300 font-medium"
                              : "text-white hover:bg-indigo-600/30"
                          } transition-colors duration-200`}
                          onClick={() => setShowMobileMenu(false)}
                        >
                          <span>{t("home")}</span>
                          <Home size={18} />
                        </Link>
                        <Link
                          to="/search"
                          className={`flex items-center justify-between px-4 py-2 rounded-md ${
                            location.pathname === "/search"
                              ? "bg-indigo-600/50 text-yellow-300 font-medium"
                              : "text-white hover:bg-indigo-600/30"
                          } transition-colors duration-200`}
                          onClick={() => setShowMobileMenu(false)}
                        >
                          <span>{t("search")}</span>
                          <Search size={18} />
                        </Link>
                        <Link
                          to="/profile/my"
                          className={`flex items-center justify-between px-4 py-2 rounded-md ${
                            location.pathname === "/profile/my"
                              ? "bg-indigo-600/50 text-yellow-300 font-medium"
                              : "text-white hover:bg-indigo-600/30"
                          } transition-colors duration-200`}
                          onClick={() => setShowMobileMenu(false)}
                        >
                          <span>{t("profile")}</span>
                          <User size={18} />
                        </Link>
                        <Link
                          to="/bookings"
                          className={`flex items-center justify-between px-4 py-2 rounded-md ${
                            location.pathname === "/bookings"
                              ? "bg-indigo-600/50 text-yellow-300 font-medium"
                              : "text-white hover:bg-indigo-600/30"
                          } transition-colors duration-200`}
                          onClick={() => setShowMobileMenu(false)}
                        >
                          <span>{t("myRequests")}</span>
                          <BookUser size={18} />
                        </Link>
                        <Link
                          to="/settings"
                          className={`flex items-center justify-between px-4 py-2 rounded-md ${
                            location.pathname === "/settings"
                              ? "bg-indigo-600/50 text-yellow-300 font-medium"
                              : "text-white hover:bg-indigo-600/30"
                          } transition-colors duration-200`}
                          onClick={() => setShowMobileMenu(false)}
                        >
                          <span>{t("settings")}</span>
                          <Settings size={18} />
                        </Link>
                        <button
                          onClick={() => {
                            setShowMobileMenu(false);
                            onLogout();
                            navigate("/logout-redirect");
                          }}
                          className="flex items-center justify-between px-4 py-2 rounded-md bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white transition-colors duration-200"
                        >
                          <span>{t("logout")}</span>
                          <LogOut size={18} />
                        </button>
                      </>
                    ) : (
                      <>
                        <Link
                          to="/login"
                          className="flex items-center justify-between px-4 py-2 rounded-md bg-gradient-to-r from-indigo-500 to-indigo-700 text-white hover:from-indigo-600 hover:to-indigo-800 transition-colors duration-200"
                          onClick={() => setShowMobileMenu(false)}
                        >
                          <span>تسجيل الدخول</span>
                          <User size={18} />
                        </Link>
                        <Link
                          to="/register/client"
                          className="flex items-center justify-between px-4 py-2 rounded-md bg-gradient-to-r from-blue-500 to-blue-700 text-white hover:from-blue-600 hover:to-blue-800 transition-colors duration-200"
                          onClick={() => setShowMobileMenu(false)}
                        >
                          <span>تسجيل كعميل</span>
                          <User size={18} />
                        </Link>
                        <Link
                          to="/register/craftsman"
                          className="flex items-center justify-between px-4 py-2 rounded-md bg-gradient-to-r from-yellow-400 to-orange-500 text-white hover:from-yellow-600 hover:to-orange-600 transition-colors duration-200"
                          onClick={() => setShowMobileMenu(false)}
                        >
                          <span>تسجيل كحرفي</span>
                          <User size={18} />
                        </Link>
                      </>
                    )}
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </header>
  );
};

export default Header;
