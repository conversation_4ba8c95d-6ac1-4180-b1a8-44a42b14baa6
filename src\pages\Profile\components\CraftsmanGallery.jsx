import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { Image, X, ZoomIn } from "lucide-react";
import Card from "../../../components/common/Card";
import LazyImage from "../../../components/common/LazyImage";
import { SERVER_URL } from "../../../services/config";

const CraftsmanGallery = ({ craftsman, darkMode }) => {
  const [selectedImage, setSelectedImage] = useState(null);
  const [processedGallery, setProcessedGallery] = useState([]);

  // معالجة مسارات الصور عند تغيير بيانات الحرفي
  useEffect(() => {
    if (craftsman && craftsman.gallery && Array.isArray(craftsman.gallery)) {
      console.log("CraftsmanGallery - معرض الصور الأصلي:", craftsman.gallery);

      const processed = craftsman.gallery
        .filter(imagePath => imagePath && imagePath !== "undefined" && imagePath !== "null") // تصفية المسارات الفارغة أو غير الصالحة
        .map(imagePath => {
          // إذا كان المسار يبدأ بـ http أو https، نستخدمه كما هو
          if (imagePath.startsWith('http')) {
            console.log("CraftsmanGallery - استخدام URL كامل:", imagePath);
            return imagePath;
          }

          // إذا كان المسار يبدأ بـ /uploads، نضيف عنوان الخادم
          if (imagePath.startsWith('/uploads')) {
            const fullUrl = `${SERVER_URL}${imagePath}`;
            console.log("CraftsmanGallery - تحويل مسار uploads إلى URL كامل:", fullUrl);
            return fullUrl;
          }

          // إذا كان المسار لا يبدأ بـ /، نضيف / قبل إضافة عنوان الخادم
          const fullUrl = `${SERVER_URL}${imagePath.startsWith('/') ? '' : '/'}${imagePath}`;
          console.log("CraftsmanGallery - تحويل مسار نسبي إلى URL كامل:", fullUrl);
          return fullUrl;
        });

      console.log("CraftsmanGallery - معرض الصور بعد المعالجة:", processed);
      setProcessedGallery(processed);
    } else if (craftsman && craftsman.workGallery && Array.isArray(craftsman.workGallery)) {
      // محاولة استخدام workGallery إذا كان gallery غير متوفر
      console.log("CraftsmanGallery - استخدام workGallery بدلاً من gallery:", craftsman.workGallery);

      const processed = craftsman.workGallery
        .filter(imagePath => imagePath && imagePath !== "undefined" && imagePath !== "null") // تصفية المسارات الفارغة أو غير الصالحة
        .map(imagePath => {
          // إذا كان المسار يبدأ بـ http أو https، نستخدمه كما هو
          if (imagePath.startsWith('http')) {
            return imagePath;
          }

          // إذا كان المسار يبدأ بـ /uploads، نضيف عنوان الخادم
          if (imagePath.startsWith('/uploads')) {
            return `${SERVER_URL}${imagePath}`;
          }

          // إذا كان المسار لا يبدأ بـ /، نضيف / قبل إضافة عنوان الخادم
          return `${SERVER_URL}${imagePath.startsWith('/') ? '' : '/'}${imagePath}`;
        });

      console.log("CraftsmanGallery - معرض الصور بعد المعالجة (من workGallery):", processed);
      setProcessedGallery(processed);
    } else {
      console.log("CraftsmanGallery - لا توجد صور في المعرض");
      setProcessedGallery([]);
    }
  }, [craftsman]);

  const openImageModal = (image) => {
    setSelectedImage(image);
  };

  const closeImageModal = () => {
    setSelectedImage(null);
  };

  return (
    <>
      <Card
        className={`p-6 mb-6 rounded-xl ${
          darkMode
            ? "bg-gray-800 text-gray-200 border border-gray-700"
            : "bg-gradient-to-br from-blue-50/80 to-indigo-50 border border-indigo-100"
        } shadow-lg transition-colors duration-300`}
      >
        <div className="flex items-center mb-5">
          <div
            className={`p-2 rounded-full mr-3 ${
              darkMode ? "bg-indigo-900/50" : "bg-indigo-100"
            }`}
          >
            <Image
              size={22}
              className={`${
                darkMode ? "text-indigo-400" : "text-indigo-500"
              } transition-colors duration-300`}
            />
          </div>
          <h2
            className={`text-xl font-bold ${
              darkMode ? "text-indigo-300" : "text-indigo-800"
            } relative inline-block transition-colors duration-300`}
          >
            <span className="relative z-10">معرض الأعمال</span>
            <span
              className={`absolute bottom-0 left-0 right-0 h-2 ${
                darkMode ? "bg-indigo-500" : "bg-indigo-300"
              } opacity-40 transform -rotate-1 z-0`}
            ></span>
          </h2>
        </div>

        {processedGallery.length > 0 ? (
          <div
            className={`rounded-lg p-4 ${
              darkMode
                ? "bg-gray-700/50 border border-gray-600"
                : "bg-gradient-to-r from-blue-100/80 to-indigo-100/80 border border-indigo-100"
            } transition-colors duration-300`}
          >
            <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
              {processedGallery.map((image, index) => (
                <motion.div
                  key={index}
                  whileHover={{ scale: 1.03 }}
                  className="relative group h-40 rounded-lg overflow-hidden shadow-md"
                >
                  <LazyImage
                    src={image}
                    alt={`عمل ${index + 1}`}
                    className="w-full h-full object-cover"
                    placeholderClassName="w-full h-full bg-gray-200 animate-pulse"
                  />
                  <div
                    className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end justify-center p-3 cursor-pointer"
                    onClick={() => openImageModal(image)}
                  >
                    <button
                      className={`p-2 rounded-full ${
                        darkMode ? "bg-indigo-900/80" : "bg-indigo-600/80"
                      } text-white hover:bg-indigo-700 transition-colors duration-200`}
                      onClick={(e) => {
                        e.stopPropagation(); // منع انتشار الحدث للعنصر الأب
                        openImageModal(image);
                      }}
                    >
                      <ZoomIn size={18} />
                    </button>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        ) : (
          <div
            className={`flex flex-col items-center justify-center p-8 rounded-lg ${
              darkMode
                ? "bg-gray-700/50 border border-gray-600"
                : "bg-gradient-to-r from-blue-100/80 to-indigo-100/80 border border-indigo-100"
            } transition-colors duration-300`}
          >
            <div
              className={`w-16 h-16 rounded-full flex items-center justify-center mb-4 ${
                darkMode ? "bg-gray-600" : "bg-indigo-100"
              }`}
            >
              <Image
                size={32}
                className={`${
                  darkMode ? "text-indigo-400" : "text-indigo-500"
                } opacity-70`}
              />
            </div>
            <p
              className={`text-center font-medium ${
                darkMode ? "text-indigo-300" : "text-indigo-700"
              } transition-colors duration-300 mb-2`}
            >
              لا توجد صور في المعرض
            </p>
            <p
              className={`text-center text-sm ${
                darkMode ? "text-gray-400" : "text-gray-500"
              }`}
            >
              لم يقم الحرفي بإضافة أي صور لأعماله بعد
            </p>
          </div>
        )}
      </Card>

      {/* Modal for image preview */}
      {selectedImage && (
        <div
          className="fixed inset-0 z-50 flex items-center justify-center bg-black/80"
          onClick={closeImageModal}
        >
          <div className="relative max-w-4xl max-h-[90vh] w-[90vw] overflow-hidden">
            <button
              className="absolute top-4 right-4 p-2 rounded-full bg-black/50 text-white hover:bg-black/70 transition-colors duration-200"
              onClick={closeImageModal}
            >
              <X size={24} />
            </button>
            <LazyImage
              src={selectedImage}
              alt="معرض الأعمال"
              className="max-w-full max-h-[90vh] object-contain"
              placeholderClassName="max-w-full max-h-[90vh] bg-gray-200 animate-pulse"
            />
          </div>
        </div>
      )}
    </>
  );
};

export default CraftsmanGallery;
