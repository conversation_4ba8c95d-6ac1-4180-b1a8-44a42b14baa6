@tailwind base;
@tailwind components;
@tailwind utilities;

/* ======= متغيرات الألوان الأساسية ======= */
:root {
  /* ألوان الهوية البصرية */
  --color-primary-light: #ecf3ff;
  --color-primary: #3730a3;
  --color-primary-dark: #2a2580;
  --color-secondary: #4238c8;
  --color-secondary-dark: #3730a3;

  /* ألوان التفاعل */
  --color-hover-light: #e6edff;
  --color-hover: #4238c8;
  --color-focus-ring: rgba(66, 56, 200, 0.2);

  /* ألوان الحالة */
  --color-success: #10b981;
  --color-warning: #f59e0b;
  --color-error: #ef4444;
  --color-info: #3b82f6;

  /* ألوان الوضع المظلم */
  --dark-bg-primary: #111827;
  --dark-bg-secondary: #1f2937;
  --dark-text-primary: #f9fafb;
  --dark-text-secondary: #d1d5db;
  --dark-border: #374151;
  --dark-hover: #4b5563;
}

@layer base {
  html {
    scroll-behavior: smooth;
  }

  body {
    font-family: "Cairo", "Tajawal", system-ui, sans-serif;
    transition: background-color 0.3s ease, color 0.3s ease;
  }

  /* ===== اتجاه اللغة ===== */
  html[dir="rtl"] body {
    direction: rtl;
  }

  html[dir="ltr"] body {
    direction: ltr;
  }

  /* ===== تنسيقات الوضع المظلم ===== */
  .dark-mode {
    color-scheme: dark;
  }

  .dark-mode .card {
    @apply bg-gray-800 text-gray-200;
  }

  .dark-mode .input {
    @apply bg-gray-700 border-gray-600 text-white focus:ring-indigo-500 focus:border-indigo-500;
  }

  .dark-mode select {
    @apply bg-gray-700 border-gray-600 text-white;
  }

  .dark-mode button.btn-secondary {
    @apply bg-gray-700 text-gray-200 border-gray-600 hover:bg-gray-600;
  }

  .dark-mode h1,
  .dark-mode h2,
  .dark-mode h3 {
    @apply text-white;
  }

  .dark-mode a {
    @apply text-indigo-400 hover:text-indigo-300;
  }

  .dark-mode .text-indigo-800 {
    @apply text-indigo-300;
  }

  .dark-mode .text-gray-600 {
    @apply text-gray-400;
  }

  .dark-mode .text-gray-500 {
    @apply text-gray-400;
  }
}

@layer components {
  /* ===== الأزرار ===== */
  .btn {
    @apply px-4 py-2 rounded-md font-medium transition-all duration-200;
  }

  .btn-primary {
    @apply bg-[#4238C8] text-white hover:bg-[#3730A3];
  }

  .btn-secondary {
    @apply bg-white text-[#3730A3] border border-[#3730A3] hover:bg-[#ECF3FF];
  }

  .btn-success {
    @apply bg-green-500 text-white hover:bg-green-600;
  }

  .btn-danger {
    @apply bg-red-500 text-white hover:bg-red-600;
  }

  .btn-warning {
    @apply bg-yellow-500 text-white hover:bg-yellow-600;
  }

  /* ===== حقول الإدخال ===== */
  .input {
    @apply w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-200 focus:border-indigo-500;
  }

  .input-sm {
    @apply px-3 py-1 text-sm;
  }

  .input-lg {
    @apply px-5 py-3 text-lg;
  }

  /* ===== البطاقات ===== */
  .card {
    @apply bg-white rounded-lg shadow-md p-4;
  }

  .card-hover {
    @apply transition-all duration-200 hover:shadow-lg;
  }

  /* ===== تأثيرات الهوفر ===== */
  .hover-effect {
    @apply relative overflow-hidden;
  }

  .hover-effect::after {
    content: "";
    @apply absolute inset-0 bg-white opacity-0 transform -skew-x-12 -translate-x-full transition-all duration-500;
  }

  .hover-effect:hover::after {
    @apply translate-x-full opacity-20;
  }

  /* ===== تأثيرات النص ===== */
  .text-gradient {
    @apply bg-clip-text text-transparent bg-gradient-to-r from-[#3730A3] to-[#4238C8];
  }

  .text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  /* ===== تدرجات الألوان ===== */
  .bg-gradient-primary {
    @apply bg-gradient-to-r from-[#3730A3] to-[#4238C8];
  }

  .bg-gradient-secondary {
    @apply bg-gradient-to-r from-indigo-500 to-blue-600;
  }

  .bg-gradient-light {
    @apply bg-gradient-to-br from-[#ECF3FF] to-indigo-100;
  }

  /* ===== تأثيرات الإشعارات ===== */
  .notification-badge {
    @apply absolute -top-1 -right-1 inline-flex items-center justify-center w-5 h-5 text-xs font-bold text-indigo-900 rounded-full bg-yellow-300 shadow-md border border-indigo-900/20;
    animation: notification-pulse 1.5s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }
}

/* ===== تأثير نبض الإشعارات ===== */
@keyframes notification-pulse {
  0%,
  100% {
    opacity: 1;
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(251, 191, 36, 0.7);
  }
  50% {
    opacity: 0.9;
    transform: scale(1.1);
    box-shadow: 0 0 0 5px rgba(251, 191, 36, 0);
  }
}

/* ===== تنسيقات الإشعارات المنبثقة (Toast) ===== */
@keyframes animate-toast {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-toast {
  animation: animate-toast 0.3s ease-out forwards;
}

#toast-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  pointer-events: none;
}

#toast-container > div {
  pointer-events: auto;
  transition: opacity 0.3s ease;
}

/* ===== أنماط الخرائط التفاعلية ===== */
.advanced-map-container {
  position: relative;
  width: 100%;
}

.leaflet-container {
  font-family: "Cairo", sans-serif;
}

.custom-marker-cluster {
  background: transparent;
  border: none;
}

.cluster-icon {
  width: 40px;
  height: 40px;
  background-color: #3730a3;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

/* أنماط الأيقونات المخصصة للمهن */
.custom-profession-icon {
  background: transparent;
  border: none;
}

.custom-profession-icon div {
  transition: transform 0.2s ease-in-out;
}

.custom-profession-icon:hover div {
  transform: scale(1.1);
}

.craftsman-popup .leaflet-popup-content-wrapper {
  border-radius: 0.5rem;
  padding: 0;
  overflow: hidden;
  background-color: white;
  color: #1f2937;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.craftsman-popup .leaflet-popup-content {
  margin: 0;
  width: 300px !important; /* زيادة العرض */
  color: #1f2937;
  padding: 0;
}

.craftsman-popup .leaflet-popup-tip {
  background-color: white;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.craftsman-popup-container {
  width: 100%;
  height: 100%;
}

/* أنماط محتوى النافذة المنبثقة */
.craftsman-popup-content {
  padding: 16px; /* زيادة التباعد الداخلي */
  font-family: "Cairo", sans-serif;
  background-color: white;
  color: #1f2937;
  direction: rtl;
  max-height: none !important; /* إزالة الحد الأقصى للارتفاع */
  overflow: visible !important; /* التأكد من عدم قطع المحتوى */
}

/* تحسين ظهور النافذة المنبثقة على الأجهزة المحمولة */
@media (max-width: 768px) {
  .craftsman-popup .leaflet-popup-content {
    width: 280px !important;
  }

  .craftsman-popup-content {
    padding: 12px;
  }
}

.craftsman-popup-name {
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  gap: 6px;
  color: #1f2937;
}

.craftsman-popup-profession {
  font-size: 14px;
  margin-bottom: 8px;
  color: #4b5563;
  font-weight: 500;
}

.craftsman-popup-rating {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-bottom: 8px;
  color: #4b5563;
}

.craftsman-popup-rating svg {
  color: #f59e0b;
  flex-shrink: 0;
}

.craftsman-popup-address {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  margin-bottom: 8px;
  color: #6b7280;
}

.craftsman-popup-address svg {
  flex-shrink: 0;
}

.craftsman-popup-phone {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 13px;
  margin-bottom: 12px;
  color: #4b5563;
}

.craftsman-popup-phone svg {
  flex-shrink: 0;
}

.craftsman-popup-button {
  width: 100%;
  padding: 8px 12px;
  background-color: #3730a3;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  text-align: center;
  margin-top: 4px;
  position: relative;
  overflow: hidden;
}

.craftsman-popup-button::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.1);
  transform: translateX(-100%);
  transition: transform 0.3s ease;
}

.craftsman-popup-button:hover {
  background-color: #4238c8;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.craftsman-popup-button:hover::before {
  transform: translateX(0);
}

.craftsman-popup-button:active {
  transform: translateY(1px);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* أنماط الوضع المظلم للنافذة المنبثقة */
.dark-mode .craftsman-popup .leaflet-popup-content-wrapper {
  background-color: #1f2937;
  color: #f3f4f6;
}

.dark-mode .craftsman-popup .leaflet-popup-tip {
  background-color: #1f2937;
}

.dark-mode .craftsman-popup-content {
  background-color: #1f2937;
}

.dark-mode .craftsman-popup-name {
  color: #f3f4f6;
}

.dark-mode .craftsman-popup-profession {
  color: #d1d5db;
}

.dark-mode .craftsman-popup-rating {
  color: #d1d5db;
}

.dark-mode .craftsman-popup-address {
  color: #9ca3af;
}

.dark-mode .craftsman-popup-phone {
  color: #d1d5db;
}

/* تحسينات إضافية للنافذة المنبثقة */
.leaflet-popup {
  z-index: 1000 !important; /* زيادة z-index لضمان ظهور النافذة فوق جميع العناصر الأخرى */
}

.leaflet-popup-content-wrapper {
  transform: translateY(-5px); /* تحريك النافذة للأعلى قليلاً */
}

/* تحسين ظهور زر الإغلاق */
.craftsman-popup a.leaflet-popup-close-button {
  color: #666;
  font-size: 18px;
  padding: 5px;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  top: 5px;
  right: 5px;
  transition: all 0.2s;
}

.craftsman-popup a.leaflet-popup-close-button:hover {
  color: #333;
  background-color: rgba(255, 255, 255, 1);
}

.dark-mode .craftsman-popup a.leaflet-popup-close-button {
  background-color: rgba(31, 41, 55, 0.8);
  color: #d1d5db;
}

.dark-mode .craftsman-popup a.leaflet-popup-close-button:hover {
  background-color: rgba(31, 41, 55, 1);
  color: #f3f4f6;
}

/* أنماط للعلامات المعطلة للمستخدمين غير المسجلين */
.disabled-marker {
  cursor: not-allowed !important;
  opacity: 0.7;
  pointer-events: none;
}

/* عند تمرير المؤشر فوق العلامة المعطلة */
.disabled-marker:hover {
  cursor: not-allowed !important;
}

/* تعطيل التفاعل مع العلامات للمستخدمين غير المسجلين */
.disabled-marker .leaflet-marker-icon {
  cursor: not-allowed !important;
}

/* تغيير شكل المؤشر عند تمرير المؤشر فوق العلامات للمستخدمين غير المسجلين */
.leaflet-container:has(.disabled-marker) .leaflet-marker-icon,
.unregistered-user-map .leaflet-marker-icon {
  cursor: not-allowed !important;
}

/* إضافة تأثير بصري للإشارة إلى أن المستخدم غير مسجل */
.unregistered-user-map .leaflet-marker-icon {
  filter: grayscale(30%);
  opacity: 0.8;
  transition: all 0.2s;
}

.unregistered-user-map .leaflet-marker-icon:hover {
  filter: grayscale(0%);
  opacity: 0.9;
}

/* تنويه للمستخدمين غير المسجلين */
.login-notice {
  background: linear-gradient(
    to left,
    rgba(59, 130, 246, 0.05),
    rgba(99, 102, 241, 0.1)
  );
  border: 1px solid rgba(99, 102, 241, 0.2);
  border-right: 4px solid #4f46e5;
  padding: 16px 20px;
  border-radius: 8px;
  margin-top: 20px;
  margin-bottom: 20px;
  display: flex;
  align-items: flex-start;
  gap: 16px;
  direction: rtl;
  text-align: right;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.05),
    0 2px 4px -1px rgba(0, 0, 0, 0.03);
  transition: all 0.3s ease;
}

.login-notice:hover {
  box-shadow: 0 6px 12px -2px rgba(0, 0, 0, 0.08),
    0 3px 6px -2px rgba(0, 0, 0, 0.05);
  transform: translateY(-1px);
}

.login-notice-icon {
  flex-shrink: 0;
  color: #4f46e5;
  background-color: rgba(79, 70, 229, 0.1);
  padding: 10px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 44px;
  height: 44px;
  margin-top: 4px;
}

.login-notice-content {
  flex: 1;
}

.login-notice-title {
  font-weight: 700;
  color: #4338ca;
  margin-bottom: 6px;
  font-size: 16px;
  display: flex;
  align-items: center;
}

.login-notice-text {
  color: #4b5563;
  font-size: 14px;
  line-height: 1.5;
}

.login-notice-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(to right, #4f46e5, #6366f1);
  color: white;
  font-weight: 600;
  padding: 6px 12px;
  border-radius: 5px;
  margin-top: 12px;
  text-decoration: none;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(79, 70, 229, 0.3);
  font-size: 14px;
}

.login-notice-button:hover {
  background: linear-gradient(to right, #4338ca, #4f46e5);
  box-shadow: 0 2px 6px rgba(79, 70, 229, 0.4);
}

.login-notice-button:active {
  transform: translateY(1px);
  box-shadow: 0 1px 2px rgba(79, 70, 229, 0.4);
}

.login-notice-button::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: 0.6s;
}

.login-notice-button:hover::before {
  left: 100%;
}

/* حاوية الأزرار */
.login-notice-buttons {
  display: flex;
  gap: 12px;
  margin-top: 12px;
  justify-content: flex-end; /* إرسال الأزرار إلى اليمين */
  flex-wrap: wrap;
  width: 50%; /* لضمان احتلال العرض الكامل للحاوية */
}

/* زر تسجيل الدخول */
.login-notice-button.login-button {
  background: linear-gradient(to right, #4f46e5, #6366f1);
  flex: 1;
  min-width: 120px;
}

.login-notice-button.login-button:hover {
  background: linear-gradient(to right, #4338ca, #4f46e5);
}

/* زر التسجيل كطالب خدمة */
.login-notice-button.register-button {
  background: linear-gradient(to right, #10b981, #059669);
  flex: 1;
  min-width: 140px;
}

.login-notice-button.register-button:hover {
  background: linear-gradient(to right, #059669, #047857);
  box-shadow: 0 2px 6px rgba(16, 185, 129, 0.4);
}

/* تحسين الاستجابة للشاشات الصغيرة */
@media (max-width: 640px) {
  .login-notice-buttons {
    flex-direction: column;
    align-items: flex-end; /* توسيط الأزرار لليمين */
    width: 100%;
  }

  .login-notice-button.login-button,
  .login-notice-button.register-button {
    flex: none;
    width: 100%;
    min-width: auto;
  }
}

/* تنويه للمستخدمين غير المسجلين - الوضع المظلم */
.dark-mode .login-notice {
  background: linear-gradient(
    to left,
    rgba(79, 70, 229, 0.1),
    rgba(99, 102, 241, 0.15)
  );
  border: 1px solid rgba(99, 102, 241, 0.3);
  border-right: 4px solid #6366f1;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.2),
    0 2px 4px -1px rgba(0, 0, 0, 0.1);
}

.dark-mode .login-notice-icon {
  color: #818cf8;
  background-color: rgba(99, 102, 241, 0.2);
}

.dark-mode .login-notice-title {
  color: #a5b4fc;
}

.dark-mode .login-notice-text {
  color: #d1d5db;
}

.dark-mode .login-notice-button {
  background: linear-gradient(to right, #3d3e95, #3d4483);
  box-shadow: 0 2px 4px rgba(99, 102, 241, 0.4);
  color: #c2caf1;
}

.dark-mode .login-notice-button:hover {
  background: linear-gradient(to right, #4f46e5, #6366f1);
  box-shadow: 0 4px 8px rgba(99, 102, 241, 0.5);
}

/* أزرار الوضع المظلم - تسجيل الدخول */
.dark-mode .login-notice-button.login-button {
  background: linear-gradient(to right, #3d3e95, #3d4483);
  color: #c2caf1;
}

.dark-mode .login-notice-button.login-button:hover {
  background: linear-gradient(to right, #4f46e5, #6366f1);
  box-shadow: 0 2px 6px rgba(99, 102, 241, 0.5);
}

/* أزرار الوضع المظلم - التسجيل كطالب خدمة */
.dark-mode .login-notice-button.register-button {
  background: linear-gradient(to right, #047857, #065f46);
  color: #a7f3d0;
}

.dark-mode .login-notice-button.register-button:hover {
  background: linear-gradient(to right, #10b981, #059669);
  box-shadow: 0 2px 6px rgba(16, 185, 129, 0.5);
}

/* ===== أنماط فلتر الخريطة ===== */
.map-filter-container {
  padding: 1rem;
  border-radius: 0.5rem;
  margin-bottom: 1rem;
}

.map-filter-group {
  margin-bottom: 1rem;
}

.map-filter-title {
  font-weight: bold;
  margin-bottom: 0.5rem;
}

.map-filter-options {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.map-filter-option {
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s;
}

.map-filter-option.active {
  background-color: #3730a3;
  color: white;
}

/* ===== أنماط مناطق الخدمة المخصصة ===== */
.service-area-container {
  margin-top: 1rem;
}

.service-area-title {
  font-weight: bold;
  margin-bottom: 0.5rem;
}

.service-area-description {
  font-size: 0.875rem;
  margin-bottom: 1rem;
  opacity: 0.8;
}

.neighborhood-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 0.5rem;
  margin-top: 1rem;
}

.neighborhood-item {
  padding: 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s;
  text-align: center;
}

.neighborhood-item.selected {
  background-color: #3730a3;
  color: white;
}
