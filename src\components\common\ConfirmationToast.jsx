import React, { useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { X, AlertTriangle, Check, XCircle } from "lucide-react";
import useThemeStore from "../../store/themeStore";

const ConfirmationToast = ({
  isOpen,
  onClose,
  onConfirm,
  title = "تأكيد العملية",
  message = "هل أنت متأكد من إتمام هذه العملية؟",
  confirmText = "تأكيد",
  cancelText = "إلغاء",
  type = "warning", // warning, success, error
}) => {
  const darkMode = useThemeStore((state) => state.darkMode);

  // إغلاق التوست عند الضغط على Escape
  useEffect(() => {
    const handleEscape = (e) => {
      if (e.key === "Escape" && isOpen) {
        onClose();
      }
    };

    window.addEventListener("keydown", handleEscape);
    return () => window.removeEventListener("keydown", handleEscape);
  }, [isOpen, onClose]);

  // تحديد الألوان والأيقونات بناءً على نوع التوست
  const getToastStyles = () => {
    switch (type) {
      case "success":
        return {
          icon: <Check size={28} className={darkMode ? "text-green-400" : "text-green-500"} />,
        };
      case "error":
        return {
          icon: <XCircle size={28} className={darkMode ? "text-red-400" : "text-red-500"} />,
        };
      case "warning":
      default:
        return {
          icon: <AlertTriangle size={28} className={darkMode ? "text-indigo-400" : "text-indigo-600"} />,
        };
    }
  };

  const toastStyles = getToastStyles();

  return (
    <AnimatePresence>
      {isOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
          {/* خلفية معتمة */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className={`absolute inset-0 ${
              darkMode ? "bg-gray-900/80" : "bg-gray-800/50"
            } backdrop-blur-sm`}
            onClick={onClose}
          />

          {/* التوست */}
          <motion.div
            initial={{ scale: 0.9, opacity: 0, y: 20 }}
            animate={{ scale: 1, opacity: 1, y: 0 }}
            exit={{ scale: 0.9, opacity: 0, y: 20 }}
            transition={{ type: "spring", damping: 25, stiffness: 300 }}
            className={`relative w-full max-w-md p-6 rounded-xl shadow-2xl border ${
              darkMode
                ? "bg-gradient-to-br from-gray-800 to-indigo-900/80 border-indigo-800/50"
                : "bg-gradient-to-br from-blue-50/90 to-indigo-100/90 border-indigo-200"
            } ${
              darkMode ? "shadow-black/30" : "shadow-gray-300/50"
            }`}
          >
            {/* زر الإغلاق */}
            <button
              onClick={onClose}
              className={`absolute top-3 left-3 p-1 rounded-full transition-colors ${
                darkMode
                  ? "text-gray-400 hover:text-white hover:bg-gray-700/50"
                  : "text-gray-500 hover:text-gray-700 hover:bg-gray-200/50"
              }`}
            >
              <X size={18} />
            </button>

            <div className="flex items-start">
              <div className="flex-shrink-0 mr-4">{toastStyles.icon}</div>
              <div className="flex-1">
                <h3
                  className={`text-lg font-bold mb-2 ${
                    darkMode ? "text-indigo-300" : "text-indigo-700"
                  }`}
                >
                  {title}
                </h3>
                <p className={`mb-6 ${
                  darkMode ? "text-gray-300" : "text-gray-600"
                }`}>{message}</p>

                <div className="flex justify-end gap-3">
                  <button
                    onClick={onClose}
                    className={`py-2 px-4 rounded-lg font-medium text-white shadow-md hover:shadow-lg transition-all duration-200 ${
                      darkMode
                        ? "bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800"
                        : "bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700"
                    }`}
                  >
                    {cancelText}
                  </button>
                  <button
                    onClick={() => {
                      onConfirm();
                      onClose();
                    }}
                    className={`py-2 px-5 rounded-lg font-medium text-white shadow-md hover:shadow-lg transition-all duration-200 ${
                      darkMode
                        ? "bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800"
                        : "bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700"
                    }`}
                  >
                    {confirmText}
                  </button>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      )}
    </AnimatePresence>
  );
};

export default ConfirmationToast;
