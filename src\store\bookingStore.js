import { create } from "zustand";
import useReviewStore from "./reviewStore";
import notificationService from "../services/notificationService";
import useUserStore from "./userStore";
import useNotificationStore from "./notificationStore";
import { bookingService } from "../services/api"; // استيراد خدمة الحجوزات

const useBookingStore = create((set, get) => ({
  bookings: [], // بدء بقائمة فارغة بدون بيانات وهمية
  loading: false,
  error: null,

  // تحميل الحجوزات من الخادم
  fetchBookings: async () => {
    set({ loading: true, error: null });
    try {
      // محاولة الحصول على الحجوزات من الخادم
      const response = await bookingService.getMyBookings();

      // طباعة معلومات مفصلة عن هيكل الحجوزات للتصحيح
      if (response && response.length > 0) {
        const firstBooking = response[0];
      }

      // تحميل جميع التقييمات لضمان عرضها في بطاقات الحجز
      try {
        await useReviewStore.getState().fetchAllReviews();
      } catch (reviewError) {
        console.error("خطأ في تحميل التقييمات:", reviewError);
      }

      // تطبيع هيكل الحجوزات قبل تخزينها في المتجر
      const normalizedBookings = (response || []).map((booking) => {
        // التأكد من وجود معرفات العميل والحرفي بشكل صريح
        return {
          ...booking,
          // تخزين المعرف الرئيسي
          id: booking._id || booking.id,
          // تخزين معرفات العميل والحرفي بشكل صريح
          clientId:
            booking.clientId || booking.client?._id || booking.client?.id,
          craftsmanId:
            booking.craftsmanId ||
            booking.craftsman?._id ||
            booking.craftsman?.id,
        };
      });

      set({
        bookings: normalizedBookings,
        loading: false,
      });

      return response;
    } catch (error) {
      console.error("خطأ في تحميل الحجوزات:", error);
      set({
        error: error.message || "حدث خطأ أثناء تحميل الحجوزات",
        loading: false,
      });
      return [];
    }
  },

  // Add a new booking
  addBooking: async (booking) => {
    set({ loading: true, error: null });

    try {
      // التأكد من وجود اسم الحرفي والعميل
      const craftsmanName = booking.craftsmanName || "حرفي";
      const clientName = booking.clientName || "عميل";

      // التحقق من نوع المستخدم - نستخدم المتغير المحلي بدلاً من الحصول عليه من المتجر
      // هذا يمنع مشكلة تسجيل الخروج التلقائي
      const currentUserType = "client"; // نفترض أن المستخدم هو عميل لأنه يقوم بالحجز

      // للتأكد، نتحقق من المتجر أيضاً
      const storeUserType = useUserStore.getState().userType;

      // إعداد بيانات الطلب للإرسال إلى الخادم
      const bookingData = {
        craftsmanId: booking.craftsmanId,
        date: booking.date,
        time: booking.time,
        endDate: booking.endDate || booking.date, // استخدام تاريخ البداية إذا لم يتم تحديد تاريخ النهاية
        endTime: booking.endTime || booking.time, // استخدام وقت البداية إذا لم يتم تحديد وقت النهاية
        description: booking.description,
        location: booking.location || {
          address: "غير محدد",
          lat: 0,
          lng: 0,
        },
      };

      // إرسال الطلب إلى الخادم
      const serverBooking = await bookingService.createBooking(bookingData);

      // إنشاء طلب محلي باستخدام البيانات من الخادم
      const newBooking = {
        ...booking,
        ...serverBooking,
        // تخزين معرفات العميل والحرفي بشكل صريح
        clientId:
          serverBooking.client?._id ||
          serverBooking.client?.id ||
          booking.clientId,
        craftsmanId:
          serverBooking.craftsman?._id ||
          serverBooking.craftsman?.id ||
          booking.craftsmanId,
        // تخزين أسماء العميل والحرفي
        craftsmanName:
          serverBooking.craftsmanName ||
          (serverBooking.craftsman &&
            serverBooking.craftsman.user &&
            serverBooking.craftsman.user.name) ||
          craftsmanName,
        clientName:
          serverBooking.clientName ||
          (serverBooking.client && serverBooking.client.name) ||
          clientName,
        // تخزين معلومات المهنة والتخصص
        craftsmanProfession:
          serverBooking.craftsman?.professions?.[0] ||
          booking.craftsmanProfession,
        craftsmanSpecialization:
          serverBooking.craftsman?.specializations?.[0] ||
          booking.craftsmanSpecialization,
        // تخزين المعرف الرئيسي والحالة وتاريخ الإنشاء
        id: serverBooking._id || serverBooking.id,
        status: serverBooking.status || "pending",
        createdAt: serverBooking.createdAt || new Date().toISOString(),
      };

      // تحديث القائمة المحلية
      set((state) => ({
        bookings: [...state.bookings, newBooking],
        loading: false,
      }));

      // إضافة إشعار للحجز الجديد
      const userType = useUserStore.getState().userType;

      // إضافة تأخير بسيط قبل إنشاء الإشعار لضمان ظهوره بعد إغلاق الإشعار المنبثق
      setTimeout(() => {
        notificationService.createBookingNotification(newBooking, userType);

        // تحديث الإشعارات من الخادم بعد إنشاء الطلب
        setTimeout(() => {
          useNotificationStore.getState().fetchNotifications();
        }, 2000);
      }, 3500);

      return newBooking;
    } catch (error) {
      console.error("خطأ في إضافة الطلب:", error);

      set({
        loading: false,
        error: error.message || "حدث خطأ أثناء إنشاء الطلب",
      });

      throw error;
    }
  },

  // تحديث حالة الطلب
  updateBookingStatus: async (bookingId, status) => {
    set({ loading: true, error: null });

    try {
      // طباعة معلومات تفصيلية للتصحيح

      // الحصول على الطلب الحالي
      const currentBooking = get().getBookingById(bookingId);

      if (!currentBooking) {
        console.error("لم يتم العثور على الطلب:", bookingId);
        set({
          loading: false,
          error: "لم يتم العثور على الطلب",
        });
        return null;
      }

      // تحديث حالة الطلب في الخادم أو محلياً
      let updatedServerBooking;
      try {
        // إرسال طلب تحديث الحالة إلى الخادم
        updatedServerBooking = await bookingService.updateBookingStatus(
          bookingId,
          status
        );

        // طباعة الاستجابة من الخادم للتصحيح

        if (updatedServerBooking.updatedLocally) {
          // دمج البيانات المحلية مع البيانات الحالية
          updatedServerBooking = {
            ...currentBooking,
            ...updatedServerBooking,
            status,
          };
        } else {
        }
      } catch (error) {
        console.error("خطأ في تحديث حالة الطلب في الخادم:", error);
        console.error("تفاصيل الخطأ:", {
          message: error.message,
          response: error.response?.data,
          status: error.response?.status,
        });

        // في حالة حدوث خطأ، نستمر بالتحديث المحلي
        updatedServerBooking = {
          ...currentBooking,
          status,
          updatedLocally: true,
        };
      }

      // تحديث الطلب في القائمة المحلية
      let updatedBooking = null;

      set((state) => {
        const updatedBookings = state.bookings.map((booking) => {
          // مقارنة المعرفات بمرونة أكبر
          const bookingIdMatches =
            String(booking.id) === String(bookingId) ||
            String(booking._id) === String(bookingId);

          if (bookingIdMatches) {
            // التأكد من الحفاظ على اسم الحرفي والعميل
            updatedBooking = {
              ...booking,
              ...updatedServerBooking,
              // تخزين المعرف الرئيسي
              id:
                booking.id ||
                booking._id ||
                updatedServerBooking._id ||
                updatedServerBooking.id,
              // تخزين معرفات العميل والحرفي بشكل صريح
              clientId:
                booking.clientId ||
                updatedServerBooking.client?._id ||
                updatedServerBooking.clientId ||
                booking.client?._id,
              craftsmanId:
                booking.craftsmanId ||
                updatedServerBooking.craftsman?._id ||
                updatedServerBooking.craftsmanId ||
                booking.craftsman?._id,
              // تحديث الحالة
              status,
              // الحفاظ على أسماء العميل والحرفي
              craftsmanName:
                booking.craftsmanName ||
                updatedServerBooking.craftsmanName ||
                (updatedServerBooking.craftsman &&
                  updatedServerBooking.craftsman.user &&
                  updatedServerBooking.craftsman.user.name) ||
                (booking.craftsman &&
                  booking.craftsman.user &&
                  booking.craftsman.user.name) ||
                "حرفي",
              clientName:
                booking.clientName ||
                updatedServerBooking.clientName ||
                (updatedServerBooking.client &&
                  updatedServerBooking.client.name) ||
                (booking.client && booking.client.name) ||
                "عميل",
              updatedAt: new Date().toISOString(),
            };

            return updatedBooking;
          }
          return booking;
        });

        return {
          bookings: updatedBookings,
          loading: false,
        };
      });

      // إضافة إشعار لتغيير الحالة
      if (updatedBooking) {
        const userType = useUserStore.getState().userType;

        // إضافة تأخير بسيط لضمان ظهور الإشعار بعد تحديث الحالة
        setTimeout(() => {
          notificationService.createStatusChangeNotification(
            updatedBooking,
            status,
            userType
          );

          // تحديث الإشعارات من الخادم بعد تغيير الحالة
          setTimeout(() => {
            useNotificationStore.getState().fetchNotifications();
          }, 2000);
        }, 1000);
      }

      return updatedBooking;
    } catch (error) {
      console.error("خطأ في تحديث حالة الطلب:", error);

      set({
        loading: false,
        error: error.message || "حدث خطأ أثناء تحديث حالة الطلب",
      });

      return null;
    }
  },

  // Update booking details
  updateBooking: async (bookingId, updatedData) => {
    set({ loading: true, error: null });

    try {
      // الحصول على الطلب الحالي
      const currentBooking = get().getBookingById(bookingId);

      if (!currentBooking) {
        console.error("لم يتم العثور على الطلب:", bookingId);
        set({
          loading: false,
          error: "لم يتم العثور على الطلب",
        });
        return null;
      }

      // إعداد البيانات للإرسال إلى الخادم
      const serverData = {
        date: updatedData.date,
        time: updatedData.time,
        endDate: updatedData.endDate || updatedData.date, // استخدام تاريخ البداية إذا لم يتم تحديد تاريخ النهاية
        endTime: updatedData.endTime || updatedData.time, // استخدام وقت البداية إذا لم يتم تحديد وقت النهاية
        description: updatedData.description,
      };

      // تحديث الطلب في الخادم
      const updatedServerBooking = await bookingService.updateBooking(
        bookingId,
        serverData
      );

      // التأكد من الحفاظ على اسم الحرفي والعميل
      const updatedBookingData = {
        ...updatedData,
        ...updatedServerBooking,
        craftsmanName:
          currentBooking.craftsmanName ||
          updatedServerBooking.craftsmanName ||
          (updatedServerBooking.craftsman &&
            updatedServerBooking.craftsman.user &&
            updatedServerBooking.craftsman.user.name) ||
          (currentBooking.craftsman &&
            currentBooking.craftsman.user &&
            currentBooking.craftsman.user.name) ||
          "حرفي",
        clientName:
          currentBooking.clientName ||
          updatedServerBooking.clientName ||
          (updatedServerBooking.client && updatedServerBooking.client.name) ||
          (currentBooking.client && currentBooking.client.name) ||
          "عميل",
        updatedAt: new Date().toISOString(),
      };

      // طباعة معلومات التصحيح

      // تحديث الطلب في القائمة المحلية
      set((state) => ({
        bookings: state.bookings.map((booking) => {
          // مقارنة المعرفات بمرونة أكبر
          const bookingIdMatches =
            String(booking.id) === String(bookingId) ||
            String(booking._id) === String(bookingId);

          if (bookingIdMatches) {
            // تحديث الطلب مع الحفاظ على المعرفات
            return {
              ...booking,
              ...updatedBookingData,
              // تخزين المعرف الرئيسي
              id:
                booking.id ||
                booking._id ||
                updatedBookingData.id ||
                updatedBookingData._id,
              // تخزين معرفات العميل والحرفي بشكل صريح
              clientId:
                booking.clientId ||
                updatedBookingData.clientId ||
                booking.client?._id,
              craftsmanId:
                booking.craftsmanId ||
                updatedBookingData.craftsmanId ||
                booking.craftsman?._id,
            };
          }
          return booking;
        }),
        loading: false,
      }));

      return updatedBookingData;
    } catch (error) {
      console.error("خطأ في تحديث الطلب:", error);

      set({
        loading: false,
        error: error.message || "حدث خطأ أثناء تحديث الطلب",
      });

      return null;
    }
  },

  // Check if booking can be edited (within 5 minutes of creation)
  canEditBooking: (bookingId) => {
    const booking = get().getBookingById(bookingId);
    if (!booking) return false;

    const createdAt = new Date(booking.createdAt);
    const now = new Date();
    const diffInMinutes = (now - createdAt) / (1000 * 60);

    return diffInMinutes <= 5;
  },

  // Add review to a booking
  addReview: (bookingId, review) => {
    // Obtener la reserva para acceder a los IDs del artesano y cliente
    const booking = get().getBookingById(bookingId);

    if (!booking) {
      console.error("No se encontró la reserva con ID:", bookingId);
      return null;
    }

    // Verificar si la reserva ya tiene una evaluación
    if (booking.reviewId) {
      console.warn("Esta reserva ya tiene una evaluación");
      return null;
    }

    // Crear una referencia a la evaluación detallada
    const reviewData = {
      bookingId,
      craftsmanId: booking.craftsmanId,
      clientId: booking.clientId,
      overallRating: review.overallRating,
      qualityRating: review.qualityRating,
      punctualityRating: review.punctualityRating,
      priceRating: review.priceRating,
      communicationRating: review.communicationRating,
      comment: review.comment,
      images: review.images || [],
    };

    try {
      // Añadir la evaluación detallada al almacén de evaluaciones
      const detailedReview = useReviewStore.getState().addReview(reviewData);

      // Actualizar la reserva con una referencia a la evaluación
      // لا نغير حالة الطلب إذا كان ملغيًا
      set((state) => ({
        bookings: state.bookings.map((b) => {
          // مقارنة المعرفات بمرونة أكبر
          const bookingIdMatches =
            String(b.id) === String(bookingId) ||
            String(b._id) === String(bookingId);

          if (bookingIdMatches) {
            // إذا كان الطلب ملغيًا، نحتفظ بحالته كما هي
            const newStatus =
              b.status === "cancelled" ? "cancelled" : "completed";
            return {
              ...b,
              reviewId: detailedReview.id || detailedReview._id,
              status: newStatus,
              // تخزين المعرف الرئيسي
              id: b.id || b._id,
              // تخزين معرفات العميل والحرفي بشكل صريح
              clientId: b.clientId || b.client?._id || booking.clientId,
              craftsmanId:
                b.craftsmanId || b.craftsman?._id || booking.craftsmanId,
            };
          }
          return b;
        }),
      }));

      // إضافة إشعار للتقييم الجديد
      notificationService.createReviewNotification(detailedReview, booking);

      return detailedReview;
    } catch (error) {
      console.error("Error al añadir la evaluación:", error);
      return null;
    }
  },

  // Get bookings for a user (ordered by creation date, newest first)
  getUserBookings: (userId, userType) => {
    const { bookings } = get();
    let userBookings = [];

    // طباعة جميع الحجوزات للتصحيح

    // إذا كان معرف المستخدم غير موجود، نعرض جميع الحجوزات
    if (!userId) {
      return bookings.sort(
        (a, b) => new Date(b.createdAt) - new Date(a.createdAt)
      );
    }

    // إذا كان المستخدم حرفي، نحاول البحث عن الحجوزات مباشرة باستخدام معرف الحرفي
    if (userType === "craftsman") {
      try {
        // البحث عن معرف الحرفي في الحجوزات المتاحة
        const craftsmanBookings = bookings.filter(
          (booking) =>
            booking.craftsman &&
            booking.craftsman.user &&
            (String(booking.craftsman.user._id) === String(userId) ||
              String(booking.craftsman.user.id) === String(userId))
        );

        if (craftsmanBookings.length > 0) {
          const craftsmanId =
            craftsmanBookings[0].craftsman._id ||
            craftsmanBookings[0].craftsman.id;

          // تحديث المتجر بالحجوزات المفلترة
          set((state) => ({
            ...state,
            // لا نغير الحجوزات الأصلية، فقط نضيف معلومات التصحيح
            directFilteredBookings: craftsmanBookings,
          }));
        } else {
        }
      } catch (error) {
        console.error("خطأ في محاولة الحصول على معرف الحرفي من API:", error);
      }
    }

    // تحويل معرف المستخدم إلى نص للمقارنة
    const userIdStr = String(userId);

    // طباعة معلومات إضافية للتصحيح

    // محاولة العثور على معرف الحرفي من البيانات المتاحة
    let craftsmanProfileId = null;
    if (userType === "craftsman") {
      try {
        // البحث عن معرف الحرفي في الحجوزات المتاحة
        const craftsmanBooking = bookings.find(
          (booking) =>
            booking.craftsman &&
            booking.craftsman.user &&
            (String(booking.craftsman.user._id) === userIdStr ||
              String(booking.craftsman.user.id) === userIdStr)
        );

        if (craftsmanBooking && craftsmanBooking.craftsman) {
          craftsmanProfileId =
            craftsmanBooking.craftsman._id || craftsmanBooking.craftsman.id;
        } else {
        }
      } catch (error) {
        console.error("خطأ في البحث عن معرف الحرفي:", error);
      }
    }

    if (userType === "client") {
      userBookings = bookings.filter((booking) => {
        // التحقق من وجود معرف العميل في أي من الصيغ المحتملة
        const clientId =
          booking.clientId ||
          (booking.client && (booking.client._id || booking.client.id)) ||
          null;

        if (!clientId) {
          return false; // لا نعرض الحجوزات التي لا تحتوي على معرف العميل
        }

        // مقارنة المعرفات كنصوص
        const bookingClientIdStr = String(clientId);

        // استخدام دالة المطابقة
        const isMatch =
          bookingClientIdStr === userIdStr ||
          bookingClientIdStr.includes(userIdStr) ||
          userIdStr.includes(bookingClientIdStr);

        return isMatch;
      });
    } else if (userType === "craftsman") {
      userBookings = bookings.filter((booking) => {
        // التحقق من وجود معرف الحرفي في أي من الصيغ المحتملة
        const craftsmanId =
          booking.craftsmanId ||
          (booking.craftsman &&
            (booking.craftsman._id || booking.craftsman.id)) ||
          null;

        if (!craftsmanId) {
          return false;
        }

        // مقارنة المعرفات كنصوص
        const bookingCraftsmanIdStr = String(craftsmanId);

        // الحصول على معرف المستخدم المرتبط بالحرفي إذا كان متاحًا
        const craftsmanUserId =
          booking.craftsman?.user?._id || booking.craftsman?.user?.id;

        // استخدام دالة المطابقة مع مراعاة العلاقة بين معرف المستخدم ومعرف الحرفي
        const isMatch =
          // مقارنة معرف الحرفي في الحجز مع معرف المستخدم
          bookingCraftsmanIdStr === userIdStr ||
          bookingCraftsmanIdStr.includes(userIdStr) ||
          userIdStr.includes(bookingCraftsmanIdStr) ||
          // مقارنة معرف المستخدم المرتبط بالحرفي مع معرف المستخدم
          (craftsmanUserId &&
            (String(craftsmanUserId) === userIdStr ||
              String(craftsmanUserId).includes(userIdStr) ||
              userIdStr.includes(String(craftsmanUserId)))) ||
          // مقارنة معرف الحرفي في الحجز مع معرف الحرفي من البيانات المتاحة
          (craftsmanProfileId &&
            (bookingCraftsmanIdStr === String(craftsmanProfileId) ||
              bookingCraftsmanIdStr.includes(String(craftsmanProfileId)) ||
              String(craftsmanProfileId).includes(bookingCraftsmanIdStr)));

        return isMatch;
      });
    }

    // إذا كان المستخدم حرفي ولم نجد أي حجوزات، نحاول البحث مباشرة في الحجوزات باستخدام معرف المستخدم
    if (userType === "craftsman" && userBookings.length === 0) {
      // البحث عن الحجوزات التي تحتوي على معرف المستخدم في كائن المستخدم المرتبط بالحرفي
      const directUserIdBookings = bookings.filter((booking) => {
        if (!booking.craftsman || !booking.craftsman.user) return false;

        const craftsmanUserId =
          booking.craftsman.user._id || booking.craftsman.user.id;
        return craftsmanUserId && String(craftsmanUserId) === String(userId);
      });

      if (directUserIdBookings.length > 0) {
        userBookings = directUserIdBookings;
      } else {
        // محاولة أخيرة: البحث عن الحجوزات باستخدام معرف الحرفي من الحجوزات الأخرى
        // نبحث عن أي حجز يحتوي على معلومات الحرفي
        const craftsmanBooking = bookings.find(
          (b) => b.craftsman && (b.craftsman._id || b.craftsman.id)
        );

        if (craftsmanBooking && craftsmanBooking.craftsman) {
          const craftsmanId =
            craftsmanBooking.craftsman._id || craftsmanBooking.craftsman.id;

          const similarBookings = bookings.filter((booking) => {
            const bookingCraftsmanId =
              booking.craftsmanId ||
              (booking.craftsman &&
                (booking.craftsman._id || booking.craftsman.id));

            return (
              bookingCraftsmanId &&
              String(bookingCraftsmanId) === String(craftsmanId)
            );
          });

          if (similarBookings.length > 0) {
            userBookings = similarBookings;
          }
        }
      }
    }

    // Ordenar por fecha de creación (de más reciente a más antigua)
    return userBookings.sort(
      (a, b) => new Date(b.createdAt) - new Date(a.createdAt)
    );
  },

  // Get a single booking by ID
  getBookingById: (bookingId) => {
    const { bookings } = get();

    // إذا كان المعرف غير موجود، نرجع null
    if (!bookingId) {
      return null;
    }

    // تحويل معرف الطلب إلى نص للمقارنة
    const bookingIdStr = String(bookingId);

    // البحث عن الطلب باستخدام مقارنة النصوص
    let foundBooking = bookings.find((booking) => {
      if (!booking.id && !booking._id) return false;

      // تحويل معرف الطلب في القائمة إلى نص
      const bookingItemIdStr = String(booking.id || booking._id);

      // مقارنة المعرفات كنصوص
      return bookingItemIdStr === bookingIdStr;
    });

    // إذا لم يتم العثور على الطلب، حاول البحث عن طريق معرف MongoDB
    if (!foundBooking) {
      foundBooking = bookings.find((booking) => {
        // البحث في جميع المعرفات المحتملة
        const possibleIds = [
          booking.id,
          booking._id,
          booking.bookingId,
          booking.data?.bookingId,
        ]
          .filter(Boolean)
          .map(String);

        return possibleIds.includes(bookingIdStr);
      });
    }

    if (foundBooking) {
    }

    return foundBooking;
  },

  // إضافة معرف التقييم إلى الحجز
  addReview: (bookingId, review) => {
    // تحديث الحجز محليًا
    set((state) => ({
      bookings: state.bookings.map((booking) => {
        // مقارنة المعرفات بمرونة أكبر
        const bookingIdMatches =
          String(booking.id) === String(bookingId) ||
          String(booking._id) === String(bookingId);

        if (bookingIdMatches) {
          return {
            ...booking,
            reviewId: review.id || review._id,
          };
        }
        return booking;
      }),
    }));

    // تحديث الحجز في قاعدة البيانات
    try {
      const reviewId = review.id || review._id;
      if (reviewId) {
        bookingService
          .updateBookingWithReview(bookingId, reviewId)
          .then((updatedBooking) => {})
          .catch((error) => {
            console.error(
              "خطأ في تحديث الحجز بمعرف التقييم في قاعدة البيانات:",
              error
            );
          });
      } else {
        console.error(
          "لا يمكن تحديث الحجز في قاعدة البيانات: معرف التقييم غير موجود"
        );
      }
    } catch (error) {
      console.error("خطأ في تحديث الحجز بمعرف التقييم:", error);
    }
  },
}));

export default useBookingStore;
