import { create } from "zustand";
import { userService, authService } from "../services/api";

// ملاحظة: نظرًا لأن تفضيلات الوضع المظلم هي تفضيلات شخصية للمستخدم،
// سنستخدم localStorage لتخزينها مؤقتًا، ولكن سنقوم بمزامنتها مع الخادم عند تغييرها
// إذا كان المستخدم مسجل الدخول

const LOCAL_STORAGE_KEY = "jobscope-theme";

// استرجاع الوضع المظلم من التخزين المحلي
const getInitialDarkMode = () => {
  if (typeof window !== "undefined") {
    const savedTheme = localStorage.getItem(LOCAL_STORAGE_KEY);
    if (savedTheme) {
      return JSON.parse(savedTheme).darkMode || false;
    }

    // إذا لم يكن هناك تفضيل محفوظ، استخدم تفضيل النظام
    return (
      window.matchMedia &&
      window.matchMedia("(prefers-color-scheme: dark)").matches
    );
  }
  return false;
};

const useThemeStore = create((set, get) => ({
  darkMode: getInitialDarkMode(),
  loading: false,
  error: null,

  // تبديل الوضع المظلم
  toggleDarkMode: async () => {
    const newDarkMode = !get().darkMode;

    // تطبيق الوضع المظلم على الصفحة
    if (newDarkMode) {
      document.body.classList.add("dark-mode");
    } else {
      document.body.classList.remove("dark-mode");
    }

    // تحديث الحالة المحلية
    set({ darkMode: newDarkMode });

    // حفظ التفضيل في التخزين المحلي
    localStorage.setItem(
      LOCAL_STORAGE_KEY,
      JSON.stringify({ darkMode: newDarkMode })
    );

    // مزامنة التفضيل مع الخادم إذا كان المستخدم مسجل الدخول
    try {
      const token = localStorage.getItem("token");
      if (token) {
        set({ loading: true });
        await userService.updateProfile({ darkMode: newDarkMode });
        set({ loading: false });
      }
    } catch (error) {
      console.error("خطأ في مزامنة تفضيل الوضع المظلم مع الخادم:", error);
      set({
        loading: false,
        error: error.message || "حدث خطأ أثناء مزامنة تفضيل الوضع المظلم",
      });
    }
  },

  // تعيين الوضع المظلم
  setDarkMode: async (value) => {
    // تطبيق الوضع المظلم على الصفحة
    if (value) {
      document.body.classList.add("dark-mode");
    } else {
      document.body.classList.remove("dark-mode");
    }

    // تحديث الحالة المحلية
    set({ darkMode: value });

    // حفظ التفضيل في التخزين المحلي
    localStorage.setItem(
      LOCAL_STORAGE_KEY,
      JSON.stringify({ darkMode: value })
    );

    // مزامنة التفضيل مع الخادم إذا كان المستخدم مسجل الدخول
    try {
      const token = localStorage.getItem("token");
      if (token) {
        set({ loading: true });
        await userService.updateProfile({ darkMode: value });
        set({ loading: false });
      }
    } catch (error) {
      console.error("خطأ في مزامنة تفضيل الوضع المظلم مع الخادم:", error);
      set({
        loading: false,
        error: error.message || "حدث خطأ أثناء مزامنة تفضيل الوضع المظلم",
      });
    }
  },

  // تحميل تفضيل الوضع المظلم من الخادم
  loadThemePreference: async () => {
    try {
      const token = localStorage.getItem("token");
      if (token) {
        set({ loading: true });
        const userData = await authService.getCurrentUser();

        if (userData && userData.darkMode !== undefined) {
          // تطبيق الوضع المظلم على الصفحة
          if (userData.darkMode) {
            document.body.classList.add("dark-mode");
          } else {
            document.body.classList.remove("dark-mode");
          }

          // تحديث الحالة المحلية
          set({ darkMode: userData.darkMode, loading: false });

          // حفظ التفضيل في التخزين المحلي
          localStorage.setItem(
            LOCAL_STORAGE_KEY,
            JSON.stringify({ darkMode: userData.darkMode })
          );
        } else {
          set({ loading: false });
        }
      }
    } catch (error) {
      console.error("خطأ في تحميل تفضيل الوضع المظلم من الخادم:", error);
      set({
        loading: false,
        error: error.message || "حدث خطأ أثناء تحميل تفضيل الوضع المظلم",
      });
    }
  },
}));

// تهيئة الوضع المظلم بناءً على التفضيل المحفوظ
if (typeof window !== "undefined") {
  const isDarkMode = useThemeStore.getState().darkMode;
  if (isDarkMode) {
    document.body.classList.add("dark-mode");
  } else {
    document.body.classList.remove("dark-mode");
  }

  // تحميل تفضيل الوضع المظلم من الخادم عند تحميل الصفحة
  useThemeStore.getState().loadThemePreference();
}

export default useThemeStore;
