import { useState, useRef, useEffect } from "react";
import React from "react";

import Button from "../../common/Button";
import useThemeStore from "../../../store/themeStore";
import useSiteSettingsStore from "../../../store/siteSettingsStore";
import authService from "../../../services/authService";
import { X, Upload, Key } from "lucide-react";

const SettingsSection = () => {
  const darkMode = useThemeStore((state) => state.darkMode);
  const [showSiteSettings, setShowSiteSettings] = useState(false);
  const [showChangePassword, setShowChangePassword] = useState(false);

  // استخدام متجر إعدادات الموقع
  const { settings, updateSettings, fetchSettings } = useSiteSettingsStore();
  const [localSettings, setLocalSettings] = useState({
    siteName: '',
    description: '',
    contactEmail: '',
    contactPhone: '',
    siteLogo: '/logo.png',
    siteAddress: '',
    siteWorkingHours: '',
    ...settings
  });
  const fileInputRef = useRef(null);
  const [logoPreview, setLogoPreview] = useState(null);

  // حالة تغيير كلمة المرور
  const [passwordData, setPasswordData] = useState({
    currentPassword: "",
    newPassword: "",
    confirmPassword: "",
  });
  const [passwordErrors, setPasswordErrors] = useState({});
  const [isChangingPassword, setIsChangingPassword] = useState(false);

  // جلب الإعدادات عند تحميل المكون
  useEffect(() => {
    fetchSettings();
  }, [fetchSettings]);

  // تحديث الإعدادات المحلية عند تغيير settings
  useEffect(() => {
    if (settings) {
      setLocalSettings({
        siteName: settings.siteName || '',
        description: settings.description || '',
        contactEmail: settings.contactEmail || '',
        contactPhone: settings.contactPhone || '',
        siteLogo: settings.siteLogo || '/logo.png',
        siteAddress: settings.siteAddress || '',
        siteWorkingHours: settings.siteWorkingHours || '',
      });
    }
  }, [settings]);

  // تحديث الإعدادات المحلية عند فتح النموذج
  useEffect(() => {
    if (showSiteSettings) {
      setLogoPreview(null);
    }
  }, [showSiteSettings]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setLocalSettings((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleLogoUpload = (e) => {
    const file = e.target.files[0];
    if (file) {
      // إنشاء URL للصورة المحملة
      const imageUrl = URL.createObjectURL(file);
      setLogoPreview(imageUrl);

      // قراءة الملف كـ Data URL
      const reader = new FileReader();
      reader.onloadend = () => {
        setLocalSettings((prev) => ({
          ...prev,
          siteLogo: reader.result,
        }));
      };
      reader.readAsDataURL(file);
    }
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    // تحديث إعدادات الموقع في المتجر
    updateSettings(localSettings);
    setShowSiteSettings(false);
    // إظهار رسالة نجاح
    alert("تم حفظ إعدادات الموقع بنجاح!");
  };

  // معالجة تغيير كلمة المرور
  const handlePasswordChange = (e) => {
    const { name, value } = e.target;
    setPasswordData((prev) => ({
      ...prev,
      [name]: value,
    }));
    // إزالة الخطأ عند التعديل
    if (passwordErrors[name]) {
      setPasswordErrors((prev) => ({
        ...prev,
        [name]: "",
      }));
    }
  };

  const validatePasswordForm = () => {
    const errors = {};

    if (!passwordData.currentPassword) {
      errors.currentPassword = "كلمة المرور الحالية مطلوبة";
    }

    if (!passwordData.newPassword) {
      errors.newPassword = "كلمة المرور الجديدة مطلوبة";
    } else if (passwordData.newPassword.length < 6) {
      errors.newPassword = "كلمة المرور يجب أن تكون 6 أحرف على الأقل";
    }

    if (!passwordData.confirmPassword) {
      errors.confirmPassword = "تأكيد كلمة المرور مطلوب";
    } else if (passwordData.newPassword !== passwordData.confirmPassword) {
      errors.confirmPassword = "كلمة المرور غير متطابقة";
    }

    setPasswordErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleChangePassword = async (e) => {
    e.preventDefault();

    if (!validatePasswordForm()) {
      return;
    }

    setIsChangingPassword(true);
    try {
      await authService.changeAdminPassword({
        currentPassword: passwordData.currentPassword,
        newPassword: passwordData.newPassword,
      });

      // إعادة تعيين النموذج
      setPasswordData({
        currentPassword: "",
        newPassword: "",
        confirmPassword: "",
      });
      setShowChangePassword(false);
      alert("تم تغيير كلمة المرور بنجاح!");
    } catch (error) {
      setPasswordErrors({
        general: error.message || "حدث خطأ أثناء تغيير كلمة المرور",
      });
    } finally {
      setIsChangingPassword(false);
    }
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h3
          className={`text-xl font-bold ${
            darkMode ? "text-indigo-300" : "text-indigo-800"
          } relative`}
        >
          <span className="relative z-10">إعدادات النظام</span>
          <span
            className={`absolute bottom-0 left-0 right-0 h-2 ${
              darkMode ? "bg-indigo-500" : "bg-indigo-300"
            } opacity-40 transform -rotate-1 z-0`}
          ></span>
        </h3>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <div
          className={`p-4 rounded-lg ${
            darkMode
              ? "bg-gray-700"
              : "bg-gradient-to-br from-white to-indigo-100/40"
          } border ${
            darkMode ? "border-gray-600" : "border-indigo-200"
          } shadow-md`}
        >
          <h4
            className={`font-bold mb-3 ${
              darkMode ? "text-indigo-300" : "text-indigo-700"
            }`}
          >
            إعدادات الموقع
          </h4>
          <p className={`mb-4 ${darkMode ? "text-gray-300" : "text-gray-600"}`}>
            تعديل الإعدادات العامة للموقع مثل اسم الموقع وشعاره.
          </p>
          <Button
            variant="primary"
            className={`${
              darkMode
                ? "bg-gradient-to-r from-[#3730A3] to-[#4238C8] hover:from-[#322e92] hover:to-[#3b32b4]"
                : "bg-gradient-to-r from-[#4238C8] to-[#3730A3] hover:from-[#3b32b4] hover:to-[#322e92]"
            } text-white transition-all duration-200 shadow-sm hover:shadow-md relative overflow-hidden group`}
            onClick={() => setShowSiteSettings(true)}
          >
            <span className="relative z-10">تعديل الإعدادات</span>
            <span className="absolute inset-0 bg-white opacity-20 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700"></span>
          </Button>
        </div>

        <div
          className={`p-4 rounded-lg ${
            darkMode
              ? "bg-gray-700"
              : "bg-gradient-to-br from-white to-indigo-100/40"
          } border ${
            darkMode ? "border-gray-600" : "border-indigo-200"
          } shadow-md`}
        >
          <h4
            className={`font-bold mb-3 ${
              darkMode ? "text-indigo-300" : "text-indigo-700"
            }`}
          >
            تغيير كلمة المرور
          </h4>
          <p className={`mb-4 ${darkMode ? "text-gray-300" : "text-gray-600"}`}>
            تغيير كلمة مرور حساب الأدمن لضمان الأمان.
          </p>
          <Button
            variant="primary"
            className={`${
              darkMode
                ? "bg-gradient-to-r from-[#3730A3] to-[#4238C8] hover:from-[#322e92] hover:to-[#3b32b4]"
                : "bg-gradient-to-r from-[#4238C8] to-[#3730A3] hover:from-[#3b32b4] hover:to-[#322e92]"
            } text-white transition-all duration-200 shadow-sm hover:shadow-md relative overflow-hidden group`}
            onClick={() => setShowChangePassword(true)}
          >
            <Key size={16} className="ml-2" />
            <span className="relative z-10">تغيير كلمة المرور</span>
            <span className="absolute inset-0 bg-white opacity-20 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700"></span>
          </Button>
        </div>
      </div>

      <div
        className={`p-4 rounded-lg ${
          darkMode
            ? "bg-gray-700"
            : "bg-gradient-to-br from-white to-indigo-100/40"
        } border ${
          darkMode ? "border-gray-600" : "border-indigo-200"
        } shadow-md`}
      >
        <p className={`mb-4 ${darkMode ? "text-gray-300" : "text-gray-600"}`}>
          هنا يمكنك تعديل إعدادات النظام والتكوين.
        </p>
        <div
          className={`p-4 rounded-lg ${
            darkMode
              ? "bg-indigo-900/30 border border-indigo-800"
              : "bg-gradient-to-r from-indigo-50/70 to-indigo-100/50 border border-indigo-200"
          }`}
        >
          <p className={`${darkMode ? "text-indigo-300" : "text-indigo-700"}`}>
            هذا القسم قيد التطوير. سيتم إضافة وظائف إدارة الإعدادات قريبًا.
          </p>
        </div>
      </div>

      {/* نموذج تعديل إعدادات الموقع */}
      {showSiteSettings && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div
            className={`relative w-full max-w-2xl max-h-[90vh] overflow-y-auto rounded-lg shadow-xl ${
              darkMode
                ? "bg-gray-800 border border-gray-700"
                : "bg-white border border-indigo-200"
            } p-6`}
          >
            <div className="flex justify-between items-center mb-6">
              <h3
                className={`text-xl font-bold ${
                  darkMode ? "text-indigo-300" : "text-indigo-800"
                }`}
              >
                تعديل إعدادات الموقع
              </h3>
              <button
                className={`p-1 rounded-full ${
                  darkMode
                    ? "bg-gray-700 text-gray-300 hover:bg-gray-600"
                    : "bg-gray-100 text-gray-700 hover:bg-gray-200"
                } transition-colors`}
                onClick={() => setShowSiteSettings(false)}
              >
                <X size={20} />
              </button>
            </div>

            <form onSubmit={handleSubmit}>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div>
                  <label
                    className={`block mb-2 text-sm font-medium ${
                      darkMode ? "text-gray-300" : "text-gray-700"
                    }`}
                  >
                    اسم الموقع
                  </label>
                  <input
                    type="text"
                    name="siteName"
                    value={localSettings.siteName}
                    onChange={handleInputChange}
                    className={`w-full p-2 rounded-md ${
                      darkMode
                        ? "bg-gray-700 border border-gray-600 text-white"
                        : "bg-white border border-gray-300 text-gray-900"
                    } focus:ring-2 focus:ring-indigo-500 focus:border-transparent`}
                    required
                  />
                </div>

                <div>
                  <label
                    className={`block mb-2 text-sm font-medium ${
                      darkMode ? "text-gray-300" : "text-gray-700"
                    }`}
                  >
                    البريد الإلكتروني
                  </label>
                  <input
                    type="email"
                    name="contactEmail"
                    value={localSettings.contactEmail}
                    onChange={handleInputChange}
                    className={`w-full p-2 rounded-md ${
                      darkMode
                        ? "bg-gray-700 border border-gray-600 text-white"
                        : "bg-white border border-gray-300 text-gray-900"
                    } focus:ring-2 focus:ring-indigo-500 focus:border-transparent`}
                    required
                  />
                </div>

                <div>
                  <label
                    className={`block mb-2 text-sm font-medium ${
                      darkMode ? "text-gray-300" : "text-gray-700"
                    }`}
                  >
                    رقم الهاتف
                  </label>
                  <input
                    type="text"
                    name="contactPhone"
                    value={localSettings.contactPhone}
                    onChange={handleInputChange}
                    className={`w-full p-2 rounded-md ${
                      darkMode
                        ? "bg-gray-700 border border-gray-600 text-white"
                        : "bg-white border border-gray-300 text-gray-900"
                    } focus:ring-2 focus:ring-indigo-500 focus:border-transparent`}
                  />
                </div>

                <div>
                  <label
                    className={`block mb-2 text-sm font-medium ${
                      darkMode ? "text-gray-300" : "text-gray-700"
                    }`}
                  >
                    العنوان
                  </label>
                  <input
                    type="text"
                    name="siteAddress"
                    value={localSettings.siteAddress}
                    onChange={handleInputChange}
                    className={`w-full p-2 rounded-md ${
                      darkMode
                        ? "bg-gray-700 border border-gray-600 text-white"
                        : "bg-white border border-gray-300 text-gray-900"
                    } focus:ring-2 focus:ring-indigo-500 focus:border-transparent`}
                  />
                </div>

                <div>
                  <label
                    className={`block mb-2 text-sm font-medium ${
                      darkMode ? "text-gray-300" : "text-gray-700"
                    }`}
                  >
                    ساعات العمل
                  </label>
                  <input
                    type="text"
                    name="siteWorkingHours"
                    value={localSettings.siteWorkingHours}
                    onChange={handleInputChange}
                    className={`w-full p-2 rounded-md ${
                      darkMode
                        ? "bg-gray-700 border border-gray-600 text-white"
                        : "bg-white border border-gray-300 text-gray-900"
                    } focus:ring-2 focus:ring-indigo-500 focus:border-transparent`}
                  />
                </div>

                <div>
                  <label
                    className={`block mb-2 text-sm font-medium ${
                      darkMode ? "text-gray-300" : "text-gray-700"
                    }`}
                  >
                    شعار الموقع
                  </label>
                  <div className="space-y-2">
                    {/* عرض الشعار الحالي أو المعاينة */}
                    <div className="flex items-center justify-center p-2 border rounded-md bg-gray-50 dark:bg-gray-800">
                      <img
                        src={logoPreview || localSettings.siteLogo}
                        alt="شعار الموقع"
                        className="max-h-20 max-w-full"
                        onError={(e) => {
                          e.target.src = "/logo.png";
                        }}
                      />
                    </div>

                    {/* زر تحميل صورة جديدة */}
                    <div className="flex space-x-2 space-x-reverse">
                      <input
                        type="file"
                        accept="image/*"
                        ref={fileInputRef}
                        onChange={handleLogoUpload}
                        className="hidden"
                        id="logo-upload"
                      />
                      <Button
                        type="button"
                        variant="outline"
                        className={`${
                          darkMode
                            ? "border-indigo-600 text-indigo-300 hover:bg-indigo-900/30"
                            : "border-indigo-500 text-indigo-600 hover:bg-indigo-50"
                        } transition-colors flex items-center`}
                        onClick={() => fileInputRef.current.click()}
                      >
                        <Upload size={16} className="ml-2" />
                        تحميل صورة
                      </Button>

                      {/* حقل إدخال رابط الشعار */}
                      <input
                        type="text"
                        name="siteLogo"
                        value={localSettings.siteLogo}
                        onChange={handleInputChange}
                        placeholder="أو أدخل رابط الصورة"
                        className={`flex-1 p-2 rounded-md ${
                          darkMode
                            ? "bg-gray-700 border border-gray-600 text-white"
                            : "bg-white border border-gray-300 text-gray-900"
                        } focus:ring-2 focus:ring-indigo-500 focus:border-transparent`}
                      />
                    </div>
                  </div>
                </div>
              </div>

              <div className="mb-4">
                <label
                  className={`block mb-2 text-sm font-medium ${
                    darkMode ? "text-gray-300" : "text-gray-700"
                  }`}
                >
                  وصف الموقع
                </label>
                <textarea
                  name="description"
                  value={localSettings.description}
                  onChange={handleInputChange}
                  rows="3"
                  className={`w-full p-2 rounded-md ${
                    darkMode
                      ? "bg-gray-700 border border-gray-600 text-white"
                      : "bg-white border border-gray-300 text-gray-900"
                  } focus:ring-2 focus:ring-indigo-500 focus:border-transparent`}
                ></textarea>
              </div>

              <div className="flex justify-end space-x-2 space-x-reverse">
                <Button
                  type="button"
                  variant="outline"
                  className={`${
                    darkMode
                      ? "border-gray-600 text-gray-300 hover:bg-gray-700"
                      : "border-gray-300 text-gray-700 hover:bg-gray-100"
                  } transition-colors`}
                  onClick={() => setShowSiteSettings(false)}
                >
                  إلغاء
                </Button>

                <Button
                  type="submit"
                  variant="primary"
                  className={`${
                    darkMode
                      ? "bg-gradient-to-r from-[#3730A3] to-[#4238C8] hover:from-[#322e92] hover:to-[#3b32b4]"
                      : "bg-gradient-to-r from-[#4238C8] to-[#3730A3] hover:from-[#3b32b4] hover:to-[#322e92]"
                  } text-white transition-all duration-200 relative overflow-hidden group`}
                >
                  <span className="relative z-10">حفظ التغييرات</span>
                  <span className="absolute inset-0 bg-white opacity-20 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700"></span>
                </Button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* نموذج تغيير كلمة المرور */}
      {showChangePassword && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div
            className={`relative w-full max-w-md rounded-lg shadow-xl ${
              darkMode
                ? "bg-gray-800 border border-gray-700"
                : "bg-white border border-indigo-200"
            } p-6`}
          >
            <div className="flex justify-between items-center mb-6">
              <h3
                className={`text-xl font-bold ${
                  darkMode ? "text-indigo-300" : "text-indigo-800"
                }`}
              >
                تغيير كلمة المرور
              </h3>
              <button
                className={`p-1 rounded-full ${
                  darkMode
                    ? "bg-gray-700 text-gray-300 hover:bg-gray-600"
                    : "bg-gray-100 text-gray-700 hover:bg-gray-200"
                } transition-colors`}
                onClick={() => {
                  setShowChangePassword(false);
                  setPasswordData({
                    currentPassword: "",
                    newPassword: "",
                    confirmPassword: "",
                  });
                  setPasswordErrors({});
                }}
              >
                <X size={20} />
              </button>
            </div>

            {passwordErrors.general && (
              <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded-md">
                {passwordErrors.general}
              </div>
            )}

            <form onSubmit={handleChangePassword}>
              <div className="space-y-4">
                <div>
                  <label
                    className={`block mb-2 text-sm font-medium ${
                      darkMode ? "text-gray-300" : "text-gray-700"
                    }`}
                  >
                    كلمة المرور الحالية
                  </label>
                  <input
                    type="password"
                    name="currentPassword"
                    value={passwordData.currentPassword}
                    onChange={handlePasswordChange}
                    className={`w-full p-3 rounded-md ${
                      darkMode
                        ? "bg-gray-700 border border-gray-600 text-white"
                        : "bg-white border border-gray-300 text-gray-900"
                    } focus:ring-2 focus:ring-indigo-500 focus:border-transparent ${
                      passwordErrors.currentPassword ? "border-red-500" : ""
                    }`}
                    required
                  />
                  {passwordErrors.currentPassword && (
                    <p className="mt-1 text-sm text-red-600">
                      {passwordErrors.currentPassword}
                    </p>
                  )}
                </div>

                <div>
                  <label
                    className={`block mb-2 text-sm font-medium ${
                      darkMode ? "text-gray-300" : "text-gray-700"
                    }`}
                  >
                    كلمة المرور الجديدة
                  </label>
                  <input
                    type="password"
                    name="newPassword"
                    value={passwordData.newPassword}
                    onChange={handlePasswordChange}
                    className={`w-full p-3 rounded-md ${
                      darkMode
                        ? "bg-gray-700 border border-gray-600 text-white"
                        : "bg-white border border-gray-300 text-gray-900"
                    } focus:ring-2 focus:ring-indigo-500 focus:border-transparent ${
                      passwordErrors.newPassword ? "border-red-500" : ""
                    }`}
                    required
                  />
                  {passwordErrors.newPassword && (
                    <p className="mt-1 text-sm text-red-600">
                      {passwordErrors.newPassword}
                    </p>
                  )}
                </div>

                <div>
                  <label
                    className={`block mb-2 text-sm font-medium ${
                      darkMode ? "text-gray-300" : "text-gray-700"
                    }`}
                  >
                    تأكيد كلمة المرور الجديدة
                  </label>
                  <input
                    type="password"
                    name="confirmPassword"
                    value={passwordData.confirmPassword}
                    onChange={handlePasswordChange}
                    className={`w-full p-3 rounded-md ${
                      darkMode
                        ? "bg-gray-700 border border-gray-600 text-white"
                        : "bg-white border border-gray-300 text-gray-900"
                    } focus:ring-2 focus:ring-indigo-500 focus:border-transparent ${
                      passwordErrors.confirmPassword ? "border-red-500" : ""
                    }`}
                    required
                  />
                  {passwordErrors.confirmPassword && (
                    <p className="mt-1 text-sm text-red-600">
                      {passwordErrors.confirmPassword}
                    </p>
                  )}
                </div>
              </div>

              <div className="flex justify-end space-x-2 space-x-reverse mt-6">
                <Button
                  type="button"
                  variant="outline"
                  className={`${
                    darkMode
                      ? "border-gray-600 text-gray-300 hover:bg-gray-700"
                      : "border-gray-300 text-gray-700 hover:bg-gray-100"
                  } transition-colors`}
                  onClick={() => {
                    setShowChangePassword(false);
                    setPasswordData({
                      currentPassword: "",
                      newPassword: "",
                      confirmPassword: "",
                    });
                    setPasswordErrors({});
                  }}
                  disabled={isChangingPassword}
                >
                  إلغاء
                </Button>

                <Button
                  type="submit"
                  variant="primary"
                  className={`${
                    darkMode
                      ? "bg-gradient-to-r from-[#3730A3] to-[#4238C8] hover:from-[#322e92] hover:to-[#3b32b4]"
                      : "bg-gradient-to-r from-[#4238C8] to-[#3730A3] hover:from-[#3b32b4] hover:to-[#322e92]"
                  } text-white transition-all duration-200 relative overflow-hidden group`}
                  disabled={isChangingPassword}
                >
                  <span className="relative z-10">
                    {isChangingPassword
                      ? "جاري التغيير..."
                      : "تغيير كلمة المرور"}
                  </span>
                  <span className="absolute inset-0 bg-white opacity-20 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700"></span>
                </Button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default SettingsSection;
