import React from "react";
import ChatMessage from "./ChatMessage";
import QuickSuggestions from "./QuickSuggestions";
import FrequentlyAskedQuestions from "../FrequentlyAskedQuestions";

/**
 * مكون جسم المحادثة
 * @param {Object} props - خصائص المكون
 * @param {Array} props.messages - قائمة الرسائل
 * @param {boolean} props.darkMode - وضع الألوان الداكنة
 * @param {boolean} props.isThinking - هل البوت يفكر حالياً
 * @param {boolean} props.showSuggestions - هل يتم عرض الاقتراحات
 * @param {Array} props.suggestions - قائمة الاقتراحات
 * @param {Function} props.onSelectSuggestion - دالة تنفذ عند اختيار اقتراح
 * @param {boolean} props.isInitializingModel - هل يتم تهيئة النموذج
 * @param {boolean} props.showFAQ - هل يتم عرض الأسئلة الشائعة
 * @param {Function} props.onSelectFAQ - دالة تنفذ عند اختيار سؤال شائع
 * @param {React.RefObject} props.messagesEndRef - مرجع لنهاية الرسائل للتمرير التلقائي
 */
const ChatBody = ({
  messages,
  darkMode,
  isThinking,
  showSuggestions,
  suggestions,
  onSelectSuggestion,
  isInitializingModel,
  showFAQ,
  onSelectFAQ,
  messagesEndRef,
}) => {
  return (
    <div
      className={`h-[350px] overflow-y-auto p-4 ${
        darkMode ? "bg-gray-800" : "bg-gray-50"
      }`}
    >
      {!showFAQ ? (
        // عرض المحادثة
        <>
          {messages.map((message, index) => (
            <ChatMessage
              key={index}
              message={message.text}
              isUser={message.isUser}
              darkMode={darkMode}
            />
          ))}

          {isThinking && (
            <ChatMessage
              message=""
              isUser={false}
              darkMode={darkMode}
              isThinking={true}
            />
          )}

          {showSuggestions && !isThinking && !isInitializingModel && (
            <QuickSuggestions
              suggestions={suggestions}
              onSelect={onSelectSuggestion}
              darkMode={darkMode}
            />
          )}

          {isInitializingModel && (
            <div
              className={`flex items-center justify-center p-4 ${
                darkMode ? "text-gray-300" : "text-gray-600"
              }`}
            >
              <div className="flex flex-col items-center">
                <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-indigo-500 mb-2"></div>
                <p className="text-sm">جاري تحميل القدرات الذكية...</p>
                <p className="text-xs mt-2 text-center max-w-xs opacity-80">
                  هذا التحميل يحدث مرة واحدة فقط. في المرات القادمة، سيكون
                  المساعد جاهزاً فوراً.
                </p>
              </div>
            </div>
          )}
        </>
      ) : (
        // عرض الأسئلة الشائعة
        <FrequentlyAskedQuestions
          darkMode={darkMode}
          onSelectQuestion={onSelectFAQ}
        />
      )}

      <div ref={messagesEndRef} />
    </div>
  );
};

export default ChatBody;
