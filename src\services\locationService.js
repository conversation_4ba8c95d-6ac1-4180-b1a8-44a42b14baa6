/**
 * خدمة تحديد الموقع الجغرافي
 * تستخدم نظام هجين لتحديد الموقع في سوريا بدون VPN
 */

// الموقع الافتراضي (دمشق، سوريا)
const DEFAULT_LOCATION = {
  lat: 33.5138,
  lng: 36.2765,
  city: "دمشق",
  country: "سوريا",
};

/**
 * تحديد الموقع باستخدام Geolocation API المدمج في المتصفح
 * @returns {Promise<Object>} موقع المستخدم أو null في حالة الفشل
 */
const getLocationFromBrowser = () => {
  return new Promise((resolve) => {
    if (!navigator.geolocation) {
      console.log("Geolocation API غير مدعوم في هذا المتصفح");
      resolve(null);
      return;
    }

    const options = {
      enableHighAccuracy: true,
      timeout: 15000, // 15 ثانية (زيادة الوقت)
      maximumAge: 60000, // دقيقة واحدة (تقليل الوقت للحصول على موقع أحدث)
    };

    // محاولة أولى بدقة عالية
    navigator.geolocation.getCurrentPosition(
      (position) => {
        const location = {
          lat: position.coords.latitude,
          lng: position.coords.longitude,
          accuracy: position.coords.accuracy,
          source: "GPS",
          timestamp: position.timestamp,
        };
        console.log(
          "تم تحديد الموقع بنجاح باستخدام GPS (دقة عالية):",
          location
        );
        resolve(location);
      },
      (error) => {
        console.log("فشل GPS بدقة عالية:", error.message);

        // محاولة ثانية بدقة أقل ووقت أطول
        const fallbackOptions = {
          enableHighAccuracy: false,
          timeout: 20000, // 20 ثانية
          maximumAge: 300000, // 5 دقائق
        };

        navigator.geolocation.getCurrentPosition(
          (position) => {
            const location = {
              lat: position.coords.latitude,
              lng: position.coords.longitude,
              accuracy: position.coords.accuracy,
              source: "GPS-Fallback",
              timestamp: position.timestamp,
            };
            console.log(
              "تم تحديد الموقع بنجاح باستخدام GPS (دقة منخفضة):",
              location
            );
            resolve(location);
          },
          (fallbackError) => {
            console.log(
              "فشل في تحديد الموقع باستخدام GPS نهائياً:",
              fallbackError.message
            );
            resolve(null);
          },
          fallbackOptions
        );
      },
      options
    );
  });
};

/**
 * تحديد الموقع باستخدام IP-API (يعمل في سوريا بدون VPN)
 * @returns {Promise<Object>} موقع المستخدم أو null في حالة الفشل
 */
const getLocationFromIP = async () => {
  try {
    console.log("محاولة تحديد الموقع باستخدام IP-API...");

    const response = await fetch(
      "http://ip-api.com/json/?fields=status,country,countryCode,region,regionName,city,lat,lon,timezone,query",
      {
        method: "GET",
        headers: {
          Accept: "application/json",
        },
      }
    );

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    if (data.status === "success") {
      const location = {
        lat: data.lat,
        lng: data.lon,
        city: data.city || data.regionName,
        region: data.regionName,
        country: data.country,
        countryCode: data.countryCode,
        timezone: data.timezone,
        ip: data.query,
        source: "IP",
      };

      console.log("تم تحديد الموقع بنجاح باستخدام IP-API:", location);
      return location;
    } else {
      console.log("فشل IP-API في تحديد الموقع:", data);
      return null;
    }
  } catch (error) {
    console.log("خطأ في IP-API:", error.message);
    return null;
  }
};

/**
 * تحديد الموقع باستخدام GeoJS كبديل ثاني
 * @returns {Promise<Object>} موقع المستخدم أو null في حالة الفشل
 */
const getLocationFromGeoJS = async () => {
  try {
    console.log("محاولة تحديد الموقع باستخدام GeoJS...");

    const response = await fetch("https://get.geojs.io/v1/ip/geo.json", {
      method: "GET",
      headers: {
        Accept: "application/json",
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    if (data.latitude && data.longitude) {
      const location = {
        lat: parseFloat(data.latitude),
        lng: parseFloat(data.longitude),
        city: data.city,
        region: data.region,
        country: data.country,
        countryCode: data.country_code,
        timezone: data.timezone,
        ip: data.ip,
        source: "GeoJS",
      };

      console.log("تم تحديد الموقع بنجاح باستخدام GeoJS:", location);
      return location;
    } else {
      console.log("فشل GeoJS في تحديد الموقع:", data);
      return null;
    }
  } catch (error) {
    console.log("خطأ في GeoJS:", error.message);
    return null;
  }
};

/**
 * التحقق من صحة الموقع (التأكد من أنه في سوريا أو منطقة معقولة)
 * @param {Object} location - الموقع المراد التحقق منه
 * @returns {boolean} true إذا كان الموقع صحيح
 */
const validateLocation = (location) => {
  if (!location || !location.lat || !location.lng) {
    return false;
  }

  // التحقق من أن الإحداثيات في نطاق معقول
  const lat = parseFloat(location.lat);
  const lng = parseFloat(location.lng);

  // نطاق سوريا التقريبي
  const syriaLatMin = 32.0;
  const syriaLatMax = 37.5;
  const syriaLngMin = 35.0;
  const syriaLngMax = 42.5;

  // نطاق أوسع للمنطقة العربية
  const regionLatMin = 12.0;
  const regionLatMax = 42.0;
  const regionLngMin = 20.0;
  const regionLngMax = 60.0;

  // التحقق من أن الموقع في نطاق معقول
  const isInRegion =
    lat >= regionLatMin &&
    lat <= regionLatMax &&
    lng >= regionLngMin &&
    lng <= regionLngMax;

  if (isInRegion) {
    // إذا كان في سوريا، فهو دقيق جداً
    const isInSyria =
      lat >= syriaLatMin &&
      lat <= syriaLatMax &&
      lng >= syriaLngMin &&
      lng <= syriaLngMax;

    if (isInSyria) {
      console.log("الموقع المحدد في سوريا - دقة عالية");
    } else {
      console.log("الموقع المحدد في المنطقة العربية - دقة جيدة");
    }

    return true;
  }

  console.log("الموقع المحدد خارج النطاق المتوقع:", { lat, lng });
  return false;
};

/**
 * الدالة الرئيسية لتحديد الموقع باستخدام نظام هجين
 * @returns {Promise<Object>} موقع المستخدم
 */
export const getCurrentLocation = async () => {
  console.log("بدء عملية تحديد الموقع التلقائي...");

  try {
    // المحاولة الأولى: Geolocation API (الأدق)
    const browserLocation = await getLocationFromBrowser();
    if (browserLocation && validateLocation(browserLocation)) {
      console.log("تم تحديد الموقع بنجاح باستخدام GPS");
      return {
        ...browserLocation,
        isDefault: false,
        method: "GPS",
      };
    }

    // المحاولة الثانية: IP-API
    const ipLocation = await getLocationFromIP();
    if (ipLocation && validateLocation(ipLocation)) {
      console.log("تم تحديد الموقع بنجاح باستخدام IP-API");
      return {
        ...ipLocation,
        isDefault: false,
        method: "IP-API",
      };
    }

    // المحاولة الثالثة: GeoJS
    const geoJSLocation = await getLocationFromGeoJS();
    if (geoJSLocation && validateLocation(geoJSLocation)) {
      console.log("تم تحديد الموقع بنجاح باستخدام GeoJS");
      return {
        ...geoJSLocation,
        isDefault: false,
        method: "GeoJS",
      };
    }

    // إذا فشلت جميع المحاولات، استخدم الموقع الافتراضي
    console.log(
      "فشل في تحديد الموقع تلقائياً، استخدام الموقع الافتراضي (دمشق)"
    );
    return {
      ...DEFAULT_LOCATION,
      isDefault: true,
      method: "Default",
    };
  } catch (error) {
    console.error("خطأ في تحديد الموقع:", error);
    return {
      ...DEFAULT_LOCATION,
      isDefault: true,
      method: "Default",
      error: error.message,
    };
  }
};

/**
 * طلب إذن تحديد الموقع من المستخدم
 * @returns {Promise<string>} حالة الإذن
 */
export const requestLocationPermission = async () => {
  if (!navigator.permissions) {
    return "unsupported";
  }

  try {
    const permission = await navigator.permissions.query({
      name: "geolocation",
    });
    return permission.state; // "granted", "denied", "prompt"
  } catch (error) {
    console.log("خطأ في التحقق من إذن الموقع:", error);
    return "unknown";
  }
};

/**
 * تحديد الموقع مع عرض رسالة للمستخدم
 * @param {Function} onLocationUpdate - دالة يتم استدعاؤها عند تحديث الموقع
 * @param {Function} onStatusUpdate - دالة يتم استدعاؤها عند تحديث حالة العملية
 */
export const getLocationWithFeedback = async (
  onLocationUpdate,
  onStatusUpdate
) => {
  if (onStatusUpdate) {
    onStatusUpdate("جاري تحديد موقعك...", "loading");
  }

  try {
    const location = await getCurrentLocation();

    if (onLocationUpdate) {
      onLocationUpdate(location);
    }

    if (onStatusUpdate) {
      if (location.isDefault) {
        onStatusUpdate(
          "تم استخدام الموقع الافتراضي (دمشق). للحصول على موقع أدق، اسمح بالوصول للموقع في المتصفح",
          "warning"
        );
      } else {
        // رسالة مبسطة
        onStatusUpdate("تم تحديد موقعك بنجاح", "success");

        // إخفاء الرسالة بعد ثانيتين
        setTimeout(() => {
          if (onStatusUpdate) {
            onStatusUpdate("", "hidden");
          }
        }, 2000);
      }
    }

    return location;
  } catch (error) {
    if (onStatusUpdate) {
      onStatusUpdate(
        "فشل في تحديد الموقع، تم استخدام الموقع الافتراضي",
        "error"
      );
    }

    return {
      ...DEFAULT_LOCATION,
      isDefault: true,
      method: "Default",
      error: error.message,
    };
  }
};

export default {
  getCurrentLocation,
  requestLocationPermission,
  getLocationWithFeedback,
  DEFAULT_LOCATION,
};
