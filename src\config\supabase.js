/*
 * إعدادات Supabase للمصادقة
 * بديل Firebase للمصادقة في سوريا
 */

import { createClient } from "@supabase/supabase-js";

// إعدادات Supabase
const supabaseUrl = "https://geqnmbnhyzzhqcouldfz.supabase.co";
const supabaseAnonKey =
  "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImdlcW5tYm5oeXp6aHFjb3VsZGZ6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgxOTI3NTMsImV4cCI6MjA2Mzc2ODc1M30.TV92S0BtPGtihgoKjcsW2svZl74_EdcrtJ60AUnIaHw";

// إنشاء عميل Supabase
const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    // تعطيل الحفظ التلقائي في localStorage لأننا نستخدم Backend منفصل
    persistSession: false,
    // تعطيل التحديث التلقائي للجلسة
    autoRefreshToken: false,
    // تمكين كشف الجلسة من URL للتحقق من تأكيد البريد الإلكتروني وإعادة تعيين كلمة المرور
    detectSessionInUrl: true,
    // تحديد URL للتوجيه بعد تأكيد البريد الإلكتروني
    redirectTo: window.location.origin + "/register/client",
    // إعدادات إضافية لإعادة تعيين كلمة المرور
    flowType: "pkce",
  },
});

export { supabase };
export default supabase;
