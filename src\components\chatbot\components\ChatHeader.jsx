import React from "react";
import { X, Brain, HelpCircle, MessageCircle } from "lucide-react";

/**
 * مكون رأس المحادثة
 * @param {Object} props - خصائص المكون
 * @param {string} props.title - عنوان المحادثة
 * @param {boolean} props.darkMode - وضع الألوان الداكنة
 * @param {Function} props.onClose - دالة تنفذ عند الضغط على زر الإغلاق
 * @param {boolean} props.showFAQ - هل يتم عرض الأسئلة الشائعة
 * @param {Function} props.toggleFAQ - دالة لتبديل عرض الأسئلة الشائعة
 */
const ChatHeader = ({ title, darkMode, onClose, showFAQ, toggleFAQ }) => {
  return (
    <div
      className={`p-3 ${
        darkMode ? "bg-indigo-700" : "bg-indigo-600"
      } text-white flex justify-between items-center`}
    >
      <div className="flex items-center">
        <Brain size={18} className="mr-2 text-yellow-300" />
        <h3 className="font-bold mr-2">{title}</h3>
      </div>
      <div className="flex items-center">
        {/* زر الأسئلة الشائعة */}
        <button
          onClick={toggleFAQ}
          className={`hover:bg-indigo-800 rounded-full p-1 transition-colors mr-1 ${
            showFAQ ? "bg-indigo-800" : ""
          }`}
          title={showFAQ ? "العودة إلى المحادثة" : "الأسئلة الشائعة"}
        >
          {showFAQ ? (
            <MessageCircle size={20} className="text-white" />
          ) : (
            <HelpCircle size={20} className="text-white" />
          )}
        </button>
        {/* زر الإغلاق */}
        <button
          onClick={onClose}
          className="hover:bg-indigo-800 rounded-full p-1 transition-colors"
          title="إغلاق"
        >
          <X size={20} className="text-white" />
        </button>
      </div>
    </div>
  );
};

export default ChatHeader;
