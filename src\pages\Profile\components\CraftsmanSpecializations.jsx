import React from "react";
import { Briefcase, Tag } from "lucide-react";
import Card from "../../../components/common/Card";

const CraftsmanSpecializations = ({ craftsman, darkMode }) => {
  // طباعة بيانات الحرفي للتصحيح
  console.log("بيانات الحرفي في CraftsmanSpecializations:", {
    professions: craftsman.professions,
    specializations: craftsman.specializations,
  });

  return (
    <Card
      className={`p-6 mb-6 rounded-xl ${
        darkMode
          ? "bg-gray-800 text-gray-200 border border-gray-700"
          : "bg-gradient-to-br from-blue-50/80 to-indigo-50 border border-indigo-100"
      } shadow-lg transition-colors duration-300`}
    >
      <div className="flex items-center mb-5">
        <div
          className={`p-2 rounded-full mr-3 ${
            darkMode ? "bg-indigo-900/50" : "bg-indigo-100"
          }`}
        >
          <Briefcase
            size={22}
            className={`${
              darkMode ? "text-indigo-400" : "text-indigo-500"
            } transition-colors duration-300`}
          />
        </div>
        <h2
          className={`text-xl font-bold ${
            darkMode ? "text-indigo-300" : "text-indigo-800"
          } relative inline-block transition-colors duration-300`}
        >
          <span className="relative z-10 ">التخصصات</span>
          <span
            className={`absolute bottom-0 left-0 right-0 h-2 ${
              darkMode ? "bg-indigo-500" : "bg-indigo-300"
            } opacity-40 transform -rotate-1 z-0`}
          ></span>
        </h2>
      </div>

      <div
        className={`rounded-lg p-4 ${
          darkMode
            ? "bg-gray-700/50 border border-gray-600"
            : "bg-gradient-to-r from-blue-100/80 to-indigo-100/80 border border-indigo-100"
        } transition-colors duration-300`}
      >
        <div className="space-y-4">
          {craftsman.professions && craftsman.professions.length > 0 ? (
            craftsman.professions.map((profession, index) => {
              const relatedSpecializations = [];

              // بيانات مثال لعرض التخصصات المرتبطة بكل مهنة
              const professionsData = [
                {
                  id: 1,
                  name: "كهربائي",
                  specializations: [
                    "تمديدات منزلية",
                    "صيانة أعطال",
                    "تركيب إنارة",
                  ],
                },
                {
                  id: 2,
                  name: "سباك",
                  specializations: [
                    "تمديدات صحية",
                    "إصلاح تسريبات",
                    "تركيب أدوات صحية",
                  ],
                },
                {
                  id: 3,
                  name: "نجار",
                  specializations: [
                    "أثاث منزلي",
                    "أبواب وشبابيك",
                    "ديكورات خشبية",
                  ],
                },
                {
                  id: 4,
                  name: "دهان",
                  specializations: [
                    "دهانات داخلية",
                    "دهانات خارجية",
                    "ديكورات جبسية",
                  ],
                },
                {
                  id: 5,
                  name: "تكييف وتبريد",
                  specializations: [
                    "تركيب مكيفات",
                    "صيانة ثلاجات",
                    "إصلاح مكيفات",
                  ],
                },
                {
                  id: 6,
                  name: "ميكانيكي",
                  specializations: [
                    "صيانة سيارات",
                    "كهرباء سيارات",
                    "ميكانيك عام",
                  ],
                },
                {
                  id: 7,
                  name: "حداد",
                  specializations: [
                    "أبواب وشبابيك",
                    "هياكل معدنية",
                    "أعمال الألمنيوم",
                  ],
                },
                {
                  id: 8,
                  name: "بناء",
                  specializations: ["بناء جدران", "تبليط", "أعمال إسمنتية"],
                },
              ];

              const profData = professionsData.find(
                (p) => p.name === profession
              );

              if (profData && craftsman.specializations) {
                // تصفية تخصصات الحرفي التي تنتمي إلى هذه المهنة
                craftsman.specializations.forEach((spec) => {
                  if (profData.specializations.includes(spec)) {
                    relatedSpecializations.push(spec);
                  }
                });
              }

              return (
                <div
                  key={index}
                  className={`p-3 rounded-lg ${
                    darkMode
                      ? "bg-gray-600/50 border border-gray-500"
                      : "bg-gradient-to-br from-white to-indigo-50/50 border border-indigo-200"
                  }`}
                >
                  <div className="flex items-center mb-2">
                    <div
                      className={`p-1.5 rounded-full mr-2 ${
                        darkMode ? "bg-indigo-900/70" : "bg-indigo-200"
                      }`}
                    >
                      <Briefcase
                        size={16}
                        className={`${
                          darkMode ? "text-indigo-300" : "text-indigo-600"
                        }`}
                      />
                    </div>
                    <div
                      className={`font-medium ${
                        darkMode ? "text-indigo-300" : "text-indigo-700"
                      }`}
                    >
                      {profession}
                    </div>
                  </div>

                  {relatedSpecializations.length > 0 && (
                    <div className="flex flex-wrap gap-2 mr-8">
                      {relatedSpecializations.map((spec, i) => (
                        <div
                          key={i}
                          className={`flex items-center px-2 py-1 rounded-full text-xs ${
                            darkMode
                              ? "bg-gray-700 text-indigo-300 border border-gray-600"
                              : "bg-indigo-100 text-indigo-700 border border-indigo-200"
                          }`}
                        >
                          <Tag size={12} className="ml-1" />
                          {spec}
                        </div>
                      ))}
                    </div>
                  )}

                  {relatedSpecializations.length === 0 && (
                    <div className="mr-8 text-sm opacity-70">تخصصات متعددة</div>
                  )}
                </div>
              );
            })
          ) : (
            <div className="text-center py-4">
              <p className={`${darkMode ? "text-gray-400" : "text-gray-500"}`}>
                لم يتم تحديد أي تخصصات
              </p>
            </div>
          )}
        </div>
      </div>
    </Card>
  );
};

export default CraftsmanSpecializations;
