# تعليمات تطبيق التغييرات في الباك إند

## المشكلة الحالية:
1. الفرونت إند يحاول الوصول إلى `/work-gallery/*` endpoints غير موجودة
2. معرض الأعمال في قاعدة البيانات يحتوي على ObjectId فقط بدلاً من روابط الصور
3. نحتاج لتحديث النظام ليستخدم imgbb لحفظ الصور

## التغييرات المطلوبة:

### 1. تحديث model Craftsman
في ملف `models/Craftsman.js`:

```javascript
const craftsmanSchema = new mongoose.Schema({
  // ... باقي الحقول الموجودة
  
  workGallery: [{
    type: String, // تغيير من ObjectId إلى String لحفظ روابط imgbb
    default: []
  }],
  
  // ... باقي الحقول
});
```

### 2. تحديث routes/craftsmen.js
أضف الكود التالي إلى ملف `routes/craftsmen.js`:

```javascript
// GET /api/craftsmen/:id/gallery - جلب معرض أعمال حرفي محدد
router.get('/:id/gallery', async (req, res) => {
  try {
    const { id } = req.params;
    
    let craftsman;
    if (id === 'me') {
      if (!req.user) {
        return res.status(401).json({ 
          success: false, 
          message: 'يجب تسجيل الدخول أولاً' 
        });
      }
      craftsman = await Craftsman.findById(req.user.id);
    } else {
      craftsman = await Craftsman.findById(id);
    }

    if (!craftsman) {
      return res.status(404).json({ 
        success: false, 
        message: 'الحرفي غير موجود' 
      });
    }

    res.json({
      success: true,
      workGallery: craftsman.workGallery || [],
      gallery: craftsman.workGallery || []
    });

  } catch (error) {
    console.error('خطأ في جلب معرض الأعمال:', error);
    res.status(500).json({ 
      success: false, 
      message: 'حدث خطأ أثناء جلب معرض الأعمال' 
    });
  }
});

// PUT /api/craftsmen/me/gallery - تحديث معرض الأعمال
router.put('/me/gallery', auth, async (req, res) => {
  try {
    const { workGallery } = req.body;

    if (!Array.isArray(workGallery)) {
      return res.status(400).json({ 
        success: false, 
        message: 'معرض الأعمال يجب أن يكون مصفوفة' 
      });
    }

    const validUrls = workGallery.filter(url => 
      typeof url === 'string' && 
      (url.startsWith('http') || url.startsWith('/uploads/'))
    );

    const craftsman = await Craftsman.findByIdAndUpdate(
      req.user.id,
      { workGallery: validUrls },
      { new: true }
    );

    if (!craftsman) {
      return res.status(404).json({ 
        success: false, 
        message: 'الحرفي غير موجود' 
      });
    }

    res.json({
      success: true,
      message: 'تم تحديث معرض الأعمال بنجاح',
      workGallery: craftsman.workGallery,
      craftsman: {
        id: craftsman._id,
        workGallery: craftsman.workGallery
      }
    });

  } catch (error) {
    console.error('خطأ في تحديث معرض الأعمال:', error);
    res.status(500).json({ 
      success: false, 
      message: 'حدث خطأ أثناء تحديث معرض الأعمال' 
    });
  }
});
```

### 3. تنظيف قاعدة البيانات (اختياري)
إذا كنت تريد تنظيف البيانات الموجودة:

```javascript
// سكريبت لتنظيف معرض الأعمال الموجود
db.craftsmen.updateMany(
  {},
  { $set: { workGallery: [] } }
);
```

### 4. إعداد imgbb API
تأكد من وجود متغير البيئة في `.env`:

```
IMGBB_API_KEY=your_imgbb_api_key_here
```

### 5. إزالة الكود القديم (اختياري)
يمكنك إزالة أي endpoints قديمة متعلقة برفع الصور للخادم مباشرة.

## النتيجة المتوقعة:
1. الصور ستُحفظ في imgbb
2. قاعدة البيانات ستحتوي على روابط الصور فقط
3. الفرونت إند سيعمل بشكل صحيح مع الباك إند
4. لن تكون هناك مشاكل في عرض الصور

## اختبار التغييرات:
1. تأكد من أن الباك إند يعمل بدون أخطاء
2. جرب رفع صورة من الفرونت إند
3. تأكد من أن الصور تظهر بشكل صحيح
4. جرب حذف صورة
5. جرب مسح المعرض بالكامل
