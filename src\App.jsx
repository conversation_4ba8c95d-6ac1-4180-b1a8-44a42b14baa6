import React, { useEffect, useState } from "react";
import { Routes, Route } from "react-router-dom";
import { Toaster } from "react-hot-toast";
import WelcomePage from "./pages/Welcome/WelcomePage";
import LoginPage from "./pages/Login/LoginPage";
import CraftsmanRegisterPage from "./pages/Register/CraftsmanRegisterPage";
import ClientRegisterPage from "./pages/Register/ClientRegisterPage";
import HomePage from "./pages/Home/HomePage";
import SearchPage from "./pages/Search/SearchPage";
import CraftsmanProfilePage from "./pages/Profile/CraftsmanProfilePage";
import MyProfilePage from "./pages/Profile/MyProfilePage";
import SettingsPage from "./pages/Settings/SettingsPage";
import BookingPage from "./pages/Booking/BookingPage";
import BookingsPage from "./pages/Bookings/BookingsPage";
import NotificationsPage from "./pages/Notifications/NotificationsPage";
import AdminMockPage from "./pages/Admin/AdminMockPage";
import AdminLogin from "./pages/Admin/AdminLogin";
import AdminDashboard from "./pages/Admin/AdminDashboard";
import AdminProtectedRoute from "./components/auth/AdminProtectedRoute";
import AdminPage from "./pages/AdminPage";
import LogoutRedirectPage from "./pages/Auth/LogoutRedirectPage";
import ResetPasswordPage from "./pages/Auth/ResetPasswordPage";
import ForgotPasswordPage from "./pages/ForgotPassword/ForgotPasswordPage";
import HowToUsePage from "./pages/HowToUse/HowToUsePage";
import NotFoundPage from "./pages/NotFound/NotFoundPage";
import LoadingScreen from "./components/common/LoadingScreen";
import ScrollToTop from "./components/common/ScrollToTop";
import useThemeStore from "./store/themeStore";
import useUserStore from "./store/userStore";
import useReviewStore from "./store/reviewStore";
import useBookingStore from "./store/bookingStore";
import { initializeTitleUpdater } from "./utils/titleUtils";

function App() {
  const darkMode = useThemeStore((state) => state.darkMode);
  const checkAuth = useUserStore((state) => state.checkAuth);
  const [authChecked, setAuthChecked] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // التحقق من حالة المصادقة عند تحميل التطبيق
  useEffect(() => {
    const verifyAuth = async () => {
      try {
        // تجنب التحقق من المصادقة في مسارات الأدمن وصفحات تسجيل الدخول
        const currentPath = window.location.pathname;
        if (
          currentPath.startsWith("/admin") ||
          currentPath === "/login" ||
          currentPath.startsWith("/register")
        ) {
          setAuthChecked(true);
          // تأخير قصير لإظهار شاشة التحميل للأدمن أيضاً
          setTimeout(() => {
            setIsLoading(false);
          }, 500);
          return;
        }

        // التحقق من وجود توكن في localStorage
        const token = localStorage.getItem("token");
        const userStr = localStorage.getItem("user");
        const user = useUserStore.getState().user;

        // إذا كان المستخدم موجودًا بالفعل في المتجر، فلا داعي للتحقق
        if (user) {
          setAuthChecked(true);
          setIsLoading(false);
          return;
        }

        // إذا كان هناك توكن وبيانات مستخدم في localStorage، نقوم بتحميلها في المتجر
        if (token && userStr) {
          try {
            // التحقق من انتهاء صلاحية التوكن
            const tokenExpiry = localStorage.getItem("tokenExpiry");
            if (tokenExpiry) {
              const expiryDate = new Date(tokenExpiry);
              const now = new Date();

              if (now > expiryDate) {
                // مسح البيانات المنتهية
                localStorage.removeItem("token");
                localStorage.removeItem("tokenExpiry");

                // محاولة التحقق من المصادقة من الخادم
                console.log("Token expired, checking auth from server...");
                await checkAuth();
              } else {
                // استخدام البيانات المخزنة في localStorage
                const userData = JSON.parse(userStr);

                // تحديث حالة المستخدم في المتجر
                useUserStore.setState({
                  user: userData,
                  isAuthenticated: true,
                  userType:
                    userData.userType || localStorage.getItem("userType"),
                  userId: userData.id || userData._id,
                });
              }
            } else {
              // إذا لم يكن هناك تاريخ انتهاء صلاحية، نفترض أن التوكن صالح
              const userData = JSON.parse(userStr);

              // تحديث حالة المستخدم في المتجر
              useUserStore.setState({
                user: userData,
                isAuthenticated: true,
                userType: userData.userType || localStorage.getItem("userType"),
                userId: userData.id || userData._id,
              });
            }
          } catch (error) {
            console.error("خطأ في تحليل بيانات المستخدم المخزنة:", error);
            // في حالة الخطأ، نحاول التحقق من المصادقة من الخادم
            // لكن فقط إذا لم نكن في مسار أدمن أو صفحات تسجيل الدخول
            const currentPath = window.location.pathname;
            if (
              !currentPath.startsWith("/admin") &&
              currentPath !== "/login" &&
              !currentPath.startsWith("/register")
            ) {
              console.log(
                "Error parsing user data, checking auth from server..."
              );
              await checkAuth();
            }
          }
        } else {
          // إذا لم يكن هناك توكن أو بيانات مستخدم، نحاول التحقق من المصادقة من الخادم
          // لكن فقط إذا لم نكن في مسار أدمن أو صفحات تسجيل الدخول
          const currentPath = window.location.pathname;
          if (
            !currentPath.startsWith("/admin") &&
            currentPath !== "/login" &&
            !currentPath.startsWith("/register")
          ) {
            await checkAuth();
          }
        }
      } catch (error) {
        console.error("خطأ في التحقق من حالة المصادقة:", error);
      } finally {
        setAuthChecked(true);

        // تأخير قصير لإظهار شاشة التحميل
        setTimeout(() => {
          setIsLoading(false);
        }, 800);
      }
    };

    verifyAuth();

    // إضافة مستمع للتحقق من حالة المصادقة عند استعادة التركيز على النافذة
    const handleFocus = async () => {
      // تجنب التحقق من المصادقة في مسارات الأدمن وصفحات تسجيل الدخول
      const currentPath = window.location.pathname;
      if (
        currentPath.startsWith("/admin") ||
        currentPath === "/login" ||
        currentPath.startsWith("/register")
      ) {
        return;
      }

      // التحقق من تاريخ انتهاء صلاحية التوكن
      const tokenExpiry = localStorage.getItem("tokenExpiry");
      const token = localStorage.getItem("token");

      if (token && tokenExpiry) {
        const expiryDate = new Date(tokenExpiry);
        const now = new Date();

        // فقط إذا انتهت صلاحية التوكن، نقوم بالتحقق من المصادقة
        if (now > expiryDate) {
          await checkAuth();
        }
      }
    };

    window.addEventListener("focus", handleFocus);

    return () => {
      window.removeEventListener("focus", handleFocus);
    };
  }, [checkAuth]);

  // Apply dark mode class to body when component mounts or darkMode changes
  useEffect(() => {
    if (darkMode) {
      document.body.classList.add("dark-mode");
    } else {
      document.body.classList.remove("dark-mode");
    }
  }, [darkMode]);

  // تهيئة محدث عنوان الصفحة
  useEffect(() => {
    initializeTitleUpdater();
  }, []);

  // تحميل جميع التقييمات عند بدء التطبيق
  useEffect(() => {
    const loadAllReviews = async () => {
      try {
        // تحميل التقييمات من الحجوزات بدلاً من محاولة تحميل جميع التقييمات دفعة واحدة
        const { bookings } = useBookingStore.getState();

        // جمع معرفات التقييمات من الحجوزات
        const reviewIds = bookings
          .filter((booking) => booking.reviewId)
          .map((booking) => booking.reviewId);

        // تحميل كل تقييم على حدة
        const { getReviewById } = useReviewStore.getState();
        const reviewPromises = reviewIds.map((reviewId) =>
          getReviewById(reviewId)
        );

        // انتظار تحميل جميع التقييمات
        await Promise.all(reviewPromises);
      } catch (error) {
        console.error("خطأ في تحميل التقييمات:", error);
      }
    };

    loadAllReviews();
  }, []);

  // تعيين حالة التحقق من المصادقة إلى true مباشرة
  useEffect(() => {
    if (!authChecked) {
      // تأخير قصير جدًا لإتاحة الوقت للتحقق من المصادقة
      const timer = setTimeout(() => {
        setAuthChecked(true);
      }, 100);

      return () => clearTimeout(timer);
    }
  }, [authChecked]);

  // إعادة تعيين حالة التحميل عند تحديث الصفحة فقط
  useEffect(() => {
    // استخدام performance API للتحقق من نوع التحميل
    const navigationEntries = performance.getEntriesByType("navigation");

    if (navigationEntries.length > 0) {
      const navType = navigationEntries[0].type;

      // عرض شاشة التحميل فقط عند تحديث الصفحة (reload)
      if (navType === "reload") {
        setIsLoading(true);
      } else {
        // إذا كان التنقل من نوع آخر (back_forward أو navigate)، لا نعرض شاشة التحميل
        setIsLoading(false);
      }
    } else {
      // إذا لم يتم دعم performance API، نستخدم طريقة بديلة
      const pageLoadTime = Date.now();
      const lastPageLoadTime = parseInt(
        sessionStorage.getItem("lastPageLoadTime") || "0"
      );
      const timeDiff = pageLoadTime - lastPageLoadTime;

      // إذا كان الفرق الزمني أقل من 3 ثوانٍ، فمن المحتمل أنه تحديث للصفحة
      if (lastPageLoadTime > 0 && timeDiff < 3000) {
        setIsLoading(true);
      }

      // تحديث وقت تحميل الصفحة
      sessionStorage.setItem("lastPageLoadTime", pageLoadTime.toString());
    }
  }, []);

  return (
    <div
      className={`min-h-screen font-sans ${
        darkMode
          ? "bg-gray-900 text-white"
          : "bg-gradient-to-br from-blue-50 to-indigo-100 text-black"
      } transition-colors duration-300`}
    >
      {/* مكون إعادة تعيين موضع التمرير عند الانتقال بين الصفحات */}
      <ScrollToTop />

      {/* عرض شاشة التحميل إذا كانت حالة التحميل نشطة */}
      {isLoading && <LoadingScreen />}

      {/* عرض المحتوى فقط بعد اكتمال التحميل */}
      {!isLoading && (
        <Routes>
          {/* مسارات المستخدمين العاديين */}
          <Route path="/" element={<WelcomePage />} />
          <Route path="/login" element={<LoginPage />} />
          <Route
            path="/register/craftsman"
            element={<CraftsmanRegisterPage />}
          />
          <Route path="/register/client" element={<ClientRegisterPage />} />
          <Route path="/home" element={<HomePage />} />
          <Route path="/search" element={<SearchPage />} />
          <Route
            path="/profile/craftsman/:id"
            element={<CraftsmanProfilePage />}
          />
          {/* مسار بديل لصفحة الحرفي للتوافق مع الروابط المباشرة */}
          <Route
            path="profile/craftsman/:id"
            element={<CraftsmanProfilePage />}
          />
          <Route path="/profile/my" element={<MyProfilePage />} />
          <Route path="/settings" element={<SettingsPage />} />
          <Route path="/booking/:id" element={<BookingPage />} />
          <Route path="/bookings" element={<BookingsPage />} />
          <Route path="/notifications" element={<NotificationsPage />} />
          <Route path="/logout-redirect" element={<LogoutRedirectPage />} />
          <Route path="/reset-password" element={<ResetPasswordPage />} />
          <Route path="/forgot-password" element={<ForgotPasswordPage />} />
          <Route path="/how-to-use" element={<HowToUsePage />} />

          {/* مسارات الأدمن */}
          <Route path="/admin/login" element={<AdminLogin />} />
          <Route path="/admin" element={<AdminPage />} />
          <Route path="/admin-mock" element={<AdminMockPage />} />

          {/* مسارات الأدمن المحمية */}
          <Route element={<AdminProtectedRoute />}>
            <Route path="/admin/dashboard" element={<AdminDashboard />} />
            {/* يمكن إضافة المزيد من المسارات المحمية للأدمن هنا */}
          </Route>

          {/* صفحة الخطأ 404 - يجب أن تكون آخر مسار */}
          <Route path="*" element={<NotFoundPage />} />
        </Routes>
      )}

      {/* Toast notifications */}
      <Toaster
        position="top-center"
        reverseOrder={false}
        gutter={8}
        containerClassName=""
        containerStyle={{}}
        toastOptions={{
          duration: 4000,
          style: {
            background: darkMode ? "#1f2937" : "#ffffff",
            color: darkMode ? "#ffffff" : "#000000",
            border: darkMode ? "1px solid #374151" : "1px solid #e5e7eb",
            borderRadius: "8px",
            fontSize: "14px",
            fontWeight: "500",
            padding: "12px 16px",
            boxShadow: darkMode
              ? "0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2)"
              : "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",
          },
          success: {
            iconTheme: {
              primary: "#10b981",
              secondary: darkMode ? "#1f2937" : "#ffffff",
            },
          },
          error: {
            iconTheme: {
              primary: "#ef4444",
              secondary: darkMode ? "#1f2937" : "#ffffff",
            },
          },
        }}
      />
    </div>
  );
}

export default App;
