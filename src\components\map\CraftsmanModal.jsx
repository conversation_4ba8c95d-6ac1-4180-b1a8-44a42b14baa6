import React from "react";
import Modal from "react-modal";
import { User, Star, ExternalLink, EyeOff } from "lucide-react";
import { useNavigate } from "react-router-dom";
import useThemeStore from "../../store/themeStore";

// تعيين العنصر الجذر للمودال
Modal.setAppElement("#root");

const CraftsmanModal = ({ isOpen, onClose, craftsman, onCraftsmanSelect }) => {
  const darkMode = useThemeStore((state) => state.darkMode);
  const navigate = useNavigate();

  if (!craftsman) return null;

  const handleProfileClick = () => {
    // استخدام المعرف المناسب
    const craftsmanId = craftsman.id || craftsman._id;

    // تأكد من أن الاسم موجود في الكائن المرسل
    const craftsmanWithName = {
      ...craftsman,
      id: craftsmanId,
      // تأكد من وجود الاسم
      name: craftsman.name || (craftsman.user && craftsman.user.name) || "حرفي",
    };

    // تمرير true لتوضيح أن النقر كان على الزر
    onCraftsmanSelect(craftsmanWithName, true);

    // إغلاق المودال
    onClose();

    // التنقل إلى صفحة الملف الشخصي
    navigate(`/profile/craftsman/${craftsmanId}`);

    // طباعة بيانات الحرفي للتصحيح
    console.log(
      "بيانات الحرفي المرسلة إلى صفحة الملف الشخصي:",
      craftsmanWithName
    );
  };

  // تخصيص نمط المودال
  const customStyles = {
    content: {
      top: "50%",
      left: "50%",
      right: "auto",
      bottom: "auto",
      marginRight: "-50%",
      transform: "translate(-50%, -50%)",
      maxWidth: "400px",
      width: "90%",
      padding: "20px",
      borderRadius: "12px",
      boxShadow: "0 8px 16px rgba(0, 0, 0, 0.15)",
      backgroundColor: darkMode ? "#1f2937" : "#ffffff",
      color: darkMode ? "#e5e7eb" : "#1f2937",
      border: darkMode ? "1px solid #374151" : "1px solid #e2e8f0",
      transition: "all 0.3s ease",
    },
    overlay: {
      backgroundColor: "rgba(0, 0, 0, 0.6)",
      zIndex: 1000,
    },
  };

  return (
    <Modal
      isOpen={isOpen}
      onRequestClose={onClose}
      style={customStyles}
      contentLabel="معلومات الحرفي"
      closeTimeoutMS={200}
    >
      <div className="craftsman-modal-content">
        {/* بطاقة معلومات الحرفي */}
        <div
          className={`rounded-lg p-4 mb-4 shadow-sm border transition-colors duration-300 ${
            darkMode
              ? "bg-gray-800 border-gray-700"
              : "bg-white border-gray-200"
          }`}
        >
          {/* الاسم */}
          <div className="craftsman-modal-name mb-3 w-full flex items-center justify-start">
            <User size={18} className="ml-2 text-blue-600 dark:text-blue-400" />
            <span
              className={`font-bold text-lg transition-colors duration-300 ${
                darkMode ? "text-white" : "text-gray-900"
              }`}
            >
              {craftsman.name ||
                (craftsman.user && craftsman.user.name) ||
                "حرفي"}
            </span>
          </div>

          {/* التقييم */}
          <div className="craftsman-modal-rating mb-3 flex items-center justify-start">
            <Star size={16} className="ml-1 text-yellow-500" />
            <span
              className={`mx-1 font-medium transition-colors duration-300 ${
                darkMode ? "text-gray-200" : "text-gray-800"
              }`}
            >
              {(craftsman.rating || 0).toFixed(1)}
            </span>
            <span
              className={`text-xs transition-colors duration-300 ${
                darkMode ? "text-gray-300" : "text-gray-600"
              }`}
            >
              ({craftsman.reviewCount || 0} تقييم)
            </span>
          </div>

          {/* المهنة والتخصص */}
          <div className="flex flex-row items-center justify-start gap-2 mb-1">
            <div
              className={`font-medium transition-colors duration-300 ${
                darkMode ? "text-blue-400" : "text-blue-600"
              }`}
            >
              {craftsman.profession ||
                (craftsman.professions &&
                  craftsman.professions.length > 0 &&
                  (typeof craftsman.professions[0] === "string"
                    ? craftsman.professions[0]
                    : craftsman.professions[0].name || "مهنة غير محددة")) ||
                "مهنة غير محددة"}
            </div>

            <div
              className={`text-sm transition-colors duration-300 ${
                darkMode ? "text-gray-400" : "text-gray-600"
              }`}
            >
              {(craftsman.specialization ||
                (craftsman.specializations &&
                  craftsman.specializations.length > 0 &&
                  craftsman.specializations[0])) && (
                <span>
                  {craftsman.specialization ||
                    (craftsman.specializations &&
                      craftsman.specializations.length > 0 &&
                      craftsman.specializations[0])}
                </span>
              )}
            </div>
          </div>
        </div>

        {/* نبذة عن الحرفي */}
        {craftsman.bio && (
          <div
            className={`craftsman-modal-bio mb-4 text-right p-3 rounded-lg border transition-colors duration-300 ${
              darkMode
                ? "bg-gray-900 border-gray-700"
                : "bg-blue-50 border-blue-100"
            }`}
          >
            <div className="flex justify-start">
              <h4
                className={`font-medium mb-1 transition-colors duration-300 ${
                  darkMode ? "text-gray-200" : "text-gray-800"
                }`}
              >
                نبذة:
              </h4>
            </div>
            <p
              className={`text-sm text-right transition-colors duration-300 ${
                darkMode ? "text-gray-300" : "text-gray-700"
              }`}
            >
              {craftsman.bio}
            </p>
          </div>
        )}

        {/* أزرار التحكم */}
        <div className="flex justify-between mt-4">
          <button
            onClick={onClose}
            className={`px-4 py-2 rounded-md text-sm transition-colors duration-300 text-white ${
              darkMode
                ? "bg-gradient-to-r from-red-700 to-red-800 hover:from-red-800 hover:to-red-900"
                : "bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700"
            } relative overflow-hidden group shadow-md`}
            style={{ transform: "none" }}
          >
            <span className="relative z-10">إغلاق</span>
            <span className="absolute inset-0 bg-white opacity-20 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700"></span>
          </button>

          {/* زر الملف الشخصي - يظهر فقط إذا لم تكن معلومات الاتصال مخفية */}
          {!craftsman.hideContactInfo ? (
            <button
              onClick={handleProfileClick}
              className={`px-4 py-2 rounded-md flex items-center text-sm transition-colors duration-300 text-white ${
                darkMode
                  ? "bg-gradient-to-r from-[#3730A3] to-[#4238C8] hover:from-[#322e92] hover:to-[#3b32b4]"
                  : "bg-gradient-to-r from-[#4238C8] to-[#3730A3] hover:from-[#3b32b4] hover:to-[#322e92]"
              } relative overflow-hidden group shadow-md`}
              style={{ transform: "none" }}
            >
              <span className="relative z-10 flex items-center">
                <ExternalLink size={14} className="ml-2" />
                عرض الملف الشخصي
              </span>
              <span className="absolute inset-0 bg-white opacity-20 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700"></span>
            </button>
          ) : (
            <div
              className={`px-4 py-2 rounded-md flex items-center text-sm ${
                darkMode
                  ? "bg-red-800/50 text-red-300 border border-red-700/50"
                  : "bg-red-100 text-red-600 border border-red-200"
              }`}
            >
              <EyeOff size={14} className="ml-2" />
              <span>معلومات الاتصال مخفية</span>
            </div>
          )}
        </div>
      </div>
    </Modal>
  );
};

export default CraftsmanModal;
