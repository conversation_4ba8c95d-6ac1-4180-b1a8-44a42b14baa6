import { create } from 'zustand';
import api from '../services/api';

const useSiteSettingsStore = create((set) => ({
  settings: null,
  loading: false,
  error: null,
  fetchSettings: async () => {
    set({ loading: true, error: null });
    try {
      const response = await api.get('/settings');
      set({ settings: response.data, loading: false });
    } catch (error) {
      console.log('خطأ في جلب إعدادات الموقع:', error.message);

      // إذا كان الخطأ 401، استخدم إعدادات افتراضية
      if (error.response?.status === 401) {
        console.log('استخدام إعدادات افتراضية بسبب خطأ 401');
        set({
          settings: {
            siteName: 'JobScope',
            description: 'منصة للربط بين طالبي الخدمة والحرفيين',
            contactEmail: '<EMAIL>',
            contactPhone: '+963 912 345 678'
          },
          loading: false
        });
      } else {
        set({ error: 'Failed to fetch settings', loading: false });
      }
    }
  },
  updateSettings: async (newSettings) => {
    set({ loading: true, error: null });
    try {
      const response = await api.put('/admin/settings', newSettings);
      set({ settings: response.data, loading: false });
    } catch (error) {
      set({ error: 'Failed to update settings', loading: false });
    }
  },
}));

export default useSiteSettingsStore;
