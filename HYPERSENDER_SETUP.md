# 📱 إعداد HyperSender لإرسال الرسائل النصية

## 🎯 **نظرة عامة**

تم تطوير تكامل كامل مع خدمة HyperSender لإرسال رسائل SMS حقيقية للأرقام السورية والدولية.

## 🔧 **خطوات الإعداد**

### **1. الحصول على بيانات HyperSender**

بعد تسجيل الدخول في [HyperSender](https://hypersender.com)، ستحتاج إلى:

#### **أ. API Token:**
- اذهب إلى قسم **"API"** أو **"Integration"** أو **"Settings"**
- ابحث عن **"API Key"** أو **"Access Token"**
- انسخ الرمز (يبدأ عادة بـ `hs_` أو مجموعة من الأحرف والأرقام)

#### **ب. Sender ID:**
- ا<PERSON><PERSON><PERSON> عن **"Sender ID"** أو **"From Name"**
- هذا هو الاسم الذي سيظهر للمستقبل (مثل: JobScope)

#### **ج. API Endpoint:**
- ابحث عن **"API Documentation"** أو **"API URL"**
- انسخ رابط إرسال الرسائل (مثل: `https://api.hypersender.com/sms/send`)

### **2. تحديث ملف البيئة**

في ملف `backend/.env`، حدث القيم التالية:

```env
# إعدادات HyperSender SMS
HYPERSENDER_API_TOKEN=your_actual_api_token_here
HYPERSENDER_SENDER_ID=JobScope
HYPERSENDER_API_URL=https://api.hypersender.com/sms/send
```

**مثال:**
```env
HYPERSENDER_API_TOKEN=hs_1234567890abcdef
HYPERSENDER_SENDER_ID=JobScope
HYPERSENDER_API_URL=https://api.hypersender.com/v1/sms/send
```

### **3. إعادة تشغيل الخادم**

```bash
cd backend
npm restart
# أو
node server.js
```

## 🧪 **اختبار الخدمة**

### **1. اختبار الاتصال:**

```bash
curl -X GET http://localhost:5000/api/auth/test-sms
```

**أو عبر المتصفح:**
```
http://localhost:5000/api/auth/test-sms
```

### **2. اختبار إرسال رمز التحقق:**

```bash
curl -X POST http://localhost:5000/api/auth/send-otp-phone \
  -H "Content-Type: application/json" \
  -d '{"phone": "+963999123456"}'
```

## 📋 **ميزات الخدمة**

### **✅ ما تم تطبيقه:**

1. **تنسيق تلقائي للأرقام:**
   - تحويل الأرقام السورية للصيغة الدولية
   - دعم الأرقام التي تبدأ بـ 0 أو 9
   - إضافة رمز الدولة +963 تلقائياً

2. **رسائل مخصصة:**
   - رمز التحقق OTP
   - رسائل الترحيب
   - إشعارات الحجز

3. **معالجة الأخطاء:**
   - تسجيل مفصل للأخطاء
   - رسائل خطأ واضحة
   - إعادة المحاولة التلقائية

4. **أمان عالي:**
   - تشفير API Token
   - تحقق من صحة الأرقام
   - حماية من الإرسال المتكرر

## 🔍 **استكشاف الأخطاء**

### **خطأ: "SMS service is not configured"**
- تأكد من وجود `HYPERSENDER_API_TOKEN` في ملف `.env`
- تأكد من إعادة تشغيل الخادم بعد التحديث

### **خطأ: "Network error"**
- تحقق من اتصال الإنترنت
- تأكد من صحة `HYPERSENDER_API_URL`

### **خطأ: "Authentication failed"**
- تحقق من صحة `HYPERSENDER_API_TOKEN`
- تأكد من أن الرمز لم ينته صلاحيته

### **خطأ: "Invalid phone number"**
- تأكد من أن الرقم بالصيغة الصحيحة
- تحقق من دعم HyperSender للأرقام السورية

## 📱 **أمثلة على الرسائل**

### **رمز التحقق:**
```
رمز التحقق الخاص بك في JobScope هو: 123456
لا تشارك هذا الرمز مع أي شخص.
صالح لمدة 10 دقائق.
```

### **رسالة ترحيب:**
```
مرحباً أحمد!
تم تسجيلك بنجاح في JobScope.
نتمنى لك تجربة ممتعة في منصتنا.
```

### **إشعار حجز:**
```
لديك طلب حجز جديد من محمد لخدمة السباكة.
يرجى مراجعة التطبيق للتفاصيل والرد على الطلب.
```

## 🚀 **الاستخدام في الكود**

### **إرسال رمز التحقق:**
```javascript
const hyperSenderService = require('../services/hyperSenderService');

// إرسال رمز التحقق
const result = await hyperSenderService.sendOTP('+963999123456', '123456');

if (result.success) {
  console.log('تم إرسال الرمز بنجاح');
} else {
  console.error('فشل في الإرسال:', result.error);
}
```

### **إرسال رسالة مخصصة:**
```javascript
const result = await hyperSenderService.sendSMS(
  '+963999123456', 
  'مرحباً! هذه رسالة تجريبية من JobScope'
);
```

## 📊 **مراقبة الأداء**

### **سجلات النظام:**
- جميع عمليات الإرسال مسجلة في console
- تتبع معرفات الرسائل
- تسجيل الأخطاء والنجاحات

### **إحصائيات الاستخدام:**
- عدد الرسائل المرسلة
- معدل النجاح
- أوقات الاستجابة

## 🔒 **الأمان والخصوصية**

### **حماية البيانات:**
- عدم تخزين أرقام الهواتف في السجلات
- تشفير API Tokens
- انتهاء صلاحية رموز التحقق

### **منع الإساءة:**
- حد أقصى لعدد الرسائل لكل رقم
- فترات انتظار بين الرسائل
- تحقق من صحة الأرقام

## ✅ **التحقق من التشغيل**

بعد الإعداد، يجب أن تحصل على:

1. **✅ اختبار الاتصال ناجح**
2. **✅ إرسال رمز التحقق يعمل**
3. **✅ استقبال الرسائل على الهاتف**
4. **✅ تسجيل العمليات في console**

الآن خدمة الرسائل النصية جاهزة للعمل! 🎉

## 📞 **الدعم**

إذا واجهت أي مشاكل:
1. تحقق من سجلات الخادم
2. اختبر الاتصال مع HyperSender
3. تأكد من صحة الإعدادات
4. راجع وثائق HyperSender API
