import React, { useState, useRef, useEffect } from "react";
import { motion } from "framer-motion";
import { Star, Upload, X, Edit } from "lucide-react";
import Button from "../common/Button";
import useThemeStore from "../../store/themeStore";
import useReviewStore from "../../store/reviewStore";
import useBookingStore from "../../store/bookingStore";
import CountdownTimer from "./CountdownTimer";

const ReviewModal = ({
  booking,
  onClose,
  onSuccess,
  existingReview = null,
  isEditing = false,
}) => {
  const darkMode = useThemeStore((state) => state.darkMode);
  const addReview = useReviewStore((state) => state.addReview);
  const updateReview = useReviewStore((state) => state.updateReview);
  const updateBookingWithReview = useBookingStore((state) => state.addReview);

  const modalRef = useRef(null);

  // استخدام التقييم الموجود إذا كنا في وضع التعديل
  const [reviewData, setReviewData] = useState(() => {
    if (isEditing && existingReview) {
      return {
        ...existingReview,
        images: existingReview.images || [],
      };
    }

    return {
      qualityRating: 5,
      punctualityRating: 5,
      priceRating: 5,
      communicationRating: 5,
      overallRating: 5,
      comment: "",
      images: [],
      craftsmanId: booking.craftsmanId || booking.craftsman?.id,
      clientId: booking.clientId || booking.client?.id,
      bookingId: booking.id,
    };
  });

  const [errors, setErrors] = useState({});
  const [previewImages, setPreviewImages] = useState(() => {
    // إذا كان هناك تقييم موجود وله صور، استخدمها كصور معاينة
    if (
      isEditing &&
      existingReview &&
      existingReview.images &&
      existingReview.images.length > 0
    ) {
      return existingReview.images;
    }
    return [];
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  // لن نضيف مستمع للنقرات خارج الشاشة لأننا نريد أن تبقى النافذة مفتوحة حتى ينقر المستخدم على زر الإلغاء أو الإرسال

  // حساب التقييم العام بناءً على المعايير الأخرى
  const calculateOverallRating = (data) => {
    const {
      qualityRating,
      punctualityRating,
      priceRating,
      communicationRating,
    } = data;
    return Math.round(
      (qualityRating + punctualityRating + priceRating + communicationRating) /
        4
    );
  };

  const handleRatingChange = (criterion, value) => {
    const newData = { ...reviewData, [criterion]: value };

    // تحديث التقييم العام تلقائيًا دائمًا
    // لا يمكن تغيير التقييم العام مباشرة
    newData.overallRating = calculateOverallRating(newData);

    setReviewData(newData);
  };

  const handleCommentChange = (e) => {
    setReviewData({ ...reviewData, comment: e.target.value });

    // مسح الخطأ عندما يكتب المستخدم
    if (errors.comment) {
      setErrors({ ...errors, comment: "" });
    }
  };

  const handleImageUpload = (e) => {
    const files = Array.from(e.target.files);

    if (files.length === 0) return;

    // التحقق من عدد الصور (الحد الأقصى 5)
    if (reviewData.images.length + files.length > 5) {
      setErrors({
        ...errors,
        images: "يمكنك تحميل 5 صور كحد أقصى",
      });
      return;
    }

    // إنشاء عناوين URL للمعاينة
    const newPreviewImages = [...previewImages];
    const newImages = [...reviewData.images];

    files.forEach((file) => {
      // التحقق من حجم الملف (الحد الأقصى 5 ميجابايت)
      if (file.size > 5 * 1024 * 1024) {
        setErrors({
          ...errors,
          images: "يجب أن يكون حجم الصورة أقل من 5 ميجابايت",
        });
        return;
      }

      const reader = new FileReader();
      reader.onload = (e) => {
        newPreviewImages.push(e.target.result);
        setPreviewImages(newPreviewImages);
      };
      reader.readAsDataURL(file);

      // في تطبيق حقيقي، هنا سيتم تحميل الصور إلى خادم
      // لهذا العرض التوضيحي، نقوم بتخزين كائنات الملف فقط
      newImages.push(file);
    });

    setReviewData({ ...reviewData, images: newImages });
    setErrors({ ...errors, images: "" });
  };

  const removeImage = (index) => {
    const newPreviewImages = [...previewImages];
    const newImages = [...reviewData.images];

    newPreviewImages.splice(index, 1);
    newImages.splice(index, 1);

    setPreviewImages(newPreviewImages);
    setReviewData({ ...reviewData, images: newImages });
  };

  const validateForm = () => {
    const newErrors = {};
    let isValid = true;

    if (!reviewData.comment) {
      newErrors.comment = "التعليق مطلوب";
      isValid = false;
    } else if (reviewData.comment.length < 5) {
      newErrors.comment = "يجب أن يكون التعليق 5 أحرف على الأقل";
      isValid = false;
    }

    setErrors(newErrors);
    return isValid;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (validateForm()) {
      setIsSubmitting(true);
      try {
        // تحويل الصور إلى عناوين URL للعرض التوضيحي
        // في تطبيق حقيقي، هنا سيتم إرسال الصور إلى خادم
        const imageUrls = previewImages;

        let reviewResult;

        // التحقق مما إذا كنا في وضع التعديل أو الإنشاء
        if (isEditing && existingReview) {
          // تحديث التقييم الموجود
          console.log("تحديث التقييم الموجود:", existingReview.id);

          reviewResult = updateReview(existingReview.id, {
            qualityRating: reviewData.qualityRating,
            punctualityRating: reviewData.punctualityRating,
            priceRating: reviewData.priceRating,
            communicationRating: reviewData.communicationRating,
            overallRating: reviewData.overallRating,
            comment: reviewData.comment,
            images: imageUrls,
          });

          console.log("تم تحديث التقييم:", reviewResult);
        } else {
          // إنشاء تقييم جديد
          console.log("معلومات التقييم قبل الإضافة:", {
            craftsmanId: reviewData.craftsmanId,
            clientId: reviewData.clientId,
            bookingId: reviewData.bookingId,
            qualityRating: reviewData.qualityRating,
            punctualityRating: reviewData.punctualityRating,
            priceRating: reviewData.priceRating,
            communicationRating: reviewData.communicationRating,
            overallRating: reviewData.overallRating,
          });

          // إضافة التقييم إلى المتجر
          // تحويل المعرفات إلى الصيغة المطلوبة للخادم
          const reviewToSubmit = {
            booking: reviewData.bookingId,  // تحويل bookingId إلى booking
            client: reviewData.clientId,    // تحويل clientId إلى client
            craftsman: reviewData.craftsmanId, // تحويل craftsmanId إلى craftsman
            qualityRating: reviewData.qualityRating,
            punctualityRating: reviewData.punctualityRating,
            priceRating: reviewData.priceRating,
            communicationRating: reviewData.communicationRating,
            overallRating: reviewData.overallRating,
            comment: reviewData.comment,
            images: imageUrls,
            createdAt: new Date().toISOString(),
          };

          console.log("بيانات التقييم المرسلة للخادم:", reviewToSubmit);

          reviewResult = addReview(reviewToSubmit);

          console.log("تم إنشاء التقييم:", reviewResult);

          // تحديث الحجز بمعرف التقييم
          if (reviewResult && booking.id) {
            // انتظار استجابة إضافة التقييم للحصول على المعرف
            reviewResult.then(review => {
              if (review && (review.id || review._id)) {
                updateBookingWithReview(booking.id, review);
                console.log(
                  "تم تحديث الحجز بمعرف التقييم:",
                  booking.id,
                  review.id || review._id
                );
              } else {
                console.error("خطأ: لم يتم الحصول على معرف التقييم من الاستجابة", review);
              }
            }).catch(error => {
              console.error("خطأ في معالجة استجابة التقييم:", error);
            });
          } else {
            console.error("خطأ: لا يمكن تحديث الحجز بمعرف التقييم");
          }
        }

        // إغلاق النافذة المنبثقة وإخطار المكون الأصلي بالنجاح
        // التأكد من أن reviewResult هو وعد (Promise) قبل استخدامه
        if (reviewResult && typeof reviewResult.then === 'function') {
          reviewResult.then(review => {
            onSuccess(review);
          }).catch(error => {
            console.error("خطأ في معالجة استجابة التقييم في onSuccess:", error);
            onSuccess(null); // إغلاق النافذة المنبثقة حتى في حالة الخطأ
          });
        } else {
          onSuccess(reviewResult);
        }
      } catch (error) {
        console.error("Error submitting review:", error);
        setErrors({
          ...errors,
          general: isEditing
            ? "حدث خطأ أثناء تحديث التقييم. يرجى المحاولة مرة أخرى."
            : "حدث خطأ أثناء إرسال التقييم. يرجى المحاولة مرة أخرى.",
        });
      } finally {
        setIsSubmitting(false);
      }
    }
  };

  // مكون لعرض نجوم التقييم
  const RatingStars = ({ criterion, label, value }) => (
    <div className="mb-4">
      <label
        className={`block font-medium mb-2 ${
          darkMode ? "text-gray-300" : "text-gray-700"
        } transition-colors duration-300`}
      >
        {label} <span className="text-red-500">*</span>
      </label>
      <div className="flex items-center">
        {[1, 2, 3, 4, 5].map((star) => (
          <button
            key={star}
            type="button"
            onClick={() =>
              criterion !== "overallRating" &&
              handleRatingChange(criterion, star)
            }
            className={`ml-1 focus:outline-none ${
              criterion === "overallRating" ? "cursor-default" : ""
            }`}
            disabled={criterion === "overallRating"}
          >
            <Star
              size={28}
              className={`${
                star <= value
                  ? `${
                      darkMode
                        ? "text-yellow-400 fill-yellow-400"
                        : "text-yellow-500 fill-yellow-500"
                    }`
                  : `${darkMode ? "text-gray-600" : "text-gray-300"}`
              }`}
            />
          </button>
        ))}
      </div>
    </div>
  );

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center z-[200] p-4 backdrop-blur-sm"
      onClick={(e) => e.stopPropagation()} // منع انتشار النقرات إلى العناصر الأساسية
    >
      <motion.div
        ref={modalRef}
        className={`${
          darkMode
            ? "bg-gradient-to-br from-gray-800 to-gray-900 text-gray-200"
            : "bg-gradient-to-br from-white to-indigo-50"
        } rounded-lg shadow-xl w-full max-w-2xl transition-colors duration-300 border ${
          darkMode ? "border-indigo-800/30" : "border-indigo-200"
        } overflow-y-auto max-h-[90vh]`}
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        onClick={(e) => e.stopPropagation()} // منع انتشار النقرات إلى العناصر الأساسية
      >
        <div className="p-6">
          {/* Header with close button */}
          <div className="flex justify-between items-center mb-6">
            <h2
              className={`text-xl font-bold ${
                darkMode ? "text-indigo-300" : "text-indigo-800"
              } relative inline-block transition-colors duration-300`}
            >
              <span className="relative z-10">
                {isEditing ? "تعديل التقييم" : "تقييم الخدمة"}
              </span>
              <span
                className={`absolute bottom-0 left-0 right-0 h-2 ${
                  darkMode ? "bg-indigo-500" : "bg-indigo-300"
                } opacity-40 transform -rotate-1 z-0`}
              ></span>
            </h2>
            <button
              onClick={onClose}
              className={`${
                darkMode
                  ? "text-gray-400 hover:text-gray-300"
                  : "text-gray-500 hover:text-gray-700"
              } transition-colors duration-300 p-1 rounded-full hover:bg-gray-100/10`}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>

          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <RatingStars
                criterion="qualityRating"
                label="جودة العمل"
                value={reviewData.qualityRating}
              />

              <RatingStars
                criterion="punctualityRating"
                label="الالتزام بالوقت"
                value={reviewData.punctualityRating}
              />

              <RatingStars
                criterion="priceRating"
                label="القيمة مقابل السعر"
                value={reviewData.priceRating}
              />

              <RatingStars
                criterion="communicationRating"
                label="التواصل"
                value={reviewData.communicationRating}
              />
            </div>

            <div className="border-t border-b py-4 my-4">
              <RatingStars
                criterion="overallRating"
                label="التقييم العام (يتم حسابه تلقائيًا)"
                value={reviewData.overallRating}
              />
            </div>

            <div className="mb-4">
              <label
                className={`block font-medium mb-2 ${
                  darkMode ? "text-gray-300" : "text-gray-700"
                } transition-colors duration-300`}
              >
                التعليق <span className="text-red-500">*</span>
              </label>
              <textarea
                value={reviewData.comment}
                onChange={handleCommentChange}
                placeholder="اكتب تعليقك هنا..."
                className={`w-full p-3 rounded-md ${
                  darkMode
                    ? "bg-gray-700 border-gray-600 text-white"
                    : "bg-white border-gray-300 text-gray-800"
                } border ${
                  errors.comment ? "border-red-500 focus:ring-red-500" : ""
                } transition-colors duration-300 min-h-[100px]`}
                required
              ></textarea>
              {errors.comment && (
                <p className="text-red-500 text-sm mt-1">{errors.comment}</p>
              )}
            </div>

            <div className="mb-4">
              <label
                className={`block font-medium mb-2 ${
                  darkMode ? "text-gray-300" : "text-gray-700"
                } transition-colors duration-300`}
              >
                صور الأعمال المنجزة (اختياري)
              </label>
              <div
                className={`border-2 border-dashed rounded-md p-4 text-center ${
                  darkMode
                    ? "border-gray-600 hover:border-gray-500"
                    : "border-gray-300 hover:border-gray-400"
                } transition-colors duration-300`}
              >
                <input
                  type="file"
                  id="image-upload"
                  className="hidden"
                  accept="image/*"
                  multiple
                  onChange={handleImageUpload}
                />
                <label
                  htmlFor="image-upload"
                  className="cursor-pointer flex flex-col items-center justify-center"
                >
                  <Upload
                    size={32}
                    className={`mb-2 ${
                      darkMode ? "text-gray-400" : "text-gray-500"
                    }`}
                  />
                  <p
                    className={`${
                      darkMode ? "text-gray-300" : "text-gray-700"
                    } transition-colors duration-300`}
                  >
                    انقر لتحميل الصور أو اسحبها وأفلتها هنا
                  </p>
                  <p
                    className={`text-sm mt-1 ${
                      darkMode ? "text-gray-400" : "text-gray-500"
                    } transition-colors duration-300`}
                  >
                    (الحد الأقصى: 5 صور، 5 ميجابايت لكل صورة)
                  </p>
                </label>
              </div>
              {errors.images && (
                <p className="text-red-500 text-sm mt-1">{errors.images}</p>
              )}

              {/* معاينة الصور */}
              {previewImages.length > 0 && (
                <div className="mt-4 grid grid-cols-2 sm:grid-cols-3 gap-2">
                  {previewImages.map((src, index) => (
                    <div
                      key={index}
                      className="relative rounded-md overflow-hidden h-24"
                    >
                      <img
                        src={src}
                        alt={`Preview ${index + 1}`}
                        className="w-full h-full object-cover"
                      />
                      <button
                        type="button"
                        onClick={() => removeImage(index)}
                        className="absolute top-1 right-1 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 transition-colors duration-300"
                      >
                        <X size={16} />
                      </button>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {errors.general && (
              <div
                className={`p-3 rounded-md ${
                  darkMode ? "bg-red-900/50" : "bg-red-100"
                } ${
                  darkMode ? "text-red-200" : "text-red-800"
                } mb-4 transition-colors duration-300`}
              >
                {errors.general}
              </div>
            )}

            {/* رسالة تنبيه حول مدة تعديل التقييم */}
            {isEditing ? (
              <div
                className={`p-3 rounded-md ${
                  darkMode ? "bg-yellow-900/30" : "bg-yellow-50"
                } ${
                  darkMode ? "text-yellow-200" : "text-yellow-800"
                } mb-4 transition-colors duration-300 border ${
                  darkMode ? "border-yellow-800/30" : "border-yellow-200"
                }`}
              >
                <div className="flex items-center gap-2 justify-between">
                  <div className="flex items-center gap-2">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-5 w-5"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                      />
                    </svg>
                    <span className="font-medium">تنبيه:</span>
                  </div>
                  {existingReview && (
                    <CountdownTimer
                      endTime={new Date(new Date(existingReview.createdAt).getTime() + 10 * 60 * 1000)}
                      darkMode={darkMode}
                    />
                  )}
                </div>
                <p className="mt-1 text-sm">
                  يمكنك تعديل هذا التقييم خلال 10 دقائق فقط من وقت إنشائه. بعد ذلك، لن تتمكن من تعديله.
                </p>
              </div>
            ) : (
              <div
                className={`p-3 rounded-md ${
                  darkMode ? "bg-blue-900/30" : "bg-blue-50"
                } ${
                  darkMode ? "text-blue-200" : "text-blue-800"
                } mb-4 transition-colors duration-300 border ${
                  darkMode ? "border-blue-800/30" : "border-blue-200"
                }`}
              >
                <div className="flex items-center gap-2">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                  <span className="font-medium">ملاحظة:</span>
                </div>
                <p className="mt-1 text-sm">
                  يمكنك تعديل التقييم خلال 10 دقائق فقط بعد إنشائه. بعد ذلك، لن تتمكن من تعديله أو تغييره.
                </p>
              </div>
            )}

            <div className="flex justify-end space-x-2 space-x-reverse">
              <Button
                type="button"
                variant="secondary"
                onClick={onClose}
                className="ml-2"
                disabled={isSubmitting}
              >
                إلغاء
              </Button>
              <Button
                type="submit"
                variant="primary"
                disabled={isSubmitting}
                className={`text-white flex items-center justify-center gap-2 shadow-md hover:shadow-lg transition-all duration-200 relative overflow-hidden group py-2 px-6 ${
                  darkMode
                    ? "bg-gradient-to-r from-indigo-600 to-indigo-700 hover:from-indigo-700 hover:to-indigo-800"
                    : "bg-gradient-to-r from-indigo-500 to-indigo-600 hover:from-indigo-600 hover:to-indigo-700"
                }`}
              >
                <span className="relative z-10">
                  {isSubmitting
                    ? "جاري الإرسال..."
                    : isEditing
                    ? "تحديث التقييم"
                    : "إرسال التقييم"}
                </span>
                <span className="absolute inset-0 bg-white opacity-20 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700"></span>
              </Button>
            </div>
          </form>
        </div>
      </motion.div>
    </div>
  );
};

export default ReviewModal;
