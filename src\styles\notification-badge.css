/* تنسيق شارة الإشعارات */
.notification-badge {
  position: absolute;
  top: -2px;
  right: -2px;
  background-color: #f7e086; /* لون أصفر */
  color: #1e293b; /* لون نص داكن */
  border-radius: 9999px;
  font-size: 0.65rem;
  font-weight: bold;
  min-width: 1.25rem;
  height: 1.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 0.25rem;
  border: 2px solid rgb(245, 217, 37);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  transform: scale(1);
  animation: notification-pulse 2s infinite;
}

/* تنسيق شارة طلب الحجز المعلق */
.pending-badge {
  animation: pending-pulse 2s infinite;
  box-shadow: 0 0 0 2px white;
}

@keyframes notification-pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(243, 238, 111, 0.7);
  }
  70% {
    transform: scale(1.05);
    box-shadow: 0 0 0 5px rgba(251, 191, 36, 0);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(251, 191, 36, 0);
  }
}

@keyframes pending-pulse {
  0% {
    opacity: 1;
    transform: translate(50%, -50%) scale(1);
    box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.7);
  }
  50% {
    opacity: 0.9;
    transform: translate(50%, -50%) scale(1.1);
    box-shadow: 0 0 0 4px rgba(16, 185, 129, 0);
  }
  100% {
    opacity: 1;
    transform: translate(50%, -50%) scale(1);
    box-shadow: 0 0 0 0 rgba(16, 185, 129, 0);
  }
}
