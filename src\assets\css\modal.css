/* تنسيقات المودال للحرفيين */
.craftsman-modal-content {
  direction: rtl;
  font-family: 'Cairo', sans-serif;
}

.craftsman-modal-name {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  font-weight: bold;
  margin-bottom: 8px;
}

.craftsman-modal-profession {
  margin-bottom: 12px;
}

.craftsman-modal-rating,
.craftsman-modal-phone {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 4px;
}

/* تأثيرات الانتقال للأزرار */
.craftsman-modal-content button {
  transition: all 0.2s ease-in-out;
}

/* إزالة تأثير الارتفاع عند التمرير */
.craftsman-modal-content button:hover {
  transform: none;
}

/* تنسيقات للمجموعات */
.custom-marker-cluster {
  background-color: rgba(66, 56, 200, 0.7);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  box-shadow: 0 0 0 4px rgba(66, 56, 200, 0.2);
}

.cluster-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  font-size: 14px;
  font-weight: bold;
}

/* تنسيقات إضافية للمودال */
.ReactModal__Overlay {
  opacity: 0;
  transition: opacity 200ms ease-in-out;
}

.ReactModal__Overlay--after-open {
  opacity: 1;
}

.ReactModal__Overlay--before-close {
  opacity: 0;
}

.ReactModal__Content {
  transform: scale(0.9);
  transition: transform 200ms ease-in-out;
}

.ReactModal__Content--after-open {
  transform: scale(1);
}

.ReactModal__Content--before-close {
  transform: scale(0.9);
}
