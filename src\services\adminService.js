import api from "./api";

class AdminService {
  // تحديث بيانات الأدمن الشخصية
  async updateAdminProfile(adminData) {
    try {
      console.log("إرسال طلب تحديث بيانات الأدمن:", adminData);
      console.log("URL الطلب:", "/admin/profile");

      const response = await api.put("/admin/profile", adminData);

      console.log("استجابة تحديث بيانات الأدمن:", response.data);
      return response.data;
    } catch (error) {
      console.error("خطأ في تحديث بيانات الأدمن:", error);
      console.error("تفاصيل الخطأ:", error.response?.data);
      console.error("حالة الخطأ:", error.response?.status);
      throw error;
    }
  }

  // تحديث كلمة مرور الأدمن
  async updateAdminPassword(passwordData) {
    try {
      console.log("إرسال طلب تحديث كلمة مرور الأدمن");

      const response = await api.put("/admin/password", passwordData);

      console.log("استجابة تحديث كلمة مرور الأدمن:", response.data);
      return response.data;
    } catch (error) {
      console.error("خطأ في تحديث كلمة مرور الأدمن:", error);
      throw error;
    }
  }

  // رفع صورة الأدمن
  async uploadAdminImage(imageFile) {
    try {
      console.log("إرسال طلب رفع صورة الأدمن");

      const formData = new FormData();
      formData.append("image", imageFile);

      const response = await api.post("/admin/upload-image", formData, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      });

      console.log("استجابة رفع صورة الأدمن:", response.data);
      return response.data;
    } catch (error) {
      console.error("خطأ في رفع صورة الأدمن:", error);
      throw error;
    }
  }

  // الحصول على بيانات الأدمن
  async getAdminProfile() {
    try {
      console.log("إرسال طلب الحصول على بيانات الأدمن");

      const response = await api.get("/admin/profile");

      console.log("استجابة الحصول على بيانات الأدمن:", response.data);
      return response.data;
    } catch (error) {
      console.error("خطأ في الحصول على بيانات الأدمن:", error);
      throw error;
    }
  }

  // تحديث بيانات الأدمن مع الصورة
  async updateAdminProfileWithImage(adminData, imageFile = null) {
    try {
      let imageUrl = adminData.image;

      // رفع الصورة أولاً إذا كانت موجودة
      if (imageFile) {
        const imageResponse = await this.uploadAdminImage(imageFile);
        imageUrl = imageResponse.imageUrl || imageResponse.url;
      }

      // تحديث بيانات الأدمن مع رابط الصورة الجديد
      const updatedData = {
        ...adminData,
        image: imageUrl,
      };

      return await this.updateAdminProfile(updatedData);
    } catch (error) {
      console.error("خطأ في تحديث بيانات الأدمن مع الصورة:", error);
      throw error;
    }
  }

  // تسجيل دخول الأدمن
  async adminLogin(credentials) {
    try {
      console.log("إرسال طلب تسجيل دخول الأدمن");

      const response = await api.post("/admin/login", {
        email: credentials.email || credentials.username,
        password: credentials.password,
        rememberMe: credentials.rememberMe || false,
      });

      console.log("استجابة تسجيل دخول الأدمن:", response.data);
      return response.data;
    } catch (error) {
      console.error("خطأ في تسجيل دخول الأدمن:", error);
      throw error;
    }
  }

  // التحقق من مصادقة الأدمن
  async checkAdminAuth() {
    try {
      console.log("التحقق من مصادقة الأدمن");

      const response = await api.get("/admin/profile");

      console.log("استجابة التحقق من مصادقة الأدمن:", response.data);
      return response.data;
    } catch (error) {
      console.error("خطأ في التحقق من مصادقة الأدمن:", error);
      throw error;
    }
  }

  // الحصول على إحصائيات لوحة التحكم
  async getDashboardStats() {
    try {
      const response = await api.get("/admin/stats");
      return response.data;
    } catch (error) {
      console.error("خطأ في جلب الإحصائيات:", error);
      throw error;
    }
  }

  // إدارة المستخدمين
  async getAllUsers() {
    try {
      const response = await api.get("/admin/users");
      return response.data;
    } catch (error) {
      console.error("خطأ في جلب المستخدمين:", error);
      throw error;
    }
  }

  async updateUser(userId, userData) {
    try {
      const response = await api.put(`/admin/users/${userId}`, userData);
      return response.data;
    } catch (error) {
      console.error("خطأ في تحديث المستخدم:", error);
      throw error;
    }
  }

  async deleteUser(userId) {
    try {
      const response = await api.delete(`/admin/users/${userId}`);
      return response.data;
    } catch (error) {
      console.error("خطأ في حذف المستخدم:", error);
      throw error;
    }
  }

  // إدارة الحرفيين
  async getAllCraftsmen() {
    try {
      const response = await api.get("/admin/craftsmen");
      return response.data;
    } catch (error) {
      console.error("خطأ في جلب الحرفيين:", error);
      throw error;
    }
  }

  async updateCraftsman(craftsmanId, craftsmanData) {
    try {
      const response = await api.put(`/admin/craftsmen/${craftsmanId}`, craftsmanData);
      return response.data;
    } catch (error) {
      console.error("خطأ في تحديث الحرفي:", error);
      throw error;
    }
  }

  async deleteCraftsman(craftsmanId) {
    try {
      const response = await api.delete(`/admin/craftsmen/${craftsmanId}`);
      return response.data;
    } catch (error) {
      console.error("خطأ في حذف الحرفي:", error);
      throw error;
    }
  }

  // إدارة الحجوزات
  async getAllBookings() {
    try {
      const response = await api.get("/admin/bookings");
      return response.data;
    } catch (error) {
      console.error("خطأ في جلب الحجوزات:", error);
      throw error;
    }
  }

  async updateBookingStatus(bookingId, status) {
    try {
      const response = await api.put(`/admin/bookings/${bookingId}`, { status });
      return response.data;
    } catch (error) {
      console.error("خطأ في تحديث حالة الحجز:", error);
      throw error;
    }
  }
}

export default new AdminService();
