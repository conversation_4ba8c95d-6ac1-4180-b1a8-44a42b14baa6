import { create } from "zustand";
import { craftsmanService } from "../services/api";

// دالة للتحقق مما إذا كانت نقطة داخل مضلع
const isPointInPolygon = (point, polygon) => {
  // خوارزمية Ray Casting للتحقق مما إذا كانت نقطة داخل مضلع
  let inside = false;
  for (let i = 0, j = polygon.length - 1; i < polygon.length; j = i++) {
    const xi = polygon[i].lng;
    const yi = polygon[i].lat;
    const xj = polygon[j].lng;
    const yj = polygon[j].lat;

    const intersect =
      yi > point.lat !== yj > point.lat &&
      point.lng < ((xj - xi) * (point.lat - yi)) / (yj - yi) + xi;

    if (intersect) inside = !inside;
  }
  return inside;
};

const useCraftsmenStore = create((set, get) => ({
  craftsmen: [],
  filteredCraftsmen: [],
  loading: false,
  error: null,

  // جلب جميع الحرفيين من الخادم
  fetchCraftsmen: async () => {
    // التحقق مما إذا كان هناك استدعاء جارٍ بالفعل أو إذا كانت البيانات موجودة بالفعل
    if (get().loading || get().craftsmen.length > 0) {
      return get().craftsmen;
    }

    set({ loading: true, error: null });
    try {
      // استدعاء خدمة الحرفيين للحصول على البيانات من الخادم
      const response = await craftsmanService.getAllCraftsmen();

      // طباعة البيانات المستلمة للتصحيح فقط إذا كانت هناك بيانات
      if (
        response &&
        (Array.isArray(response)
          ? response.length > 0
          : response.craftsmen && response.craftsmen.length > 0)
      ) {
        console.log("API Response:", response);
      }

      // تحديث المتجر بالبيانات الجديدة
      // تعديل: استخدام response مباشرة إذا كان مصفوفة، أو response.craftsmen إذا كان كائنًا
      const craftsmenData = Array.isArray(response)
        ? response
        : response.craftsmen || [];

      // معالجة بيانات الحرفيين
      const processedCraftsmenData = craftsmenData;

      set({
        craftsmen: processedCraftsmenData,
        filteredCraftsmen: processedCraftsmenData,
        loading: false,
      });

      return craftsmenData;
    } catch (error) {
      console.error("Error fetching craftsmen:", error);

      // في حالة حدوث خطأ، نستخدم مصفوفة فارغة بدلاً من البيانات الوهمية
      set({
        craftsmen: [],
        filteredCraftsmen: [],
        loading: false,
        error: error.message,
      });

      return [];
    }
  },

  // جلب حرفي واحد بواسطة المعرف
  fetchCraftsman: async (id) => {
    set({ loading: true, error: null });
    try {
      console.log("Fetching craftsman with ID:", id);

      // تحويل المعرف إلى نص للمقارنة
      const idStr = id ? id.toString() : "";

      // البحث عن الحرفي في المتجر أولاً بمقارنة أكثر دقة
      const existingCraftsman = get().craftsmen.find((c) => {
        const cId = c.id ? c.id.toString() : "";
        const c_Id = c._id ? c._id.toString() : "";
        return cId === idStr || c_Id === idStr;
      });

      if (existingCraftsman) {
        console.log("Found existing craftsman in store:", existingCraftsman);
        set({ loading: false });
        return existingCraftsman;
      }

      // إذا لم يتم العثور عليه، جلبه من الخادم
      console.log("Craftsman not found in store, fetching from API...");
      const response = await craftsmanService.getCraftsmanById(id);

      // طباعة بيانات الحرفي للتأكد من استلامها بشكل صحيح
      console.log("Craftsman data from API:", response);

      if (!response) {
        throw new Error("لم يتم العثور على بيانات الحرفي");
      }

      // تعديل: استخدام response مباشرة إذا لم يكن يحتوي على حقل craftsman
      const craftsmanData = response.craftsman || response;

      if (!craftsmanData) {
        throw new Error("بيانات الحرفي غير صالحة");
      }

      // التأكد من أن البيانات تحتوي على معرف
      if (craftsmanData && !craftsmanData.id && craftsmanData._id) {
        craftsmanData.id = craftsmanData._id;
      }

      // معالجة مسارات الصور
      if (craftsmanData.image && !craftsmanData.image.startsWith("http")) {
        // إذا كان المسار نسبيًا، أضف عنوان الخادم
        const { SERVER_URL } = await import("../services/config");
        craftsmanData.image = `${SERVER_URL}${
          craftsmanData.image.startsWith("/") ? "" : "/"
        }${craftsmanData.image}`;
      }

      // تحديث قائمة الحرفيين في المتجر
      set((state) => ({
        craftsmen: [...state.craftsmen, craftsmanData],
        loading: false,
      }));

      return craftsmanData;
    } catch (error) {
      console.error("Error fetching craftsman:", error);
      set({
        loading: false,
        error: error.message || "حدث خطأ أثناء جلب بيانات الحرفي",
      });
      return null;
    }
  },

  // Filter craftsmen by various criteria
  filterCraftsmen: ({
    profession,
    professions,
    specialization,
    rating,
    location,
    radius,
    serviceAreas,
  }) => {
    set((state) => {
      let filtered = [...state.craftsmen];

      // طباعة معلومات التصحيح
      console.log("بدء تصفية الحرفيين:");
      console.log("- عدد الحرفيين الكلي:", state.craftsmen.length);
      console.log("- المهنة المحددة:", profession);
      console.log("- المهن المحددة:", professions);

      // طباعة بيانات الحرفيين للتصحيح
      if (state.craftsmen.length > 0) {
        console.log(
          "نموذج بيانات الحرفي الأول:",
          JSON.stringify(state.craftsmen[0], null, 2)
        );
      }

      // دعم تصفية متعددة للمهن
      if (professions && professions.length > 0) {
        // طباعة المهن المحددة للتصحيح
        console.log("المهن المحددة للفلترة:", professions);

        // تحويل المهن المحددة إلى تنسيق موحد للمقارنة
        const normalizedSelectedProfessions = professions
          .map((prof) => {
            if (typeof prof === "string") {
              return prof.trim().toLowerCase();
            } else if (typeof prof === "object" && prof.name) {
              return prof.name.trim().toLowerCase();
            }
            return "";
          })
          .filter((name) => name !== "");

        console.log(
          "المهن المحددة بعد التوحيد:",
          normalizedSelectedProfessions
        );

        // تصفية حسب مصفوفة المهن
        filtered = filtered.filter((craftsman) => {
          // طباعة بيانات الحرفي للتصحيح
          console.log(
            `فحص الحرفي: ${craftsman.name}, المهن:`,
            craftsman.professions || craftsman.profession || "غير محدد"
          );

          // تجميع جميع المهن المحتملة للحرفي في مصفوفة واحدة
          let craftsmanProfessions = [];

          // إضافة المهن من مصفوفة المهن إذا كانت موجودة
          if (
            craftsman.professions &&
            Array.isArray(craftsman.professions) &&
            craftsman.professions.length > 0
          ) {
            craftsmanProfessions = [
              ...craftsmanProfessions,
              ...craftsman.professions
                .map((prof) => {
                  if (typeof prof === "string") {
                    return prof.trim().toLowerCase();
                  } else if (typeof prof === "object" && prof.name) {
                    return prof.name.trim().toLowerCase();
                  }
                  return "";
                })
                .filter((name) => name !== ""),
            ];
          }

          // إضافة المهنة الرئيسية إذا كانت موجودة
          if (craftsman.profession) {
            if (typeof craftsman.profession === "string") {
              craftsmanProfessions.push(
                craftsman.profession.trim().toLowerCase()
              );
            } else if (
              typeof craftsman.profession === "object" &&
              craftsman.profession.name
            ) {
              craftsmanProfessions.push(
                craftsman.profession.name.trim().toLowerCase()
              );
            }
          }

          // إضافة المهنة من حقل profession_id إذا كان موجودًا
          if (craftsman.profession_id) {
            // يمكن إضافة منطق هنا للبحث عن اسم المهنة بناءً على الرقم
            // لكن هذا يتطلب قائمة مرجعية بجميع المهن
          }

          console.log(`مهن الحرفي بعد التوحيد:`, craftsmanProfessions);

          // التحقق من وجود تطابق بين مهن الحرفي والمهن المحددة
          const hasMatch = normalizedSelectedProfessions.some((selectedProf) =>
            craftsmanProfessions.includes(selectedProf)
          );

          if (hasMatch) {
            console.log(`الحرفي ${craftsman.name} يتطابق مع المهن المحددة`);
          }

          return hasMatch;
        });

        // طباعة عدد الحرفيين بعد التصفية للتصحيح
        console.log(`تمت التصفية حسب المهن: ${filtered.length} حرفي متبقي`);
      } else if (profession) {
        // للتوافق مع الكود القديم
        console.log("تصفية حسب مهنة واحدة:", profession);

        // تحويل المهنة المحددة إلى تنسيق موحد للمقارنة
        let normalizedProfession = "";
        if (typeof profession === "string") {
          normalizedProfession = profession.trim().toLowerCase();
        } else if (typeof profession === "object" && profession.name) {
          normalizedProfession = profession.name.trim().toLowerCase();
        }

        console.log("المهنة المحددة بعد التوحيد:", normalizedProfession);

        filtered = filtered.filter((craftsman) => {
          // تجميع جميع المهن المحتملة للحرفي في مصفوفة واحدة
          let craftsmanProfessions = [];

          // إضافة المهن من مصفوفة المهن إذا كانت موجودة
          if (
            craftsman.professions &&
            Array.isArray(craftsman.professions) &&
            craftsman.professions.length > 0
          ) {
            craftsmanProfessions = [
              ...craftsmanProfessions,
              ...craftsman.professions
                .map((prof) => {
                  if (typeof prof === "string") {
                    return prof.trim().toLowerCase();
                  } else if (typeof prof === "object" && prof.name) {
                    return prof.name.trim().toLowerCase();
                  }
                  return "";
                })
                .filter((name) => name !== ""),
            ];
          }

          // إضافة المهنة الرئيسية إذا كانت موجودة
          if (craftsman.profession) {
            if (typeof craftsman.profession === "string") {
              craftsmanProfessions.push(
                craftsman.profession.trim().toLowerCase()
              );
            } else if (
              typeof craftsman.profession === "object" &&
              craftsman.profession.name
            ) {
              craftsmanProfessions.push(
                craftsman.profession.name.trim().toLowerCase()
              );
            }
          }

          // التحقق من وجود تطابق بين مهن الحرفي والمهنة المحددة
          const hasMatch = craftsmanProfessions.includes(normalizedProfession);

          if (hasMatch) {
            console.log(`الحرفي ${craftsman.name} يتطابق مع المهنة المحددة`);
          }

          return hasMatch;
        });

        // طباعة عدد الحرفيين بعد التصفية للتصحيح
        console.log(
          `تمت التصفية حسب مهنة واحدة: ${filtered.length} حرفي متبقي`
        );
      }

      if (specialization) {
        filtered = filtered.filter((c) => c.specialization === specialization);
      }

      // تم تعديل الفلترة بناءً على الإتاحة لاستخدام دالة isAvailableNow من ملف availabilityUtils.js
      // يتم استخدام هذه الدالة في واجهة المستخدم مباشرة

      if (rating) {
        filtered = filtered.filter((c) => c.rating >= rating);
      }

      // فلترة بناءً على الموقع ونطاق العمل - استخدام منطق تقاطع الدوائر
      if (location && radius) {
        // تحسين حساب المسافة والتعامل مع هياكل البيانات المختلفة
        filtered = filtered.filter((c) => {
          // التعامل مع الحالات المختلفة لهيكل البيانات
          const craftsmanLat = c.latitude || (c.location && c.location.lat);
          const craftsmanLng = c.longitude || (c.location && c.location.lng);

          // إذا لم تكن هناك إحداثيات، استبعاد الحرفي
          if (!craftsmanLat || !craftsmanLng) {
            console.log("Craftsman without location:", c);
            return false;
          }

          // دالة حساب المسافة بين نقطتين (بالمتر)
          const calculateDistance = (lat1, lng1, lat2, lng2) => {
            const R = 6371e3; // نصف قطر الأرض بالمتر
            const φ1 = (lat1 * Math.PI) / 180;
            const φ2 = (lat2 * Math.PI) / 180;
            const Δφ = ((lat2 - lat1) * Math.PI) / 180;
            const Δλ = ((lng2 - lng1) * Math.PI) / 180;

            const a =
              Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
              Math.cos(φ1) * Math.cos(φ2) * Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
            const angle = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

            return R * angle;
          };

          // دالة للتحقق من تقاطع دائرتين أو احتواء إحداهما للأخرى
          const doCirclesIntersectOrContain = (
            center1Lat,
            center1Lng,
            radius1, // الدائرة الأولى (نطاق البحث)
            center2Lat,
            center2Lng,
            radius2 // الدائرة الثانية (نطاق عمل الحرفي)
          ) => {
            // حساب المسافة بين مراكز الدائرتين
            const distance = calculateDistance(
              center1Lat,
              center1Lng,
              center2Lat,
              center2Lng
            );

            // الحالة 1: مركز الحرفي داخل نطاق البحث
            const craftsmanCenterInSearchRadius = distance <= radius1;

            // الحالة 2: مركز البحث داخل نطاق عمل الحرفي
            const searchCenterInCraftsmanRadius = distance <= radius2;

            // الحالة 3: تقاطع الدوائر (بدون احتواء كامل)
            const sumOfRadii = radius1 + radius2;
            const circlesIntersect = distance <= sumOfRadii;

            // يظهر الحرفي إذا تحققت أي من الحالات الثلاث
            return (
              craftsmanCenterInSearchRadius ||
              searchCenterInCraftsmanRadius ||
              circlesIntersect
            );
          };

          // الحصول على نطاق عمل الحرفي (بالمتر)
          const craftsmanWorkRadius = (c.workRadius || 1) * 1000; // تحويل من كم إلى متر
          const searchRadius = radius * 1000; // تحويل من كم إلى متر

          // التحقق من تقاطع دائرة نطاق البحث مع دائرة نطاق عمل الحرفي أو احتواء إحداهما للأخرى
          const shouldShow = doCirclesIntersectOrContain(
            location.lat,
            location.lng,
            searchRadius, // نطاق البحث (بالمتر)
            craftsmanLat,
            craftsmanLng,
            craftsmanWorkRadius // نطاق عمل الحرفي (بالمتر)
          );

          // طباعة معلومات التصفية للتصحيح
          if (c.name) {
            console.log(`Store Filter - Craftsman ${c.name}:`, {
              searchRadius: searchRadius / 1000, // عرض بالكيلومتر
              craftsmanWorkRadius: craftsmanWorkRadius / 1000, // عرض بالكيلومتر
              distance:
                calculateDistance(
                  location.lat,
                  location.lng,
                  craftsmanLat,
                  craftsmanLng
                ) / 1000, // عرض بالكيلومتر
              shouldShow,
            });
          }

          return shouldShow;
        });
      }

      // فلترة بناءً على مناطق الخدمة المخصصة
      if (serviceAreas && serviceAreas.length > 0) {
        console.log(
          "تطبيق الفلترة بناءً على مناطق الخدمة المخصصة:",
          serviceAreas
        );

        filtered = filtered.filter((c) => {
          // التعامل مع الحالات المختلفة لهيكل البيانات
          const craftsmanLat = c.latitude || (c.location && c.location.lat);
          const craftsmanLng = c.longitude || (c.location && c.location.lng);

          // إذا لم تكن هناك إحداثيات، استبعاد الحرفي
          if (!craftsmanLat || !craftsmanLng) {
            return false;
          }

          // التحقق مما إذا كان الحرفي يقع داخل أي من المناطق المرسومة
          return serviceAreas.some((area) => {
            if (area.type === "polygon") {
              // التحقق مما إذا كان الحرفي داخل المضلع
              return isPointInPolygon(
                { lat: craftsmanLat, lng: craftsmanLng },
                area.coordinates.map((coord) => ({
                  lat: coord[0],
                  lng: coord[1],
                }))
              );
            } else if (area.type === "circle") {
              // التحقق مما إذا كان الحرفي داخل الدائرة
              const R = 6371; // نصف قطر الأرض بالكيلومتر
              const dLat = ((area.center[0] - craftsmanLat) * Math.PI) / 180;
              const dLng = ((area.center[1] - craftsmanLng) * Math.PI) / 180;
              const a =
                Math.sin(dLat / 2) * Math.sin(dLat / 2) +
                Math.cos((craftsmanLat * Math.PI) / 180) *
                  Math.cos((area.center[0] * Math.PI) / 180) *
                  Math.sin(dLng / 2) *
                  Math.sin(dLng / 2);
              const angle = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
              const distance = R * angle; // المسافة بالكيلومتر

              return distance <= area.radius / 1000; // تحويل من متر إلى كيلومتر
            }
            return false;
          });
        });
      }

      return { filteredCraftsmen: filtered };
    });
  },

  // Get a single craftsman by ID
  getCraftsmanById: (id) => {
    if (!id) return null;

    // تحويل المعرف إلى نص للمقارنة
    const idStr = id.toString();

    // البحث عن الحرفي بمقارنة أكثر مرونة
    return useCraftsmenStore.getState().craftsmen.find((c) => {
      const cId = c.id ? c.id.toString() : "";
      const c_Id = c._id ? c._id.toString() : "";
      return cId === idStr || c_Id === idStr;
    });
  },
}));

export default useCraftsmenStore;
