/**
 * خدمة رفع الصور باستخدام imgbb API
 */

const IMGBB_API_KEY = "42fc3f62a897f929c107af05c14d14ee"; // مفتاح imgbb API
const IMGBB_API_URL = "https://api.imgbb.com/1/upload";

/**
 * رفع صورة واحدة إلى imgbb
 * @param {File} file - ملف الصورة
 * @param {string} name - اسم الصورة (اختياري)
 * @returns {Promise<Object>} - نتيجة الرفع
 */
export const uploadImageToImgbb = async (file, name = null) => {
  try {
    // التحقق من نوع الملف
    if (
      !file.type ||
      typeof file.type !== "string" ||
      !file.type.startsWith("image/")
    ) {
      throw new Error("يجب أن يكون الملف صورة");
    }

    // التحقق من حجم الملف (32MB حد أقصى لـ imgbb)
    const maxSize = 32 * 1024 * 1024; // 32MB
    if (file.size > maxSize) {
      throw new Error("حجم الصورة كبير جداً. الحد الأقصى 32MB");
    }

    // إنشاء FormData
    const formData = new FormData();
    formData.append("key", IMGBB_API_KEY);
    formData.append("image", file);

    if (name) {
      formData.append("name", name);
    }

    // رفع الصورة
    const response = await fetch(IMGBB_API_URL, {
      method: "POST",
      body: formData,
    });

    const result = await response.json();

    if (!result.success) {
      throw new Error(result.error?.message || "فشل في رفع الصورة");
    }

    // إرجاع معلومات الصورة
    return {
      success: true,
      data: {
        id: result.data.id,
        url: result.data.url,
        display_url: result.data.display_url,
        thumb: result.data.thumb.url,
        medium: result.data.medium?.url || result.data.url,
        delete_url: result.data.delete_url,
        size: result.data.size,
        filename: result.data.image.filename,
        extension: result.data.image.extension,
      },
    };
  } catch (error) {
    console.error("خطأ في رفع الصورة:", error);
    return {
      success: false,
      error: error.message,
    };
  }
};

/**
 * رفع عدة صور دفعة واحدة
 * @param {FileList|Array} files - قائمة الملفات
 * @param {Function} onProgress - دالة تتبع التقدم (اختياري)
 * @returns {Promise<Array>} - نتائج الرفع
 */
export const uploadMultipleImages = async (files, onProgress = null) => {
  const results = [];
  const totalFiles = files.length;

  for (let i = 0; i < totalFiles; i++) {
    const file = files[i];

    try {
      // رفع الصورة
      const result = await uploadImageToImgbb(
        file,
        `gallery_${Date.now()}_${i}`
      );
      results.push(result);

      // تحديث التقدم
      if (onProgress) {
        onProgress({
          current: i + 1,
          total: totalFiles,
          percentage: Math.round(((i + 1) / totalFiles) * 100),
          currentFile: file.name,
          result: result,
        });
      }
    } catch (error) {
      console.error(`خطأ في رفع الصورة ${file.name}:`, error);
      results.push({
        success: false,
        error: error.message,
        filename: file.name,
      });
    }
  }

  return results;
};

/**
 * ضغط الصورة قبل الرفع (اختياري)
 * @param {File} file - ملف الصورة
 * @param {number} quality - جودة الضغط (0.1 - 1.0)
 * @param {number} maxWidth - العرض الأقصى
 * @param {number} maxHeight - الارتفاع الأقصى
 * @returns {Promise<File>} - الصورة المضغوطة
 */
export const compressImage = async (
  file,
  quality = 0.8,
  maxWidth = 1920,
  maxHeight = 1080
) => {
  return new Promise((resolve) => {
    const canvas = document.createElement("canvas");
    const ctx = canvas.getContext("2d");
    const img = new Image();

    img.onload = () => {
      // حساب الأبعاد الجديدة
      let { width, height } = img;

      if (width > maxWidth) {
        height = (height * maxWidth) / width;
        width = maxWidth;
      }

      if (height > maxHeight) {
        width = (width * maxHeight) / height;
        height = maxHeight;
      }

      // رسم الصورة على Canvas
      canvas.width = width;
      canvas.height = height;
      ctx.drawImage(img, 0, 0, width, height);

      // تحويل إلى Blob
      canvas.toBlob(
        (blob) => {
          const compressedFile = new File([blob], file.name, {
            type: file.type,
            lastModified: Date.now(),
          });
          resolve(compressedFile);
        },
        file.type,
        quality
      );
    };

    img.src = URL.createObjectURL(file);
  });
};

/**
 * التحقق من صحة الصورة
 * @param {File} file - ملف الصورة
 * @returns {Object} - نتيجة التحقق
 */
export const validateImage = (file) => {
  const errors = [];

  // التحقق من نوع الملف
  const allowedTypes = [
    "image/jpeg",
    "image/jpg",
    "image/png",
    "image/gif",
    "image/webp",
  ];
  if (
    !file.type ||
    typeof file.type !== "string" ||
    !allowedTypes.includes(file.type)
  ) {
    errors.push("نوع الملف غير مدعوم. الأنواع المدعومة: JPEG, PNG, GIF, WebP");
  }

  // التحقق من حجم الملف
  const maxSize = 32 * 1024 * 1024; // 32MB
  if (file.size > maxSize) {
    errors.push("حجم الملف كبير جداً. الحد الأقصى 32MB");
  }

  const minSize = 1024; // 1KB
  if (file.size < minSize) {
    errors.push("حجم الملف صغير جداً");
  }

  return {
    isValid: errors.length === 0,
    errors: errors,
  };
};

/**
 * حذف صورة من imgbb (إذا كان لديك delete_url)
 * @param {string} deleteUrl - رابط الحذف
 * @returns {Promise<boolean>} - نتيجة الحذف
 */
export const deleteImageFromImgbb = async (deleteUrl) => {
  try {
    const response = await fetch(deleteUrl, {
      method: "GET", // imgbb يستخدم GET للحذف
    });

    return response.ok;
  } catch (error) {
    console.error("خطأ في حذف الصورة:", error);
    return false;
  }
};

/**
 * الحصول على رابط الصورة بحجم محدد
 * @param {string|object} originalUrl - الرابط الأصلي أو كائن يحتوي على بيانات الصورة
 * @param {string} size - الحجم المطلوب (thumb, medium, original)
 * @returns {string} - رابط الصورة بالحجم المطلوب
 */
export const getImageUrl = (originalUrl, size = "medium") => {
  // تجاهل متغير size مؤقتاً لأن imgbb لا يدعم تغيير الحجم عبر URL
  void size;
  // التحقق من صحة المدخل
  if (!originalUrl) return "/img/default-avatar-2-modified.svg";

  // إذا كان المدخل كائن
  if (typeof originalUrl === "object") {
    // إذا كان يحتوي على خاصية url
    if (originalUrl.url) {
      originalUrl = originalUrl.url;
    }
    // إذا كان يحتوي على خاصية display_url (من imgbb)
    else if (originalUrl.display_url) {
      originalUrl = originalUrl.display_url;
    }
    // إذا كان الكائن نفسه يحتوي على مسار الصورة كـ string
    else if (
      typeof originalUrl === "object" &&
      Object.keys(originalUrl).length > 0
    ) {
      // محاولة العثور على مسار الصورة في الكائن
      const possiblePaths = Object.values(originalUrl).filter(
        (value) =>
          typeof value === "string" &&
          (value.startsWith("/uploads/") ||
            value.startsWith("http") ||
            value.includes("i.ibb.co"))
      );

      if (possiblePaths.length > 0) {
        originalUrl = possiblePaths[0];
      } else {
        console.warn(
          "getImageUrl: لم يتم العثور على مسار صورة صالح في الكائن:",
          originalUrl
        );
        return "/img/default-avatar-2-modified.svg";
      }
    } else {
      console.warn("getImageUrl: كائن غير صالح:", originalUrl);
      return "/img/default-avatar-2-modified.svg";
    }
  }

  // إذا لم يكن نص، حوله إلى نص
  if (typeof originalUrl !== "string") {
    console.warn("getImageUrl: originalUrl is not a string:", originalUrl);
    return "/img/default-avatar-2-modified.svg";
  }

  // إذا كان مسار نسبي، أضف عنوان الخادم
  if (originalUrl.startsWith("/uploads/")) {
    // استخدم عنوان الخادم المباشر
    const fullUrl = `https://jobscope-8t58.onrender.com${originalUrl}`;
    console.log("تحويل مسار نسبي إلى مسار كامل:", originalUrl, "->", fullUrl);
    return fullUrl;
  }

  // إذا كان الرابط من imgbb
  if (originalUrl.includes("i.ibb.co")) {
    // imgbb لا يدعم تغيير الحجم عبر URL، نعيد الرابط الأصلي
    return originalUrl;
  }

  return originalUrl;
};

export default {
  uploadImageToImgbb,
  uploadMultipleImages,
  compressImage,
  validateImage,
  deleteImageFromImgbb,
  getImageUrl,
};
