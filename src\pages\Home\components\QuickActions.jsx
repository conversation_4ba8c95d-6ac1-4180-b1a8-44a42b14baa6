import React from "react";
import { Link } from "react-router-dom";
import { motion } from "framer-motion";
import { Search, FileText, User } from "lucide-react";
import Card from "../../../components/common/Card";
import Button from "../../../components/common/Button";
import useThemeStore from "../../../store/themeStore";

const QuickActions = () => {
  const darkMode = useThemeStore((state) => state.darkMode);

  // تكوين البطاقات
  const actionCards = [
    {
      title: "البحث عن حرفي",
      description: "ابحث عن الحرفي المناسب في منطقتك",
      icon: <Search size={28} />,
      path: "/search",
      buttonText: "ابحث الآن",
      delay: 0,
    },
    {
      title: "طلباتي",
      description: "استعرض طلباتك السابقة والحالية",
      icon: <FileText size={28} />,
      path: "/bookings",
      buttonText: "عرض الطلبات",
      delay: 0.1,
    },
    {
      title: "ملفي الشخصي",
      description: "عرض وتعديل معلومات ملفك الشخصي",
      icon: <User size={28} />,
      path: "/profile/my",
      buttonText: "عرض الملف",
      delay: 0.2,
    },
  ];

  return (
    <section className="mb-10">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {actionCards.map((card, index) => (
          <motion.div
            key={index}
            whileHover={{
              y: -5,
              boxShadow: "0 15px 30px -5px rgba(66, 56, 200, 0.15)"
            }}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.4, delay: card.delay }}
            className="group"
          >
            <Card
              className={`flex flex-col items-center p-0 text-center ${
                darkMode
                  ? "bg-gray-800/90 text-gray-200 border border-gray-700"
                  : "bg-white/95 border border-indigo-100/60"
              } shadow-lg hover:shadow-xl transition-all duration-300 rounded-xl h-full backdrop-blur-sm overflow-hidden ${
                !darkMode && "hover:bg-gradient-to-b hover:from-white hover:to-indigo-50/30"
              }`}
            >
              {/* Encabezado con gradiente */}
              <div className={`w-full py-6 px-4 ${
                darkMode
                  ? "bg-gradient-to-br from-indigo-800 via-indigo-900 to-purple-900"
                  : "bg-gradient-to-br from-blue-700 via-indigo-800 to-blue-700"
              } relative mb-6`}>
                {/* Patrón decorativo */}
                <div className="absolute inset-0 opacity-10">
                  <div className="absolute top-0 left-0 w-full h-full"
                       style={{
                         backgroundImage: 'url("data:image/svg+xml,%3Csvg width=\'100\' height=\'100\' viewBox=\'0 0 100 100\' xmlns=\'http://www.w3.org/2000/svg\'%3E%3Cpath d=\'M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z\' fill=\'%23ffffff\' fill-opacity=\'0.1\' fill-rule=\'evenodd\'/%3E%3C/svg%3E")',
                         backgroundSize: '180px 180px'
                       }}>
                  </div>
                </div>

                {/* Efectos de luz */}
                <div className="absolute -bottom-16 -left-16 w-48 h-48 rounded-full bg-white opacity-10 blur-xl"></div>
                <div className="absolute -top-16 -right-16 w-48 h-48 rounded-full bg-white opacity-10 blur-xl"></div>

                {/* Icono con efecto de elevación */}
                <div className="relative">
                  <div className={`w-20 h-20 mx-auto bg-white/10 backdrop-blur-sm text-white
                    rounded-full flex items-center justify-center shadow-lg transition-all duration-300
                    border-2 border-white/20 group-hover:border-white/30 relative z-10`}
                  >
                    {card.icon}
                  </div>

                  {/* Efecto de resplandor */}
                  <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-16 h-16 rounded-full bg-white/30 blur-xl z-0"></div>
                </div>
              </div>

              <div className="px-6 pb-6 flex flex-col flex-1">
                <h3
                  className={`text-xl font-bold mb-3 ${
                    darkMode ? "text-indigo-300" : "text-indigo-700"
                  } relative inline-block transition-colors duration-300`}
                >
                  {card.title}
                  <div className={`h-1 w-12 mx-auto mt-2 rounded-full ${
                    darkMode ? "bg-indigo-500/50" : "bg-indigo-400/50"
                  }`}></div>
                </h3>

                <p
                  className={`${
                    darkMode ? "text-gray-400" : "text-gray-600"
                  } mb-6 transition-colors duration-300`}
                >
                  {card.description}
                </p>

                <Link to={card.path} className="mt-auto w-full">
                  <Button
                    variant="primary"
                    className={`w-full text-white flex items-center justify-center gap-2 shadow-lg hover:shadow-xl transition-all duration-300 relative overflow-hidden group py-3 px-4 rounded-lg ${
                      darkMode
                        ? "bg-gradient-to-r from-indigo-600 to-purple-700 hover:from-indigo-700 hover:to-purple-800"
                        : "bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700"
                    }`}
                  >
                    <span className="relative z-10 font-medium">{card.buttonText}</span>

                    {/* Icono de flecha con animación mejorada */}
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-5 w-5 mr-1 relative z-10 transition-transform duration-300 group-hover:-translate-x-1"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M15 19l-7-7 7-7"
                      />
                    </svg>

                    {/* Efecto de brillo mejorado */}
                    <span className="absolute inset-0 bg-white opacity-20 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700"></span>
                  </Button>
                </Link>
              </div>
            </Card>
          </motion.div>
        ))}
      </div>
    </section>
  );
};

export default QuickActions;
