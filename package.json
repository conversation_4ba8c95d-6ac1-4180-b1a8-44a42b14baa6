{"name": "jobscope-tailwind", "private": true, "version": "0.0.1", "scripts": {"dev": "vite", "backend": "cd backend && npm run dev", "start": "concurrently \"npm run dev\" \"npm run backend\"", "build": "vite build", "preview": "vite preview"}, "devDependencies": {"@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.21", "concurrently": "^9.1.2", "postcss": "^8.4.31", "tailwindcss": "^3.4.1", "vite": "^4.5.0"}, "dependencies": {"@mapbox/mapbox-gl-geocoder": "^5.0.3", "@react-oauth/google": "^0.12.1", "@supabase/supabase-js": "^2.49.8", "@tensorflow-models/mobilenet": "^2.1.1", "@tensorflow/tfjs": "^4.22.0", "@uploadcare/react-widget": "^2.4.7", "axios": "^1.9.0", "bcryptjs": "^3.0.2", "compromise": "^14.14.4", "dotenv": "^16.5.0", "firebase": "^11.8.1", "form-data": "^4.0.2", "framer-motion": "^12.10.5", "jwt-decode": "^4.0.0", "leaflet": "^1.9.4", "leaflet-draw": "^1.0.4", "leaflet.markercluster": "^1.5.3", "lucide-react": "^0.501.0", "mailgun.js": "^12.0.1", "mapbox-gl": "^3.11.0", "mongodb": "^6.16.0", "mongoose": "^8.15.1", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hot-toast": "^2.5.2", "react-lazy-load-image-component": "^1.6.3", "react-leaflet": "^5.0.0", "react-modal": "^3.16.3", "react-router-dom": "^7.5.1", "react-simple-chatbot": "^0.6.1", "react-tooltip": "^5.28.1", "styled-components": "^6.1.17", "zustand": "^5.0.3"}}