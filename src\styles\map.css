/* أنماط مخصصة للخريطة ونطاقات عمل الحرفيين */

/* تنسيق tooltip نطاق عمل الحرفي */
.craftsman-radius-tooltip {
  background: rgba(255, 255, 255, 0.95) !important;
  border: none !important;
  border-radius: 8px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  padding: 8px 12px !important;
  font-family: 'Cairo', sans-serif !important;
  backdrop-filter: blur(10px) !important;
}

.craftsman-radius-tooltip::before {
  border-top-color: rgba(255, 255, 255, 0.95) !important;
}

/* تحسين مظهر العلامات للمستخدمين غير المسجلين */
.unregistered-user-map .leaflet-marker-icon {
  cursor: not-allowed !important;
  opacity: 0.7;
  filter: grayscale(20%);
}

.unregistered-user-map .leaflet-marker-icon:hover {
  opacity: 0.9;
  transform: scale(1.05);
  transition: all 0.2s ease;
}

/* تحسين مظهر النوافذ المنبثقة للحرفيين */
.craftsman-popup {
  font-family: 'Cairo', sans-serif !important;
}

.craftsman-popup .leaflet-popup-content-wrapper {
  background: rgba(255, 255, 255, 0.98) !important;
  border-radius: 12px !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12) !important;
  backdrop-filter: blur(10px) !important;
}

.craftsman-popup .leaflet-popup-tip {
  background: rgba(255, 255, 255, 0.98) !important;
}

/* تحسين مظهر مجموعات العلامات */
.custom-marker-cluster {
  background: rgba(59, 130, 246, 0.9) !important;
  border-radius: 50% !important;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3) !important;
}

.cluster-icon {
  color: white !important;
  font-weight: bold !important;
  font-size: 14px !important;
  line-height: 40px !important;
  text-align: center !important;
  font-family: 'Cairo', sans-serif !important;
}

/* تحسين مظهر دائرة نطاق البحث */
.search-radius-circle {
  animation: pulse-search 2s infinite;
}

@keyframes pulse-search {
  0% {
    opacity: 0.6;
  }
  50% {
    opacity: 0.8;
  }
  100% {
    opacity: 0.6;
  }
}

/* تحسين مظهر دوائر نطاق عمل الحرفيين */
.craftsman-work-radius {
  transition: all 0.3s ease;
}

.craftsman-work-radius:hover {
  opacity: 0.8 !important;
  stroke-width: 3 !important;
}

/* تحسين الألوان في الوضع المظلم */
.dark .craftsman-radius-tooltip {
  background: rgba(31, 41, 55, 0.95) !important;
  color: #f9fafb !important;
}

.dark .craftsman-radius-tooltip::before {
  border-top-color: rgba(31, 41, 55, 0.95) !important;
}

.dark .craftsman-popup .leaflet-popup-content-wrapper {
  background: rgba(31, 41, 55, 0.98) !important;
  color: #f9fafb !important;
}

.dark .craftsman-popup .leaflet-popup-tip {
  background: rgba(31, 41, 55, 0.98) !important;
}

/* تحسين استجابة الخريطة */
@media (max-width: 768px) {
  .craftsman-radius-tooltip {
    font-size: 12px !important;
    padding: 6px 8px !important;
  }
  
  .cluster-icon {
    font-size: 12px !important;
    line-height: 35px !important;
  }
}

/* تأثيرات بصرية للدوائر */
.craftsman-circle-emerald {
  filter: drop-shadow(0 0 8px rgba(16, 185, 129, 0.3));
}

.craftsman-circle-blue {
  filter: drop-shadow(0 0 8px rgba(59, 130, 246, 0.3));
}

.craftsman-circle-violet {
  filter: drop-shadow(0 0 8px rgba(139, 92, 246, 0.3));
}

.craftsman-circle-amber {
  filter: drop-shadow(0 0 8px rgba(245, 158, 11, 0.3));
}

.craftsman-circle-red {
  filter: drop-shadow(0 0 8px rgba(239, 68, 68, 0.3));
}

/* تحسين أداء الرسوم المتحركة */
.leaflet-zoom-animated {
  will-change: transform;
}

.leaflet-interactive {
  will-change: transform;
}
