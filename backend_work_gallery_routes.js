// ملف routes للباك إند - معرض الأعمال
// يجب إضافة هذا الكود إلى ملف routes/craftsmen.js في الباك إند

const express = require('express');
const router = express.Router();
const Craftsman = require('../models/Craftsman'); // تأكد من المسار الصحيح
const auth = require('../middleware/auth'); // تأكد من المسار الصحيح

// GET /api/craftsmen/:id/gallery - جلب معرض أعمال حرفي محدد
router.get('/:id/gallery', async (req, res) => {
  try {
    const { id } = req.params;
    
    let craftsman;
    if (id === 'me') {
      // إذا كان المستخدم يطلب معرضه الخاص
      if (!req.user) {
        return res.status(401).json({ 
          success: false, 
          message: 'يجب تسجيل الدخول أولاً' 
        });
      }
      craftsman = await Craftsman.findById(req.user.id);
    } else {
      // جلب معرض حرفي آخر
      craftsman = await Craftsman.findById(id);
    }

    if (!craftsman) {
      return res.status(404).json({ 
        success: false, 
        message: 'الحرفي غير موجود' 
      });
    }

    // إرجاع معرض الأعمال
    res.json({
      success: true,
      workGallery: craftsman.workGallery || [],
      gallery: craftsman.workGallery || [] // للتوافق مع الكود القديم
    });

  } catch (error) {
    console.error('خطأ في جلب معرض الأعمال:', error);
    res.status(500).json({ 
      success: false, 
      message: 'حدث خطأ أثناء جلب معرض الأعمال' 
    });
  }
});

// PUT /api/craftsmen/me/gallery - تحديث معرض الأعمال
router.put('/me/gallery', auth, async (req, res) => {
  try {
    const { workGallery } = req.body;

    // التحقق من صحة البيانات
    if (!Array.isArray(workGallery)) {
      return res.status(400).json({ 
        success: false, 
        message: 'معرض الأعمال يجب أن يكون مصفوفة' 
      });
    }

    // التحقق من أن جميع العناصر هي روابط صالحة
    const validUrls = workGallery.filter(url => 
      typeof url === 'string' && 
      (url.startsWith('http') || url.startsWith('/uploads/'))
    );

    // تحديث معرض الأعمال
    const craftsman = await Craftsman.findByIdAndUpdate(
      req.user.id,
      { workGallery: validUrls },
      { new: true }
    );

    if (!craftsman) {
      return res.status(404).json({ 
        success: false, 
        message: 'الحرفي غير موجود' 
      });
    }

    res.json({
      success: true,
      message: 'تم تحديث معرض الأعمال بنجاح',
      workGallery: craftsman.workGallery,
      craftsman: {
        id: craftsman._id,
        workGallery: craftsman.workGallery
      }
    });

  } catch (error) {
    console.error('خطأ في تحديث معرض الأعمال:', error);
    res.status(500).json({ 
      success: false, 
      message: 'حدث خطأ أثناء تحديث معرض الأعمال' 
    });
  }
});

// POST /api/craftsmen/me/upload-gallery - رفع صور جديدة (للتوافق مع الكود القديم)
router.post('/me/upload-gallery', auth, async (req, res) => {
  try {
    // هذا الـ endpoint للتوافق مع الكود القديم فقط
    // الآن نستخدم imgbb لرفع الصور، لذلك هذا الـ endpoint لن يستخدم
    res.status(400).json({ 
      success: false, 
      message: 'استخدم imgbb لرفع الصور بدلاً من هذا الـ endpoint' 
    });
  } catch (error) {
    console.error('خطأ في رفع الصور:', error);
    res.status(500).json({ 
      success: false, 
      message: 'حدث خطأ أثناء رفع الصور' 
    });
  }
});

module.exports = router;

// ملاحظات للتطبيق:
// 1. أضف هذا الكود إلى ملف routes/craftsmen.js الموجود
// 2. تأكد من أن middleware auth يعمل بشكل صحيح
// 3. تأكد من أن model Craftsman يحتوي على حقل workGallery
// 4. في app.js أو server.js، تأكد من تسجيل الـ routes:
//    app.use('/api/craftsmen', craftsmenRoutes);

// مثال على هيكل Craftsman model:
/*
const craftsmanSchema = new mongoose.Schema({
  // ... باقي الحقول
  workGallery: [{
    type: String, // روابط الصور من imgbb
    default: []
  }],
  // ... باقي الحقول
});
*/

// مثال على middleware auth:
/*
const auth = (req, res, next) => {
  try {
    const token = req.header('Authorization')?.replace('Bearer ', '');
    if (!token) {
      return res.status(401).json({ message: 'لا يوجد توكن، الوصول مرفوض' });
    }
    
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    req.user = decoded;
    next();
  } catch (error) {
    res.status(401).json({ message: 'التوكن غير صالح' });
  }
};
*/
