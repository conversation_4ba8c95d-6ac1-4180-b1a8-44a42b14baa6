// نظام الحماية للأدمن
class AdminSecurity {
  constructor() {
    this.STORAGE_KEY = 'admin_security_lockout';
    this.MAX_ATTEMPTS = 2;
    this.LOCKOUT_DURATION = 10  * 1000; // ساعة واحدة بالميلي ثانية
    this.listeners = new Set();
  }

  // الحصول على بيانات القفل من localStorage
  getLockoutData() {
    try {
      const data = localStorage.getItem(this.STORAGE_KEY);
      return data ? JSON.parse(data) : null;
    } catch (error) {
      console.error('خطأ في قراءة بيانات القفل:', error);
      return null;
    }
  }

  // حفظ بيانات القفل في localStorage
  saveLockoutData(data) {
    try {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(data));
    } catch (error) {
      console.error('خطأ في حفظ بيانات القفل:', error);
    }
  }

  // مسح بيانات القفل
  clearLockoutData() {
    try {
      localStorage.removeItem(this.STORAGE_KEY);
    } catch (error) {
      console.error('خطأ في مسح بيانات القفل:', error);
    }
  }

  // تسجيل محاولة فاشلة
  recordFailedAttempt() {
    const now = Date.now();
    let lockoutData = this.getLockoutData();

    if (!lockoutData) {
      lockoutData = {
        attempts: 1,
        firstAttempt: now,
        isLocked: false,
        lockStartTime: null,
        pausedTime: 0 // الوقت المتوقف عند مغادرة الصفحة
      };
    } else {
      lockoutData.attempts += 1;
    }

    // إذا تجاوز المحاولات المسموحة
    if (lockoutData.attempts >= this.MAX_ATTEMPTS) {
      lockoutData.isLocked = true;
      lockoutData.lockStartTime = now;
      lockoutData.pausedTime = 0;
    }

    this.saveLockoutData(lockoutData);
    this.notifyListeners();
    
    return lockoutData;
  }

  // التحقق من حالة القفل
  isLocked() {
    const lockoutData = this.getLockoutData();
    
    if (!lockoutData || !lockoutData.isLocked) {
      return { locked: false, timeRemaining: 0 };
    }

    const now = Date.now();
    const elapsedTime = now - lockoutData.lockStartTime - lockoutData.pausedTime;
    const timeRemaining = this.LOCKOUT_DURATION - elapsedTime;

    if (timeRemaining <= 0) {
      // انتهت مدة القفل
      this.clearLockoutData();
      this.notifyListeners();
      return { locked: false, timeRemaining: 0 };
    }

    return { 
      locked: true, 
      timeRemaining: Math.ceil(timeRemaining / 1000), // بالثواني
      attempts: lockoutData.attempts 
    };
  }

  // إيقاف المؤقت عند مغادرة الصفحة
  pauseTimer() {
    const lockoutData = this.getLockoutData();
    if (lockoutData && lockoutData.isLocked && lockoutData.lockStartTime) {
      const now = Date.now();
      const elapsedTime = now - lockoutData.lockStartTime - lockoutData.pausedTime;
      lockoutData.pausedTime += elapsedTime;
      lockoutData.lockStartTime = now; // إعادة تعيين وقت البداية
      this.saveLockoutData(lockoutData);
    }
  }

  // استئناف المؤقت عند العودة للصفحة
  resumeTimer() {
    const lockoutData = this.getLockoutData();
    if (lockoutData && lockoutData.isLocked) {
      lockoutData.lockStartTime = Date.now();
      this.saveLockoutData(lockoutData);
      this.notifyListeners();
    }
  }

  // إضافة مستمع للتحديثات
  addListener(callback) {
    this.listeners.add(callback);
  }

  // إزالة مستمع
  removeListener(callback) {
    this.listeners.delete(callback);
  }

  // إشعار جميع المستمعين
  notifyListeners() {
    this.listeners.forEach(callback => {
      try {
        callback(this.isLocked());
      } catch (error) {
        console.error('خطأ في إشعار المستمع:', error);
      }
    });
  }

  // إعادة تعيين النظام (للاختبار أو الطوارئ)
  reset() {
    this.clearLockoutData();
    this.notifyListeners();
  }

  // تنسيق الوقت المتبقي
  formatTimeRemaining(seconds) {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    } else {
      return `${minutes}:${secs.toString().padStart(2, '0')}`;
    }
  }

  // الحصول على رسالة الحالة
  getStatusMessage(lockStatus) {
    if (!lockStatus.locked) {
      return null;
    }

    const timeFormatted = this.formatTimeRemaining(lockStatus.timeRemaining);
    return {
      title: '🔒 تم قفل تسجيل الدخول',
      message: `تم تجاوز عدد المحاولات المسموحة (${lockStatus.attempts}/${this.MAX_ATTEMPTS}). يرجى المحاولة بعد: ${timeFormatted}`,
      timeRemaining: lockStatus.timeRemaining,
      timeFormatted
    };
  }
}

// إنشاء instance واحد للاستخدام في جميع أنحاء التطبيق
const adminSecurity = new AdminSecurity();

// إضافة مستمعين لأحداث الصفحة
if (typeof window !== 'undefined') {
  // عند مغادرة الصفحة أو التبويب
  window.addEventListener('beforeunload', () => {
    adminSecurity.pauseTimer();
  });

  // عند إخفاء الصفحة (تغيير التبويب)
  document.addEventListener('visibilitychange', () => {
    if (document.hidden) {
      adminSecurity.pauseTimer();
    } else {
      adminSecurity.resumeTimer();
    }
  });

  // عند فقدان التركيز
  window.addEventListener('blur', () => {
    adminSecurity.pauseTimer();
  });

  // عند استعادة التركيز
  window.addEventListener('focus', () => {
    adminSecurity.resumeTimer();
  });
}

export default adminSecurity;
