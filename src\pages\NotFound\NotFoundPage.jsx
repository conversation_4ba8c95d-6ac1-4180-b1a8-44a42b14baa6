import React from "react";
import { <PERSON>, useNavigate } from "react-router-dom";
import { motion } from "framer-motion";
import {
  Home,
  Search,
  ArrowRight,
  AlertTriangle,
  RefreshCw,
} from "lucide-react";
import useThemeStore from "../../store/themeStore";
import Layout from "../../components/layout/Layout";

const NotFoundPage = () => {
  const darkMode = useThemeStore((state) => state.darkMode);
  const navigate = useNavigate();

  const handleGoHome = () => {
    navigate("/");
  };

  const handleGoSearch = () => {
    navigate("/search");
  };

  const handleGoBack = () => {
    window.history.back();
  };

  return (
    <Layout>
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-100 to-purple-50 flex items-center justify-center p-4">
        <div className="max-w-xl w-full">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center"
          >
            {/* رقم 404 */}
            <motion.div
              initial={{ scale: 0.5, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="mb-2"
            >
              <h1 className="text-[12rem] md:text-[16rem] font-black text-transparent bg-clip-text bg-gradient-to-r from-indigo-600 via-purple-600 to-blue-600 leading-none">
                404
              </h1>
            </motion.div>

            {/* العنوان والوصف */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              className={`mb-8 ${darkMode ? "text-gray-200" : "text-gray-800"}`}
            >
              <div className="flex items-center justify-center mb-4">
                <AlertTriangle className="w-8 h-8 text-yellow-500 ml-3" />
                <h2 className="text-xl md:text-4xl font-bold">
                  الصفحة غير موجودة
                </h2>
              </div>
              <p
                className={`text-lg md:text-xl max-w-2xl mx-auto leading-relaxed ${
                  darkMode ? "text-gray-400" : "text-gray-600"
                }`}
              >
                عذراً، يبدو أن الصفحة التي تبحث عنها قد تم نقلها أو حذفها أو أن
                الرابط غير صحيح. لا تقلق، يمكنك العودة إلى الصفحة الرئيسية أو
                البحث عن ما تريد.
              </p>

            </motion.div>

            {/* زر العودة للصفحة السابقة - في المنتصف مع تصميم جميل */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.6 }}
              className="flex justify-center mb-12"
            >
              <motion.button
                whileHover={{ scale: 1.05, y: -3 }}
                whileTap={{ scale: 0.95 }}
                onClick={handleGoBack}
                className={`group relative overflow-hidden ${
                  darkMode
                    ? "bg-gradient-to-r from-gray-800 to-gray-700 hover:from-gray-700 hover:to-gray-600 text-white border-gray-600"
                    : "bg-gradient-to-r from-white to-gray-50 hover:from-gray-50 hover:to-gray-100 text-gray-800 border-gray-300"
                } border-2 px-10 py-4 rounded-2xl font-semibold text-lg shadow-lg hover:shadow-xl transition-all duration-300 flex items-center gap-3 min-w-[320px] justify-center backdrop-blur-sm`}
              >
                <span className="absolute inset-0 bg-gradient-to-r from-indigo-500/10 to-purple-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></span>
                <RefreshCw className="w-6 h-6 relative z-10 group-hover:rotate-180 transition-transform duration-500" />
                <span className="relative z-10">العودة للصفحة السابقة</span>
                <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-indigo-500 to-purple-500 opacity-0 group-hover:opacity-5 transition-opacity duration-300"></div>
              </motion.button>
            </motion.div>

            {/* قسم المساعدة الاحترافي */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.6, delay: 0.8 }}
              className="mt-1"
            >
              <div
                className={`max-w-4xl mx-auto p-8 rounded-2xl ${
                  darkMode
                    ? "bg-gradient-to-br from-gray-800/80 to-gray-900/80 border border-gray-700/50"
                    : "bg-gradient-to-br from-white/80 to-gray-50/80 border border-gray-200/50"
                } backdrop-blur-lg shadow-2xl`}
              >
                <div className="text-center mb-8">
                  <h3
                    className={`text-2xl font-bold mb-2 ${
                      darkMode ? "text-white" : "text-gray-900"
                    }`}
                  >
                    هل تحتاج مساعدة؟
                  </h3>
                  <p
                    className={`text-lg ${
                      darkMode ? "text-gray-400" : "text-gray-600"
                    }`}
                  >
                    اختر من الخيارات التالية للعثور على ما تبحث عنه
                  </p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  {/* الصفحة الرئيسية */}
                  <motion.div
                    whileHover={{ scale: 1.05, y: -5 }}
                    transition={{ duration: 0.2 }}
                  >
                    <Link
                      to="/"
                      className={`group block p-6 rounded-xl transition-all duration-300 ${
                        darkMode
                          ? "bg-gradient-to-br from-indigo-900/50 to-purple-900/50 hover:from-indigo-800/60 hover:to-purple-800/60 border border-indigo-700/30"
                          : "bg-gradient-to-br from-indigo-50 to-purple-50 hover:from-indigo-100 hover:to-purple-100 border border-indigo-200/50"
                      } shadow-lg hover:shadow-xl`}
                    >
                      <div className="flex items-center justify-center w-12 h-12 rounded-lg bg-gradient-to-r from-indigo-500 to-purple-600 text-white mb-4 mx-auto group-hover:scale-110 transition-transform duration-300">
                        <Home className="w-6 h-6" />
                      </div>
                      <h4
                        className={`text-lg font-semibold text-center mb-2 ${
                          darkMode ? "text-white" : "text-gray-900"
                        }`}
                      >
                        الصفحة الرئيسية
                      </h4>
                      <p
                        className={`text-sm text-center ${
                          darkMode ? "text-gray-400" : "text-gray-600"
                        }`}
                      >
                        تصفح الخدمات المتاحة واكتشف أفضل الحرفيين في منطقتك
                      </p>
                    </Link>
                  </motion.div>

                  {/* البحث */}
                  <motion.div
                    whileHover={{ scale: 1.05, y: -5 }}
                    transition={{ duration: 0.2 }}
                  >
                    <Link
                      to="/search"
                      className={`group block p-6 rounded-xl transition-all duration-300 ${
                        darkMode
                          ? "bg-gradient-to-br from-blue-900/50 to-cyan-900/50 hover:from-blue-800/60 hover:to-cyan-800/60 border border-blue-700/30"
                          : "bg-gradient-to-br from-blue-50 to-cyan-50 hover:from-blue-100 hover:to-cyan-100 border border-blue-200/50"
                      } shadow-lg hover:shadow-xl`}
                    >
                      <div className="flex items-center justify-center w-12 h-12 rounded-lg bg-gradient-to-r from-blue-500 to-cyan-600 text-white mb-4 mx-auto group-hover:scale-110 transition-transform duration-300">
                        <Search className="w-6 h-6" />
                      </div>
                      <h4
                        className={`text-lg font-semibold text-center mb-2 ${
                          darkMode ? "text-white" : "text-gray-900"
                        }`}
                      >
                        البحث المتقدم
                      </h4>
                      <p
                        className={`text-sm text-center ${
                          darkMode ? "text-gray-400" : "text-gray-600"
                        }`}
                      >
                        ابحث عن حرفيين متخصصين حسب المهنة والموقع والتقييم
                      </p>
                    </Link>
                  </motion.div>

                  {/* تسجيل الدخول */}
                  <motion.div
                    whileHover={{ scale: 1.05, y: -5 }}
                    transition={{ duration: 0.2 }}
                  >
                    <Link
                      to="/login"
                      className={`group block p-6 rounded-xl transition-all duration-300 ${
                        darkMode
                          ? "bg-gradient-to-br from-emerald-900/50 to-teal-900/50 hover:from-emerald-800/60 hover:to-teal-800/60 border border-emerald-700/30"
                          : "bg-gradient-to-br from-emerald-50 to-teal-50 hover:from-emerald-100 hover:to-teal-100 border border-emerald-200/50"
                      } shadow-lg hover:shadow-xl`}
                    >
                      <div className="flex items-center justify-center w-12 h-12 rounded-lg bg-gradient-to-r from-emerald-500 to-teal-600 text-white mb-4 mx-auto group-hover:scale-110 transition-transform duration-300">
                        <AlertTriangle className="w-6 h-6" />
                      </div>
                      <h4
                        className={`text-lg font-semibold text-center mb-2 ${
                          darkMode ? "text-white" : "text-gray-900"
                        }`}
                      >
                        تسجيل الدخول
                      </h4>
                      <p
                        className={`text-sm text-center ${
                          darkMode ? "text-gray-400" : "text-gray-600"
                        }`}
                      >
                        الوصول إلى حسابك لإدارة الطلبات والحجوزات
                      </p>
                    </Link>
                  </motion.div>
                </div>

                {/* خط فاصل */}
                <div
                  className={`my-8 h-px ${
                    darkMode
                      ? "bg-gradient-to-r from-transparent via-gray-600 to-transparent"
                      : "bg-gradient-to-r from-transparent via-gray-300 to-transparent"
                  }`}
                ></div>

                {/* معلومات إضافية */}
                <div className="text-center">
                  <p
                    className={`text-sm ${
                      darkMode ? "text-gray-400" : "text-gray-600"
                    }`}
                  >
                    إذا كنت تواجه مشكلة تقنية، يرجى المحاولة مرة أخرى أو
                    <button
                      onClick={() => window.location.reload()}
                      className={`mx-1 underline hover:no-underline ${
                        darkMode
                          ? "text-indigo-400 hover:text-indigo-300"
                          : "text-indigo-600 hover:text-indigo-700"
                      }`}
                    >
                      إعادة تحميل الصفحة
                    </button>
                  </p>
                </div>
              </div>
            </motion.div>
          </motion.div>
        </div>
      </div>
    </Layout>
  );
};

export default NotFoundPage;
