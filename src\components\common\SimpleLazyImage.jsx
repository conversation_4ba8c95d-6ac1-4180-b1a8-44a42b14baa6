import React, { useState, useEffect, useRef } from "react";
import { SERVER_URL } from "../../services/config";

const SimpleLazyImage = ({ src, alt, className, placeholderClassName }) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isInView, setIsInView] = useState(false);
  const [imgSrc, setImgSrc] = useState("");
  const [hasError, setHasError] = useState(false);
  const imgRef = useRef();

  // استخدام Intersection Observer API لتحديد متى تصبح الصورة مرئية
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        // إذا كانت الصورة مرئية، نبدأ بتحميلها
        if (entries[0].isIntersecting) {
          setIsInView(true);
          observer.disconnect();
        }
      },
      { threshold: 0.1 } // تحميل الصورة عندما يكون 10% منها مرئيًا على الأقل
    );

    if (imgRef.current) {
      observer.observe(imgRef.current);
    }

    return () => {
      if (imgRef.current) {
        observer.disconnect();
      }
    };
  }, []);

  // تحديث مصدر الصورة عند تغيير الـ prop
  useEffect(() => {
    // إعادة تعيين حالة الخطأ عند تغيير مصدر الصورة
    setHasError(false);

    // التحقق من أن src هو نص صالح
    if (
      !src ||
      typeof src !== "string" ||
      src === "undefined" ||
      src === "null"
    ) {
      // إذا كان المصدر فارغًا أو غير صالح، استخدم الصورة الافتراضية
      console.log(
        "SimpleLazyImage: Empty or invalid source, using default avatar"
      );
      setImgSrc("/img/default-avatar-2-modified.svg");
      setIsLoaded(true);
      setHasError(true); // تعيين حالة الخطأ لعرض الصورة الافتراضية مباشرة
    } else if (
      src === "/img/user-avatar.svg" ||
      src === "/img/user-avatar.svg?url" ||
      src === "/img/default-avatar.svg" ||
      src === "/img/default-avatar-3-modified.svg" ||
      src === "/img/default-avatar-2-modified.svg"
    ) {
      // إذا كان المصدر هو الصورة الافتراضية، استخدمها مباشرة
      console.log("SimpleLazyImage: Using default avatar directly");
      setImgSrc("/img/default-avatar-2-modified.svg");
      setIsLoaded(true);
      setHasError(true); // تعيين حالة الخطأ لعرض الصورة الافتراضية مباشرة
    } else if (typeof src === "string" && src.startsWith("data:")) {
      // إذا كان المصدر بيانات Base64، استخدمه كما هو
      setImgSrc(src);
    } else if (typeof src === "string" && src.startsWith("http")) {
      // إذا كان المصدر URL كاملاً، استخدمه كما هو
      setImgSrc(src);
    } else {
      // إذا كان مسارًا نسبيًا، أضف عنوان الخادم
      const fullPath = `${SERVER_URL}${
        typeof src === "string" && src.startsWith("/") ? "" : "/"
      }${src}`;
      setImgSrc(fullPath);
    }
  }, [src]);

  // معالج تحميل الصورة
  const handleImageLoaded = () => {
    setIsLoaded(true);
    setHasError(false);
  };

  // معالج خطأ تحميل الصورة - استخدام الصورة الافتراضية مباشرة
  const handleImageError = () => {
    console.log("SimpleLazyImage: Error loading image:", imgSrc);

    // إذا كان المسار الحالي هو الصورة الافتراضية ولا تزال هناك أخطاء
    if (
      (typeof imgSrc === "string" && imgSrc.includes("default-avatar")) ||
      (typeof imgSrc === "string" && imgSrc.includes("user-avatar.svg"))
    ) {
      console.log(
        "SimpleLazyImage: Error loading default avatar, stopping retry attempts"
      );
      // إظهار الصورة على أي حال حتى لو كان هناك خطأ في تحميلها
      setIsLoaded(true);
      return;
    }

    // استخدام الصورة الافتراضية مباشرة
    console.log("SimpleLazyImage: Switching to default avatar immediately");
    setImgSrc("/img/default-avatar-2-modified.svg");
    setIsLoaded(true);
    setHasError(true);
  };

  return (
    <div ref={imgRef} className={className || ""}>
      {isInView ? (
        <>
          {/* إذا كان هناك خطأ، عرض الصورة الافتراضية مباشرة */}
          {hasError ? (
            <img
              src="/img/default-avatar-2-modified.svg"
              alt={alt || "صورة المستخدم الافتراضية"}
              className={`${className || ""} block`}
              onLoad={() => setIsLoaded(true)}
            />
          ) : (
            <>
              {/* الصورة الرئيسية */}
              <img
                src={imgSrc}
                alt={alt || "صورة"}
                className={`${className || ""} ${
                  isLoaded ? "block" : "hidden"
                }`}
                onLoad={handleImageLoaded}
                onError={handleImageError}
                loading="eager"
                fetchPriority="high"
              />

              {/* مؤشر التحميل يظهر حتى تكتمل الصورة */}
              {!isLoaded && (
                <div
                  className={
                    placeholderClassName ||
                    `${className || ""} bg-gray-200 animate-pulse`
                  }
                >
                  {/* مؤشر تحميل دائري */}
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="w-8 h-8 border-4 border-indigo-500 border-t-transparent rounded-full animate-spin"></div>
                  </div>
                </div>
              )}
            </>
          )}
        </>
      ) : (
        // مكان حجز قبل أن تصبح الصورة مرئية
        <div
          className={placeholderClassName || `${className || ""} bg-gray-200`}
        ></div>
      )}
    </div>
  );
};

export default SimpleLazyImage;
