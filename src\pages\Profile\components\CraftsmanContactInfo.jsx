import React, { useState } from "react";
import {
  Phone,
  Mail,
  MapPin,
  ExternalLink,
  Lock,
  Copy,
  Check,
  Share2,
  Smartphone,
  Calendar,
  Award,
  MessageCircle,
  User,
  EyeOff,
} from "lucide-react";
import { Link } from "react-router-dom";
import Card from "../../../components/common/Card";
import useUserStore from "../../../store/userStore";
import { motion } from "framer-motion";
import { getRelatedSpecializations } from "../../../data/professionsData";

// دالة لتحويل الوقت من نظام 24 ساعة إلى نظام 12 ساعة
const formatTime12Hour = (timeString) => {
  if (!timeString) return "-";

  const [hours, minutes] = timeString.split(":");
  const hour = parseInt(hours, 10);
  const ampm = hour >= 12 ? "مساءً" : "صباحاً";
  const hour12 = hour % 12 || 12;

  return `${hour12}:${minutes} ${ampm}`;
};

// تم إزالة دالة التصحيح

const CraftsmanContactInfo = ({ craftsman, darkMode }) => {
  // التحقق من حالة تسجيل الدخول باستخدام المتجر
  const isAuthenticated = useUserStore((state) => state.isAuthenticated);

  // تم إزالة طباعة بيانات ساعات العمل للتصحيح

  // حالات النسخ للعناصر المختلفة
  const [copied, setCopied] = useState({
    phone: false,
    email: false,
    address: false,
  });

  // دالة لنسخ النص إلى الحافظة
  const copyToClipboard = (text, field) => {
    if (!text) return;

    navigator.clipboard.writeText(text).then(() => {
      setCopied({ ...copied, [field]: true });

      // إعادة تعيين حالة النسخ بعد ثانيتين
      setTimeout(() => {
        setCopied({ ...copied, [field]: false });
      }, 2000);
    });
  };

  // دالة لمشاركة معلومات الحرفي
  const shareProfile = () => {
    if (navigator.share) {
      navigator
        .share({
          title: `معلومات الحرفي ${craftsman.name}`,
          text: `تفاصيل الاتصال بالحرفي ${craftsman.name}${
            craftsman.phone ? `\nرقم الهاتف: ${craftsman.phone}` : ""
          }${craftsman.email ? `\nالبريد الإلكتروني: ${craftsman.email}` : ""}`,
          url: window.location.href,
        })
        .catch((error) => console.log("خطأ في المشاركة:", error));
    } else {
      // نسخ الرابط إلى الحافظة إذا كانت المشاركة غير مدعومة
      copyToClipboard(window.location.href, "link");
      alert("تم نسخ رابط الصفحة إلى الحافظة");
    }
  };

  // تأثيرات الحركة
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: "spring",
        stiffness: 100,
        damping: 12,
      },
    },
  };

  // التحقق من إعدادات إخفاء معلومات الاتصال
  if (craftsman.hideContactInfo) {
    return (
      <Card
        className={`mt-6 overflow-hidden rounded-xl shadow-lg ${
          darkMode
            ? "bg-gray-800 text-gray-200 border border-gray-700"
            : "bg-gradient-to-br from-red-50/80 to-orange-50 border border-red-100"
        } transition-colors duration-300`}
      >
        <div className="p-6 text-center">
          <div
            className={`inline-flex items-center justify-center w-16 h-16 rounded-full mb-4 ${
              darkMode ? "bg-red-900/50" : "bg-red-100"
            }`}
          >
            <EyeOff
              className={`w-8 h-8 ${
                darkMode ? "text-red-400" : "text-red-600"
              }`}
            />
          </div>
          <h3
            className={`text-lg font-semibold mb-2 ${
              darkMode ? "text-red-400" : "text-red-600"
            }`}
          >
            معلومات الاتصال غير متاحة
          </h3>
          <p
            className={`text-sm ${
              darkMode ? "text-gray-400" : "text-gray-600"
            }`}
          >
            قام الحرفي بإخفاء معلومات الاتصال الخاصة به مؤقتاً
          </p>
        </div>
      </Card>
    );
  }

  return (
    <motion.div initial="hidden" animate="visible" variants={containerVariants}>
      <Card
        className={`mt-4 p-6 rounded-xl ${
          darkMode
            ? "bg-gray-800 text-gray-200 border border-gray-700"
            : "bg-gradient-to-br from-blue-50/80 to-indigo-50 border border-indigo-100"
        } shadow-lg transition-all duration-300 hover:shadow-xl relative overflow-hidden`}
      >
        {/* زخرفة خلفية */}
        <div className="absolute top-0 right-0 w-40 h-40 bg-indigo-500/5 rounded-full -mr-20 -mt-20 z-0"></div>
        <div className="absolute bottom-0 left-0 w-60 h-60 bg-indigo-500/5 rounded-full -ml-40 -mb-40 z-0"></div>

        {/* هيدر القسم مع تأثيرات */}
        <div className="flex items-center justify-between mb-6 relative z-10">
          <div className="flex items-center">
            <motion.div
              whileHover={{ scale: 1.1, rotate: 5 }}
              whileTap={{ scale: 0.95 }}
              className={`p-3 rounded-full mr-3 ${
                darkMode ? "bg-indigo-900/50" : "bg-indigo-100"
              } transform transition-all duration-300`}
            ></motion.div>
            <div className="relative">
              <h2
                className={`text-xl font-bold  ${
                  darkMode ? "text-indigo-300" : "text-indigo-800"
                } relative inline-flex items-center transition-colors duration-300`}
              >
                <div
                  className={`p-1.5 rounded-full  ${
                    darkMode ? "bg-indigo-900/50" : "bg-indigo-100"
                  } transition-all duration-300`}
                >
                  <User
                    size={18}
                    className={`${
                      darkMode ? "text-indigo-400" : "text-indigo-500"
                    } transition-colors duration-300`}
                  />
                </div>
                <span className="relative z-10 mr-1">بيانات الحرفي</span>
                <span
                  className={`absolute bottom-0 left-0 right-0 h-2 ${
                    darkMode ? "bg-indigo-500" : "bg-indigo-300"
                  } opacity-40 transform -rotate-1 z-0 rounded-full`}
                ></span>
              </h2>
              <p
                className={`text-sm mt-1  ${
                  darkMode ? "text-gray-400" : "text-gray-500"
                }`}
              >
                معلومات التواصل وأوقات العمل
              </p>
            </div>
          </div>

          {/* زر المشاركة */}
          {isAuthenticated && (
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={shareProfile}
              className={`p-2 rounded-full ${
                darkMode
                  ? "bg-indigo-900/50 hover:bg-indigo-800 text-indigo-300"
                  : "bg-indigo-100 hover:bg-indigo-200 text-indigo-700"
              } transition-all duration-300`}
              title="مشاركة معلومات الاتصال"
            >
              <Share2 size={20} />
            </motion.button>
          )}
        </div>

        {/* زخرفة جانبية */}
        <div className="absolute top-0 left-0 w-20 h-1 bg-gradient-to-r from-transparent via-indigo-500 to-transparent opacity-30"></div>

        <div className="grid grid-cols-1 gap-4 relative z-10">
          {/* رقم الهاتف */}
          <motion.div
            variants={itemVariants}
            className={`p-6 rounded-xl ${
              darkMode
                ? "bg-gradient-to-r from-gray-800 to-gray-700 border border-indigo-900/30 shadow-lg"
                : "bg-gradient-to-r from-blue-50 to-indigo-100 border border-indigo-200 shadow-md"
            } transition-all duration-300 hover:shadow-xl relative overflow-hidden group`}
          >
            {/* تأثير الخلفية عند التحويم */}
            <div className="absolute inset-0 bg-indigo-500/0 group-hover:bg-indigo-500/5 transition-colors duration-300"></div>

            {/* زخرفة خلفية */}
            <div className="absolute top-0 right-0 w-32 h-32 bg-indigo-500/5 rounded-full -mr-10 -mt-10 z-0"></div>
            <div className="absolute bottom-0 left-0 w-24 h-24 bg-indigo-500/5 rounded-full -ml-10 -mb-10 z-0"></div>

            <div className="relative z-10">
              {/* العنوان وشارة الاتصال السريع */}
              <div className="flex items-center justify-between flex-wrap gap-2 mb-4">
                <div className="flex items-center">
                  <div
                    className={`p-3 rounded-full mr-3 ${
                      darkMode ? "bg-indigo-900/70" : "bg-indigo-200"
                    } transition-all duration-300 group-hover:scale-110 shadow-md`}
                  >
                    <Smartphone
                      size={22}
                      className={`${
                        darkMode ? "text-indigo-300" : "text-indigo-600"
                      } transition-colors duration-300`}
                    />
                  </div>
                  <div
                    className={`text-base font-bold mr-1 ${
                      darkMode ? "text-indigo-300" : "text-indigo-700"
                    }`}
                  >
                    رقم الهاتف المحمول
                  </div>
                </div>

                {/* شارة الاتصال السريع */}
                {isAuthenticated && craftsman.phone && (
                  <div
                    className={`text-xs px-3 py-1 rounded-full ${
                      darkMode
                        ? "bg-green-900/50 text-green-300 border border-green-800/50"
                        : "bg-green-100 text-green-700 border border-green-300"
                    } shadow-sm`}
                  >
                    اتصال مباشر
                  </div>
                )}
              </div>

              {isAuthenticated ? (
                <div className="flex flex-col gap-3">
                  {/* رقم الهاتف - يكون غير قابل للنسخ عندما يكون الحرفي غير متاح */}
                  <div
                    className={`font-bold text-xl text-center py-2 px-4 rounded-lg ${
                      !craftsman.available
                        ? darkMode
                          ? "bg-gray-900/50 text-gray-500 border border-gray-700/30 select-none cursor-not-allowed"
                          : "bg-white/80 text-gray-400 border border-gray-300 select-none cursor-not-allowed"
                        : darkMode
                        ? "bg-gray-900/50 text-indigo-300 border border-indigo-900/30"
                        : "bg-white/80 text-indigo-800 border border-indigo-200"
                    } break-all shadow-inner`}
                    title={!craftsman.available ? "الحرفي غير متاح حاليًا" : ""}
                    onClick={(e) => !craftsman.available && e.preventDefault()}
                    style={{
                      userSelect: !craftsman.available ? "none" : "auto",
                    }}
                  >
                    {craftsman.available
                      ? craftsman.phone || "رقم الهاتف غير متوفر"
                      : craftsman.phone
                      ? craftsman.phone.replace(/\d/g, (digit, index) =>
                          index < 4 || index > craftsman.phone.length - 3
                            ? digit
                            : "*"
                        )
                      : "رقم الهاتف غير متوفر"}
                  </div>

                  {/* أزرار الإجراءات */}
                  {craftsman.phone && (
                    <div className="flex items-center justify-center gap-3 mt-3">
                      {/* زر الاتصال */}
                      <motion.a
                        whileHover={craftsman.available ? { scale: 1.05 } : {}}
                        whileTap={craftsman.available ? { scale: 0.95 } : {}}
                        href={
                          craftsman.available
                            ? `tel:${craftsman.phone}`
                            : undefined
                        }
                        onClick={(e) =>
                          !craftsman.available && e.preventDefault()
                        }
                        className={`p-3 rounded-full ${
                          !craftsman.available
                            ? darkMode
                              ? "bg-gray-700 text-gray-500 border border-gray-600 cursor-not-allowed opacity-60"
                              : "bg-gray-200 text-gray-400 border border-gray-300 cursor-not-allowed opacity-60"
                            : darkMode
                            ? "bg-green-900/50 hover:bg-green-800/70 text-green-300 border border-green-800/30"
                            : "bg-green-100 hover:bg-green-200 text-green-700 border border-green-300"
                        } transition-all duration-300 shadow-md`}
                        title={
                          craftsman.available
                            ? "اتصال"
                            : "الحرفي غير متاح حاليًا"
                        }
                      >
                        <Phone size={20} />
                      </motion.a>

                      {/* زر النسخ */}
                      <motion.button
                        whileHover={craftsman.available ? { scale: 1.05 } : {}}
                        whileTap={craftsman.available ? { scale: 0.95 } : {}}
                        onClick={() =>
                          craftsman.available &&
                          copyToClipboard(craftsman.phone, "phone")
                        }
                        className={`p-3 rounded-full ${
                          !craftsman.available
                            ? darkMode
                              ? "bg-gray-700 text-gray-500 border border-gray-600 cursor-not-allowed opacity-60"
                              : "bg-gray-200 text-gray-400 border border-gray-300 cursor-not-allowed opacity-60"
                            : darkMode
                            ? "bg-indigo-900/50 hover:bg-indigo-800/70 text-indigo-300 border border-indigo-800/30"
                            : "bg-indigo-100 hover:bg-indigo-200 text-indigo-700 border border-indigo-200"
                        } transition-all duration-300 shadow-md`}
                        title={
                          craftsman.available
                            ? "نسخ الرقم"
                            : "الحرفي غير متاح حاليًا"
                        }
                      >
                        {copied.phone ? (
                          <Check size={20} />
                        ) : (
                          <Copy size={20} />
                        )}
                      </motion.button>

                      {/* زر الرسائل - متاح دائمًا */}
                      <motion.a
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        href={`sms:${craftsman.phone}`}
                        className={`p-3 rounded-full ${
                          darkMode
                            ? "bg-blue-900/50 hover:bg-blue-800/70 text-blue-300 border border-blue-800/30"
                            : "bg-blue-100 hover:bg-blue-200 text-blue-700 border border-blue-300"
                        } transition-all duration-300 shadow-md`}
                        title="إرسال رسالة"
                      >
                        <MessageCircle size={20} />
                      </motion.a>
                    </div>
                  )}
                </div>
              ) : (
                <div className="flex items-center justify-center mt-2 p-3 rounded-lg bg-yellow-500/10 border border-yellow-500/20">
                  <Lock
                    size={18}
                    className={`ml-2 ${
                      darkMode ? "text-yellow-400" : "text-yellow-600"
                    }`}
                  />
                  <span
                    className={`${
                      darkMode ? "text-yellow-300" : "text-yellow-600"
                    } text-sm font-medium`}
                  >
                    <Link
                      to="/login"
                      className="underline hover:text-indigo-500 transition-colors duration-200 font-bold"
                    >
                      سجل دخول
                    </Link>{" "}
                    لرؤية رقم الهاتف
                  </span>
                </div>
              )}
            </div>
          </motion.div>

          {/* البريد الإلكتروني */}
          {craftsman.email && (
            <motion.div
              variants={itemVariants}
              className={`p-6 rounded-xl ${
                darkMode
                  ? "bg-gradient-to-r from-gray-800 to-gray-700 border border-indigo-900/30 shadow-lg"
                  : "bg-gradient-to-r from-blue-50 to-indigo-100 border border-indigo-200 shadow-md"
              } transition-all duration-300 hover:shadow-xl relative overflow-hidden group`}
            >
              {/* تأثير الخلفية عند التحويم */}
              <div className="absolute inset-0 bg-indigo-500/0 group-hover:bg-indigo-500/5 transition-colors duration-300"></div>

              {/* زخرفة خلفية */}
              <div className="absolute top-0 right-0 w-32 h-32 bg-indigo-500/5 rounded-full -mr-10 -mt-10 z-0"></div>
              <div className="absolute bottom-0 left-0 w-24 h-24 bg-indigo-500/5 rounded-full -ml-10 -mb-10 z-0"></div>

              <div className="relative z-10">
                {/* العنوان وشارة التواصل */}
                <div className="flex items-center justify-between flex-wrap gap-2 mb-4">
                  <div className="flex items-center">
                    <div
                      className={`p-3 rounded-full mr-3 ${
                        darkMode ? "bg-indigo-900/70" : "bg-indigo-200"
                      } transition-all duration-300 group-hover:scale-110 shadow-md`}
                    >
                      <Mail
                        size={22}
                        className={`${
                          darkMode ? "text-indigo-300" : "text-indigo-600"
                        } transition-colors duration-300`}
                      />
                    </div>
                    <div
                      className={`text-base font-bold mr-1 ${
                        darkMode ? "text-indigo-300" : "text-indigo-700"
                      }`}
                    >
                      البريد الإلكتروني
                    </div>
                  </div>

                  {/* شارة التواصل */}
                  {isAuthenticated && (
                    <div
                      className={`text-xs px-3 py-1 rounded-full ${
                        darkMode
                          ? "bg-blue-900/50 text-blue-300 border border-blue-800/50"
                          : "bg-blue-100 text-blue-700 border border-blue-300"
                      } shadow-sm`}
                    >
                      تواصل إلكتروني
                    </div>
                  )}
                </div>

                {isAuthenticated ? (
                  <div className="flex flex-col gap-3">
                    {/* البريد الإلكتروني */}
                    <div
                      className={`font-bold text-xl text-center py-2 px-4 rounded-lg ${
                        darkMode
                          ? "bg-gray-900/50 text-indigo-300 border border-indigo-900/30"
                          : "bg-white/80 text-indigo-800 border border-indigo-200"
                      } break-all shadow-inner`}
                    >
                      {craftsman.email}
                    </div>

                    {/* أزرار الإجراءات */}
                    <div className="flex items-center justify-center gap-3 mt-3">
                      {/* زر إرسال بريد */}
                      <motion.a
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        href={`mailto:${craftsman.email}`}
                        className={`p-3 rounded-full ${
                          darkMode
                            ? "bg-blue-900/50 hover:bg-blue-800/70 text-blue-300 border border-blue-800/30"
                            : "bg-blue-100 hover:bg-blue-200 text-blue-700 border border-blue-300"
                        } transition-all duration-300 shadow-md`}
                        title="إرسال بريد إلكتروني"
                      >
                        <Mail size={20} />
                      </motion.a>

                      {/* زر النسخ */}
                      <motion.button
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        onClick={() =>
                          copyToClipboard(craftsman.email, "email")
                        }
                        className={`p-3 rounded-full ${
                          darkMode
                            ? "bg-indigo-900/50 hover:bg-indigo-800/70 text-indigo-300 border border-indigo-800/30"
                            : "bg-indigo-100 hover:bg-indigo-200 text-indigo-700 border border-indigo-200"
                        } transition-all duration-300 shadow-md`}
                        title="نسخ البريد الإلكتروني"
                      >
                        {copied.email ? (
                          <Check size={20} />
                        ) : (
                          <Copy size={20} />
                        )}
                      </motion.button>
                    </div>
                  </div>
                ) : (
                  <div className="flex items-center justify-center mt-2 p-3 rounded-lg bg-yellow-500/10 border border-yellow-500/20">
                    <Lock
                      size={18}
                      className={`ml-2 ${
                        darkMode ? "text-yellow-400" : "text-yellow-600"
                      }`}
                    />
                    <span
                      className={`${
                        darkMode ? "text-yellow-300" : "text-yellow-600"
                      } text-sm font-medium`}
                    >
                      <Link
                        to="/login"
                        className="underline hover:text-indigo-500 transition-colors duration-200 font-bold"
                      >
                        سجل دخول
                      </Link>{" "}
                      لرؤية البريد الإلكتروني
                    </span>
                  </div>
                )}
              </div>
            </motion.div>
          )}

          {/* العنوان */}
          {craftsman.address && (
            <motion.div
              variants={itemVariants}
              className={`p-4 rounded-xl ${
                darkMode
                  ? "bg-gray-700/50 border border-gray-600"
                  : "bg-gradient-to-r from-blue-100/80 to-indigo-100/80 border border-indigo-100"
              } transition-all duration-300 hover:shadow-md relative overflow-hidden group`}
            >
              {/* تأثير الخلفية عند التحويم */}
              <div className="absolute inset-0 bg-indigo-500/0 group-hover:bg-indigo-500/5 transition-colors duration-300"></div>

              <div className="relative z-10">
                {/* العنوان وشارة الموقع */}
                <div className="flex items-center justify-between flex-wrap gap-2 mb-2">
                  <div className="flex items-center">
                    <div
                      className={`p-2 rounded-full mr-2 ${
                        darkMode ? "bg-indigo-900/50" : "bg-indigo-100"
                      } transition-all duration-300 group-hover:scale-110`}
                    >
                      <MapPin
                        size={18}
                        className={`${
                          darkMode ? "text-indigo-400" : "text-indigo-500"
                        } transition-colors duration-300`}
                      />
                    </div>
                    <div
                      className={`text-sm font-medium mr-1 ${
                        darkMode ? "text-indigo-300" : "text-indigo-600"
                      }`}
                    >
                      العنوان
                    </div>
                  </div>

                  {/* شارة الموقع */}
                  <div
                    className={`text-xs px-2 py-0.5 rounded-full ${
                      darkMode
                        ? "bg-purple-900/30 text-purple-400"
                        : "bg-purple-100 text-purple-700"
                    }`}
                  >
                    الموقع
                  </div>
                </div>

                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
                  {/* العنوان */}
                  <div
                    className={`font-bold text-lg ${
                      darkMode ? "text-gray-200" : "text-indigo-800"
                    } break-all`}
                  >
                    {craftsman.address}
                  </div>

                  {/* أزرار الإجراءات */}
                  {isAuthenticated && (
                    <div className="flex items-center gap-2 mt-2 sm:mt-0">
                      {/* زر الخريطة */}
                      <motion.a
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        href={`https://maps.google.com/?q=${encodeURIComponent(
                          craftsman.address
                        )}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className={`p-2 rounded-full ${
                          darkMode
                            ? "bg-purple-900/30 hover:bg-purple-800/50 text-purple-400"
                            : "bg-purple-100 hover:bg-purple-200 text-purple-700"
                        } transition-all duration-300`}
                        title="عرض على الخريطة"
                      >
                        <MapPin size={18} />
                      </motion.a>

                      {/* زر النسخ */}
                      <motion.button
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        onClick={() =>
                          copyToClipboard(craftsman.address, "address")
                        }
                        className={`p-2 rounded-full ${
                          darkMode
                            ? "bg-indigo-900/30 hover:bg-indigo-800/50 text-indigo-400"
                            : "bg-indigo-100 hover:bg-indigo-200 text-indigo-700"
                        } transition-all duration-300`}
                        title="نسخ العنوان"
                      >
                        {copied.address ? (
                          <Check size={18} />
                        ) : (
                          <Copy size={18} />
                        )}
                      </motion.button>
                    </div>
                  )}
                </div>
              </div>
            </motion.div>
          )}

          {/* ساعات العمل */}
          <motion.div
            variants={itemVariants}
            className={`p-4 rounded-xl ${
              darkMode
                ? "bg-gray-700/50 border border-gray-600"
                : "bg-gradient-to-r from-blue-100/80 to-indigo-100/80 border border-indigo-100"
            } transition-all duration-300 hover:shadow-md relative overflow-hidden group`}
          >
            {/* تأثير الخلفية عند التحويم */}
            <div className="absolute inset-0 bg-indigo-500/0 group-hover:bg-indigo-500/5 transition-colors duration-300"></div>

            <div className="relative z-10">
              {/* العنوان وشارة الجدول */}
              <div className="flex items-center justify-between flex-wrap gap-2 mb-2">
                <div className="flex items-center">
                  <div
                    className={`p-2 rounded-full mr-2 ${
                      darkMode ? "bg-indigo-900/50" : "bg-indigo-100"
                    } transition-all duration-300 group-hover:scale-110`}
                  >
                    <Calendar
                      size={18}
                      className={`${
                        darkMode ? "text-indigo-400" : "text-indigo-500"
                      } transition-colors duration-300`}
                    />
                  </div>
                  <div
                    className={`text-sm font-medium mr-1 ${
                      darkMode ? "text-indigo-300" : "text-indigo-600"
                    }`}
                  >
                    جدول أوقات العمل
                  </div>
                </div>

                {/* شارة الجدول */}
                <div
                  className={`text-xs px-2 py-0.5 rounded-full ${
                    darkMode
                      ? "bg-amber-900/30 text-amber-400 border border-amber-800/50"
                      : "bg-amber-100 text-amber-700 border border-amber-200"
                  }`}
                >
                  مواعيد الدوام
                </div>
              </div>

              <div
                className={`mt-2 ${
                  darkMode ? "text-gray-200" : "text-indigo-800"
                }`}
              >
                {/* التحقق من وجود workingHoursArray */}
                {craftsman.workingHoursArray &&
                Array.isArray(craftsman.workingHoursArray) &&
                craftsman.workingHoursArray.length > 0 ? (
                  <div className="space-y-1 mt-2">
                    {/* عرض أيام الأسبوع مع ساعات العمل */}
                    {[
                      ["saturday", "السبت"],
                      ["sunday", "الأحد"],
                      ["monday", "الإثنين"],
                      ["tuesday", "الثلاثاء"],
                      ["wednesday", "الأربعاء"],
                      ["thursday", "الخميس"],
                      ["friday", "الجمعة"],
                    ]
                      .map(([dayKey, arabicDay]) => {
                        // البحث عن اليوم في workingHoursArray
                        const hours = craftsman.workingHoursArray.find(
                          (item) => item.day === dayKey
                        );

                        // عرض فقط الأيام التي تم اختيارها كأيام عمل
                        if (!hours) {
                          return null; // لا تعرض هذا اليوم إذا لم يكن موجود
                        }

                        return (
                          <div
                            key={dayKey}
                            className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-1 py-1 border-b border-gray-200 dark:border-gray-700 last:border-0"
                          >
                            <span
                              className={`text-sm font-medium ${
                                darkMode ? "text-gray-300" : "text-gray-700"
                              }`}
                            >
                              {arabicDay}:
                            </span>

                            <span
                              className={`text-sm ${
                                darkMode ? "text-indigo-300" : "text-indigo-600"
                              }`}
                            >
                              {/* عرض حالة اليوم وساعات العمل */}
                              {hours.isWorking
                                ? hours.start || hours.from
                                  ? `${formatTime12Hour(
                                      hours.start || hours.from
                                    )} - ${formatTime12Hour(
                                      hours.end || hours.to
                                    )}`
                                  : "ساعات غير محددة"
                                : "يوم عطلة"}
                            </span>
                          </div>
                        );
                      })
                      .filter((day) => day !== null)}
                  </div>
                ) : (
                  <div
                    className={`text-sm ${
                      darkMode ? "text-gray-400" : "text-gray-600"
                    }`}
                  >
                    لم يتم تحديد أيام أو ساعات العمل
                  </div>
                )}
              </div>

              {/* مؤشر الحالة - تصميم محسن */}
              {craftsman.available !== undefined && (
                <div className="mt-4 flex justify-center">
                  <div
                    className={`flex items-center justify-center px-5 py-2.5 rounded-xl shadow-lg transition-all duration-300 transform hover:scale-105 ${
                      craftsman.available
                        ? darkMode
                          ? "bg-gradient-to-r from-green-800 via-green-700 to-green-800 border border-green-600"
                          : "bg-gradient-to-r from-green-500 via-green-400 to-green-500 border border-green-300"
                        : darkMode
                        ? "bg-gradient-to-r from-red-800 via-red-700 to-red-800 border border-red-600"
                        : "bg-gradient-to-r from-red-500 via-red-400 to-red-500 border border-red-300"
                    }`}
                    style={{
                      minWidth: "140px",
                      boxShadow: craftsman.available
                        ? darkMode
                          ? "0 4px 12px rgba(74, 222, 128, 0.3)"
                          : "0 4px 12px rgba(74, 222, 128, 0.4)"
                        : darkMode
                        ? "0 4px 12px rgba(248, 113, 113, 0.3)"
                        : "0 4px 12px rgba(248, 113, 113, 0.4)",
                    }}
                  >
                    <span
                      className={`font-bold text-base ${
                        craftsman.available
                          ? darkMode
                            ? "text-green-200"
                            : "text-white"
                          : darkMode
                          ? "text-red-200"
                          : "text-white"
                      }`}
                    >
                      {craftsman.available ? "متاح حالياً" : "غير متاح حالياً"}
                    </span>
                  </div>
                </div>
              )}
            </div>
          </motion.div>
        </div>
      </Card>
    </motion.div>
  );
};

export default CraftsmanContactInfo;
