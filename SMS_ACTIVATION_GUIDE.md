# 📱 دليل تفعيل إرسال رمز التحقق عبر SMS

## ✅ **ما تم إنجازه:**

### **1. تكامل HyperSender:**
- ✅ إنشاء خدمة HyperSender كاملة
- ✅ إضافة API Token الخاص بك
- ✅ دعم تنسيقات API متعددة
- ✅ معالجة شاملة للأخطاء

### **2. تحديث الباك إند:**
- ✅ استبدال المحاكاة بخدمة حقيقية
- ✅ إضافة endpoints للاختبار والمراقبة
- ✅ تحسين معالجة الأخطاء

### **3. نظام التحقق المزدوج:**
- ✅ اختيار بين البريد الإلكتروني والهاتف
- ✅ حفظ واستعادة الحالات
- ✅ واجهة مستخدم محسنة

## 🚀 **خطوات التفعيل:**

### **الخطوة 1: تثبيت المتطلبات**
```bash
cd backend
npm install axios
```

### **الخطوة 2: التحقق من الإعدادات**
تأكد من أن ملف `backend/.env` يحتوي على:
```env
HYPERSENDER_API_TOKEN=250|e2Lq3UqTPIzYJBYhdJviP1Zb066RBHuOCWtkj5eY90306903
HYPERSENDER_SENDER_ID=JobScope
HYPERSENDER_API_URL=https://api.hypersender.com/api/send
```

### **الخطوة 3: إعادة تشغيل الخادم**
```bash
cd backend
npm run dev
# أو
node src/server.js
```

### **الخطوة 4: اختبار النظام**

#### **أ. اختبار الاتصال:**
```bash
curl http://localhost:5000/api/auth/test-sms
```

#### **ب. اختبار إرسال رمز التحقق:**
```bash
curl -X POST http://localhost:5000/api/auth/send-otp-phone \
  -H "Content-Type: application/json" \
  -d '{"phone": "+963999123456"}'
```

## 📱 **كيفية الاستخدام:**

### **1. في صفحة تسجيل الحرفي:**
1. المستخدم يختار "رقم الهاتف" كطريقة تحقق
2. يدخل رقم هاتفه السوري
3. يضغط "إرسال رمز التحقق"
4. يستقبل رمز مكون من 6 أرقام
5. يدخل الرمز ويضغط "تحقق"
6. يكمل باقي خطوات التسجيل

### **2. تنسيق الأرقام المدعومة:**
- `+963999123456` ✅
- `0999123456` ✅ (يتم تحويلها تلقائياً)
- `999123456` ✅ (يتم إضافة +963)

## 🔍 **استكشاف الأخطاء:**

### **خطأ: "SMS service is not configured"**
**الحل:**
```bash
# تحقق من ملف .env
cat backend/.env | grep HYPERSENDER

# تأكد من إعادة تشغيل الخادم
cd backend && npm run dev
```

### **خطأ: "Network error"**
**الحل:**
- تحقق من اتصال الإنترنت
- تأكد من صحة API URL
- تحقق من حالة خدمة HyperSender

### **خطأ: "Authentication failed"**
**الحل:**
- تحقق من صحة API Token
- تأكد من أن الرمز لم ينته صلاحيته
- راجع إعدادات حسابك في HyperSender

### **خطأ: "Invalid phone number"**
**الحل:**
- تأكد من أن الرقم سوري صالح
- استخدم الصيغة الدولية (+963)
- تحقق من دعم HyperSender للأرقام السورية

## 📊 **مراقبة النظام:**

### **سجلات الخادم:**
```bash
# مراقبة السجلات المباشرة
cd backend && npm run dev

# البحث عن رسائل SMS
grep "HyperSender" logs/app.log
```

### **رسائل السجل المهمة:**
- `Sending OTP via HyperSender` - بداية الإرسال
- `SMS sent successfully` - نجح الإرسال
- `Failed to send SMS` - فشل الإرسال
- `Format X - HyperSender response` - استجابة API

## 🧪 **اختبار شامل:**

### **1. اختبار سريع:**
```javascript
// في console المتصفح
fetch('http://localhost:5000/api/auth/test-sms')
  .then(r => r.json())
  .then(console.log);
```

### **2. اختبار إرسال حقيقي:**
```javascript
fetch('http://localhost:5000/api/auth/send-otp-phone', {
  method: 'POST',
  headers: {'Content-Type': 'application/json'},
  body: JSON.stringify({phone: '+963999123456'})
}).then(r => r.json()).then(console.log);
```

## ⚡ **نصائح للأداء:**

### **1. تحسين الإرسال:**
- استخدم أرقام بالصيغة الدولية
- تجنب الإرسال المتكرر لنفس الرقم
- راقب رصيد حسابك

### **2. أمان إضافي:**
- لا تشارك API Token
- استخدم HTTPS في الإنتاج
- راقب استخدام الخدمة

## 🎯 **النتيجة المتوقعة:**

عند تفعيل النظام بنجاح:

1. **✅ المستخدم يختار التحقق بالهاتف**
2. **✅ يدخل رقم هاتفه السوري**
3. **✅ يضغط "إرسال رمز التحقق"**
4. **✅ يستقبل رسالة SMS على هاتفه:**
   ```
   رمز التحقق الخاص بك في JobScope هو: 123456
   لا تشارك هذا الرمز مع أي شخص.
   صالح لمدة 10 دقائق.
   ```
5. **✅ يدخل الرمز ويتم التحقق بنجاح**
6. **✅ يكمل تسجيل حسابه**

## 🔧 **إعدادات متقدمة:**

### **تخصيص الرسائل:**
يمكنك تعديل نص الرسائل في:
```javascript
// backend/src/services/hyperSenderService.js
async sendOTP(phone, otp) {
  const message = `رمز التحقق الخاص بك في JobScope هو: ${otp}
لا تشارك هذا الرمز مع أي شخص.
صالح لمدة 10 دقائق.`;
  
  return await this.sendSMS(phone, message);
}
```

### **تغيير مدة انتهاء الصلاحية:**
```javascript
// backend/src/models/OTP.js
otpSchema.index({ createdAt: 1 }, { expireAfterSeconds: 600 }); // 10 دقائق
```

## 🎉 **تهانينا!**

الآن نظام إرسال رمز التحقق عبر SMS يعمل بشكل كامل! 

المستخدمون يمكنهم:
- اختيار التحقق بالهاتف أو البريد الإلكتروني
- استقبال رموز تحقق حقيقية على هواتفهم
- إكمال تسجيل حساباتهم بأمان

النظام جاهز للاستخدام! 🚀
