import React, { useState, useEffect } from "react";
import Button from "../common/Button";
import useThemeStore from "../../store/themeStore";
import useBookingStore from "../../store/bookingStore";
import bookingService from "../../services/bookingService";
import toastUtils from "../../utils/toastUtils";
import { formatDate } from "../../utils/dateUtils";

const BookingEditModal = ({ booking, onClose, onSave }) => {
  const darkMode = useThemeStore((state) => state.darkMode);
  const [formData, setFormData] = useState({
    date: "",
    time: "",
    description: "",
  });
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});
  const [remainingTime, setRemainingTime] = useState(0);
  const [timer, setTimer] = useState(null);

  // تحميل بيانات الطلب عند فتح النموذج
  useEffect(() => {
    if (booking) {
      setFormData({
        date: booking.date
          ? formatDate(new Date(booking.date), "yyyy-MM-dd")
          : "",
        time: booking.time || "",
        description: booking.description || "",
      });

      // حساب الوقت المتبقي لتعديل الطلب
      updateRemainingTime();

      // إنشاء مؤقت لتحديث الوقت المتبقي كل دقيقة
      const intervalId = setInterval(updateRemainingTime, 60000); // 60000 مللي ثانية = دقيقة واحدة
      setTimer(intervalId);
    }

    return () => {
      // تنظيف المؤقت عند إغلاق النموذج
      if (timer) {
        clearInterval(timer);
      }
    };
  }, [booking]);

  // تحديث الوقت المتبقي
  const updateRemainingTime = () => {
    if (booking) {
      const remaining = bookingService.getRemainingEditTime(booking);
      setRemainingTime(remaining);

      // إذا انتهى الوقت، أغلق النموذج
      if (remaining <= 0) {
        toastUtils.showToast("انتهت فترة التعديل المسموح بها", "error");
        onClose();
      }
    }
  };

  // التحقق من صحة البيانات
  const validateForm = () => {
    const newErrors = {};

    if (!formData.date) {
      newErrors.date = "يرجى تحديد التاريخ";
    }

    if (!formData.time) {
      newErrors.time = "يرجى تحديد الوقت";
    }

    if (!formData.description || formData.description.trim().length < 2) {
      newErrors.description = "يرجى إدخال وصف للمشكلة (حرفين على الأقل)";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // معالجة تغيير الحقول
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));

    // مسح الخطأ عند تغيير القيمة
    if (errors[name]) {
      setErrors((prev) => ({
        ...prev,
        [name]: undefined,
      }));
    }
  };

  // معالجة حفظ التعديلات
  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setLoading(true);

    try {
      // تحديث الطلب
      const updatedBooking = await bookingService.updateBooking(
        booking.id,
        formData
      );

      toastUtils.showToast("تم تحديث الطلب بنجاح", "success");

      // استدعاء دالة onSave لتحديث واجهة المستخدم
      if (onSave) {
        onSave(updatedBooking);
      }

      onClose();
    } catch (error) {
      console.error("خطأ في تحديث الطلب:", error);
      toastUtils.showToast("حدث خطأ أثناء تحديث الطلب", "error");
    } finally {
      setLoading(false);
    }
  };

  // معالجة تأكيد الطلب وإرساله للحرفي
  const handleConfirm = async () => {
    if (!validateForm()) {
      return;
    }

    setLoading(true);

    try {
      // أولاً، تحديث بيانات الطلب
      await bookingService.updateBooking(booking.id, formData);

      // ثم، تغيير حالة الطلب إلى "قيد الانتظار" لإرساله للحرفي
      await bookingService.confirmBooking(booking.id);

      toastUtils.showToast("تم تأكيد الطلب وإرساله للحرفي", "success");

      // استدعاء دالة onSave لتحديث واجهة المستخدم
      if (onSave) {
        onSave({ ...booking, ...formData, status: "pending" });
      }

      onClose();
    } catch (error) {
      console.error("خطأ في تأكيد الطلب:", error);
      toastUtils.showToast("حدث خطأ أثناء تأكيد الطلب", "error");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50">
      <div
        className={`w-full max-w-2xl rounded-lg shadow-xl overflow-hidden ${
          darkMode ? "bg-gray-800 text-white" : "bg-white text-gray-800"
        }`}
      >
        <div
          className={`px-6 py-4 border-b ${
            darkMode ? "border-gray-700" : "border-gray-200"
          }`}
        >
          <div className="flex justify-between items-center">
            <h3
              className={`text-xl font-bold ${
                darkMode ? "text-indigo-300" : "text-indigo-700"
              }`}
            >
              تعديل الطلب
            </h3>

            {/* عرض الوقت المتبقي */}
            <div
              className={`text-sm font-medium ${
                remainingTime <= 2
                  ? "text-red-500"
                  : remainingTime <= 5
                  ? "text-yellow-500"
                  : darkMode
                  ? "text-indigo-300"
                  : "text-indigo-600"
              }`}
            >
              الوقت المتبقي للتعديل: {remainingTime} دقيقة
            </div>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="p-6">
          <div className="space-y-4">
            {/* حقل التاريخ */}
            <div>
              <label
                className={`block text-sm font-medium mb-1 ${
                  darkMode ? "text-gray-300" : "text-gray-700"
                }`}
              >
                التاريخ
              </label>
              <input
                type="date"
                name="date"
                value={formData.date}
                onChange={handleChange}
                className={`w-full px-3 py-2 rounded-md ${
                  darkMode
                    ? "bg-gray-700 border-gray-600 text-white"
                    : "bg-white border-gray-300 text-gray-700"
                } ${
                  errors.date ? "border-red-500" : "border-gray-300"
                } focus:outline-none focus:ring-2 focus:ring-indigo-500`}
              />
              {errors.date && (
                <p className="mt-1 text-sm text-red-500">{errors.date}</p>
              )}
            </div>

            {/* حقل الوقت */}
            <div>
              <label
                className={`block text-sm font-medium mb-1 ${
                  darkMode ? "text-gray-300" : "text-gray-700"
                }`}
              >
                الوقت
              </label>
              <input
                type="time"
                name="time"
                value={formData.time}
                onChange={handleChange}
                className={`w-full px-3 py-2 rounded-md ${
                  darkMode
                    ? "bg-gray-700 border-gray-600 text-white"
                    : "bg-white border-gray-300 text-gray-700"
                } ${
                  errors.time ? "border-red-500" : "border-gray-300"
                } focus:outline-none focus:ring-2 focus:ring-indigo-500`}
              />
              {errors.time && (
                <p className="mt-1 text-sm text-red-500">{errors.time}</p>
              )}
            </div>

            {/* حقل الوصف */}
            <div>
              <label
                className={`block text-sm font-medium mb-1 ${
                  darkMode ? "text-gray-300" : "text-gray-700"
                }`}
              >
                وصف المشكلة
              </label>
              <textarea
                name="description"
                value={formData.description}
                onChange={handleChange}
                rows="4"
                className={`w-full px-3 py-2 rounded-md ${
                  darkMode
                    ? "bg-gray-700 border-gray-600 text-white"
                    : "bg-white border-gray-300 text-gray-700"
                } ${
                  errors.description ? "border-red-500" : "border-gray-300"
                } focus:outline-none focus:ring-2 focus:ring-indigo-500`}
              ></textarea>
              {errors.description && (
                <p className="mt-1 text-sm text-red-500">
                  {errors.description}
                </p>
              )}
            </div>
          </div>

          <div className="mt-6 flex flex-wrap justify-end gap-3">
            <Button
              type="button"
              variant="danger"
              onClick={onClose}
              disabled={loading}
              className="px-4 py-2"
            >
              إلغاء
            </Button>

            <Button
              type="button"
              variant="success"
              onClick={handleConfirm}
              disabled={loading}
              className="px-4 py-2"
            >
              تأكيد وإرسال للحرفي
            </Button>

            <Button
              type="submit"
              variant="primary"
              disabled={loading}
              className="px-4 py-2"
            >
              {loading ? "جاري الحفظ..." : "حفظ التعديلات"}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default BookingEditModal;
