/**
 * بيانات المهن والتخصصات
 * يتم استخدام هذا الملف في جميع أنحاء التطبيق للحصول على قائمة المهن والتخصصات
 */
export const professionsData = [
  {
    id: 1,
    name: "كهربائي",
    specializations: [
      "تمديدات منزلية",
      "صيانة كهربائية",
      "تركيب أنظمة إنارة",
      "لوحات كهربائية",
      "تركيب مولدات",
      "صيانة مصاعد",
      "أنظمة طاقة شمسية",
      "أتمتة منزلية",
    ],
  },
  {
    id: 2,
    name: "سباك",
    specializations: [
      "تمديدات صحية",
      "صيانة وتركيب",
      "معالجة تسربات",
      "تركيب أدوات صحية",
      "صيانة سخانات",
      "تركيب خزانات مياه",
      "تركيب مضخات",
      "صيانة شبكات الصرف",
    ],
  },
  {
    id: 3,
    name: "نجار",
    specializations: [
      "أثاث منزلي",
      "أبواب ونوافذ",
      "ديكورات خشبية",
      "مطابخ",
      "غرف نوم",
      "خزائن حائط",
      "أثاث مكتبي",
      "ترميم أثاث قديم",
    ],
  },
  {
    id: 4,
    name: "دهان",
    specializations: [
      "دهانات داخلية",
      "دهانات خارجية",
      "دهانات حديثة",
      "ديكورات جبسية",
      "ورق جدران",
      "دهانات زخرفية",
      "دهانات إيبوكسي",
      "دهانات مقاومة للرطوبة",
    ],
  },
  {
    id: 5,
    name: "مصمم ديكور",
    specializations: [
      "تصميم داخلي",
      "تصميم واجهات",
      "استشارات ديكور",
      "تصميم مساحات تجارية",
      "تصميم مكاتب",
      "تصميم حدائق",
      "تصميم إضاءة",
      "تصميم ثلاثي الأبعاد",
    ],
  },
  {
    id: 6,
    name: "ميكانيكي",
    specializations: [
      "صيانة سيارات",
      "كهرباء سيارات",
      "ميكانيك عام",
      "صيانة محركات",
      "تبديل زيوت",
      "إصلاح فرامل",
      "ضبط زوايا",
      "صيانة تكييف سيارات",
    ],
  },
  {
    id: 7,
    name: "حداد",
    specializations: [
      "أبواب وشبابيك",
      "هياكل معدنية",
      "أعمال الألمنيوم",
      "درابزين",
      "بوابات حديدية",
      "حماية نوافذ",
      "هناجر",
      "أعمال ستانلس ستيل",
    ],
  },
  {
    id: 8,
    name: "بناء",
    specializations: [
      "بناء جدران",
      "تبليط",
      "أعمال إسمنتية",
      "ترميم",
      "تشطيبات",
      "قصارة",
      "عزل مائي",
      "عزل حراري",
    ],
  },
  {
    id: 9,
    name: "مكيفات",
    specializations: [
      "تركيب",
      "صيانة",
      "تنظيف",
      "إصلاح",
      "شحن غاز",
      "تركيب وحدات مركزية",
      "صيانة دورية",
      "استبدال قطع",
    ],
  },
  {
    id: 10,
    name: "خياط",
    specializations: [
      "ملابس رجالية",
      "ملابس نسائية",
      "تفصيل وخياطة",
      "تعديل ملابس",
      "خياطة ستائر",
      "خياطة مفروشات",
      "تطريز",
      "تصميم أزياء",
    ],
  },
  {
    id: 11,
    name: "طباخ",
    specializations: [
      "مأكولات شرقية",
      "حلويات",
      "مأكولات غربية",
      "مشاوي",
      "معجنات",
      "طبخ منزلي",
      "طعام صحي",
      "مناسبات وحفلات",
    ],
  },
  {
    id: 12,
    name: "مزارع",
    specializations: [
      "زراعة خضروات",
      "زراعة أشجار مثمرة",
      "تقليم أشجار",
      "تركيب أنظمة ري",
      "مكافحة آفات",
      "تنسيق حدائق",
      "زراعة عضوية",
      "إنتاج شتلات",
    ],
  },
  {
    id: 13,
    name: "مصلح أجهزة كهربائية",
    specializations: [
      "غسالات",
      "ثلاجات",
      "أفران",
      "مكيفات",
      "تلفزيونات",
      "أجهزة صغيرة",
      "سخانات مياه",
      "مكانس كهربائية",
    ],
  },
  {
    id: 14,
    name: "مصلح موبايلات وكمبيوتر",
    specializations: [
      "إصلاح هواتف",
      "إصلاح حواسيب",
      "تغيير شاشات",
      "إزالة كلمات المرور",
      "استعادة البيانات",
      "إصلاح شبكات",
    ],
  },
  {
    id: 15,
    name: "سائق",
    specializations: [
      "توصيل ركاب",
      "نقل بضائع",
      "نقل أثاث",
      "رحلات بين المدن",
      "توصيل طلبات",
      "سيارات خاصة",
      "شاحنات",
      "حافلات",
    ],
  },
  {
    id: 16,
    name: "مصور",
    specializations: [
      "تصوير مناسبات",
      "تصوير منتجات",
      "تصوير عقارات",
      "تصوير فوتوغرافي",
      "تصوير فيديو",
      "مونتاج",
      "تصوير جوي",
      "تصوير وثائقي",
    ],
  },
  {
    id: 17,
    name: "معلم",
    specializations: [
      "رياضيات",
      "فيزياء",
      "كيمياء",
      "لغة عربية",
      "لغة إنجليزية",
      "علوم",
      "تاريخ وجغرافيا",
      "تقوية دراسية",
    ],
  },
  {
    id: 20,
    name: "حلاق",
    specializations: [
      "قص شعر رجالي",
      "حلاقة ذقن",
      "تصفيف شعر نسائي",
      "صبغ شعر",
      "تسريحات",
      "علاجات شعر",
      "ماكياج",
      "عناية بالبشرة",
    ],
  },
  {
    id: 21,
    name: "تركيب و صيانة ألمنيوم",
    specializations: [
      "تركيب نوافذ ألمنيوم",
      "أبواب ألمنيوم",
      "واجهات زجاجية",
      "مطابخ ألمنيوم",
      "غرف زجاجية",
      "شتر ودرابزين",
      "صيانة ألمنيوم",
      "تركيب سكك وأقفال",
    ],
  },
  {
    id: 22,
    name: "معلم سيراميك",
    specializations: [
      "تبليط أرضيات",
      "تبليط جدران حمامات",
      "قص وتشكيل السيراميك",
      "تركيب بورسلان",
      "تركيب غرانيت",
      "تنسيق فواصل",
      "تركيب سيراميك ثلاثي الأبعاد",
      "تصليح وتعديل بلاط",
    ],
  },
  {
    id: 25,
    name: "عامل نظافة",
    specializations: [
      "تنظيف منازل",
      "تنظيف مكاتب",
      "تنظيف واجهات زجاجية",
      "تنظيف خزانات مياه",
      "غسيل سجاد",
      "تنظيف مطاعم ومحلات",
      "تعقيم وتعطير",
      "تنظيف ما بعد البناء",
    ],
  },
  {
    id: 26,
    name: "عامل توصيل",
    specializations: [
      "توصيل طلبات طعام",
      "توصيل مستندات",
      "توصيل أدوية",
      "توصيل منتجات من متاجر",
      "توصيل ضمن المدينة",
      "توصيل سريع",
      "دراجة نارية",
      "سيارة صغيرة",
    ],
  },
  {
    id: 28,
    name: "عامل صيانة عامة",
    specializations: [
      "تصليح أثاث منزلي",
      "صيانة أبواب ونوافذ",
      "صيانة حمامات ومطابخ",
      "تصليح تسربات",
      "تصليح أقفال",
      "تثبيت أثاث",
      "صيانة دورية",
      "تركيب إكسسوارات منزلية",
    ],
  },
  {
    id: 30,
    name: "فني تبريد وتكييف",
    specializations: [
      "تركيب برادات",
      "صيانة وحدات تبريد",
      "تعبئة غاز تبريد",
      "صيانة برادات تجارية",
      "أنظمة تبريد صناعي",
      "فحص أعطال",
      "تركيب غرف تبريد",
      "تركيب وحدات تبريد مركزية",
    ],
  },
  {
    id: 31,
    name: "عامل بناء",
    specializations: [
      "بناء جدران حجر",
      "بناء جدران بلوك",
      "قصارة (لياسة)",
      "صب أعمدة وأسقف",
      "تركيب حجارة واجهات",
      "فورمجي (خشب تسليح)",
      "أعمال ترميم",
      "رفع سقايل (سقالات)",
    ],
  },
  {
    id: 33,
    name: "فني تمديدات صحية",
    specializations: [
      "تركيب مغاسل",
      "تمديد أنابيب مياه",
      "صيانة تسريبات",
      "تصليح مضخات",
      "تركيب سخانات",
      "فحص ضغط المياه",
      "تنظيف مجاري",
      "تجهيز حمامات جديدة",
    ],
  },
  {
    id: 34,
    name: "فني لحام وحدادة",
    specializations: [
      "لحام أبواب حديد",
      "لحام شبابيك",
      "لحام سلالم",
      "تصنيع قواعد معدنية",
      "لحام بالغاز",
      "لحام كهرباء",
      "قص وتشكيل الحديد",
      "صيانة هياكل معدنية",
    ],
  },
  {
    id: 38,
    name: "حدّاد متخصص في الأبواب والنوافذ",
    specializations: [
      "تصنيع أبواب حديد",
      "شبابيك حديد مزخرفة",
      "بوابات خارجية",
      "تركيب أبواب أمنية",
      "درابزينات وسلالم",
      "أبواب محلات",
      "صيانة لحام",
      "دهان الحديد",
    ],
  },
  // خدمات الصحة والجمال
  {
    id: 40,
    name: "طبيب",
    specializations: [
      "طب عام",
      "طب أسنان",
      "طب عيون",
      "طب أطفال",
      "طب باطني",
      "طب جلدية",
      "طب نسائية وتوليد",
      "طب عظام",
    ],
  },
  {
    id: 41,
    name: "ممرض",
    specializations: [
      "رعاية منزلية",
      "حقن وتضميد",
      "رعاية مسنين",
      "رعاية أطفال",
      "قياس ضغط وسكر",
      "تركيب محاليل",
      "رعاية ما بعد العمليات",
      "إسعافات أولية",
    ],
  },
  {
    id: 42,
    name: "معالج فيزيائي",
    specializations: [
      "علاج إصابات رياضية",
      "علاج آلام الظهر",
      "علاج ما بعد الكسور",
      "تأهيل حركي",
      "علاج تشنجات عضلية",
      "علاج مشاكل المفاصل",
      "تدليك علاجي",
      "علاج طبيعي منزلي",
    ],
  },
  {
    id: 43,
    name: "خبير تجميل",
    specializations: [
      "مكياج احترافي",
      "تصفيف شعر",
      "عناية بالبشرة",
      "مكياج عرائس",
      "حناء وتزيين",
      "قص وصبغ شعر",
      "علاجات تجميلية",
      "مانيكير وباديكير",
    ],
  },
  // خدمات الطعام والضيافة
  {
    id: 50,
    name: "شيف",
    specializations: [
      "مأكولات شرقية",
      "مأكولات غربية",
      "حلويات",
      "مشاوي",
      "مأكولات بحرية",
      "معجنات",
      "طبخ منزلي",
      "طعام صحي",
    ],
  },
  {
    id: 51,
    name: "نادل",
    specializations: [
      "خدمة مطاعم",
      "خدمة حفلات",
      "خدمة مناسبات",
      "خدمة مؤتمرات",
      "خدمة كوكتيل",
      "خدمة بوفيه",
      "خدمة كبار الشخصيات",
      "خدمة فنادق",
    ],
  },
  {
    id: 52,
    name: "منسق حفلات",
    specializations: [
      "تنسيق أعراس",
      "تنسيق مناسبات",
      "تنسيق مؤتمرات",
      "تنسيق حفلات أطفال",
      "تنسيق حفلات تخرج",
      "تنسيق معارض",
      "تنسيق ديكور حفلات",
      "تنسيق بوفيهات",
    ],
  },
  // خدمات الإلكترونيات والتقنية
  {
    id: 60,
    name: "فني إلكترونيات",
    specializations: [
      "إصلاح أجهزة كهربائية",
      "إصلاح أجهزة إلكترونية",
      "إصلاح أجهزة منزلية",
      "إصلاح أجهزة صوت",
      "إصلاح أجهزة تلفاز",
      "إصلاح أجهزة ألعاب",
      "إصلاح أجهزة طبية",
      "تركيب أنظمة إلكترونية",
    ],
  },
  {
    id: 61,
    name: "مصمم مواقع",
    specializations: [
      "تصميم مواقع تعريفية",
      "تصميم متاجر إلكترونية",
      "تصميم مواقع خدمية",
      "تصميم مواقع شخصية",
      "تصميم مواقع تعليمية",
      "تصميم مواقع إخبارية",
      "تصميم واجهات مستخدم",
      "تطوير مواقع ووردبريس",
    ],
  },
  {
    id: 62,
    name: "مطور تطبيقات",
    specializations: [
      "تطوير تطبيقات أندرويد",
      "تطوير تطبيقات آيفون",
      "تطوير تطبيقات ويب",
      "تطوير تطبيقات سطح المكتب",
      "تطوير ألعاب",
      "تطوير تطبيقات تعليمية",
      "تطوير تطبيقات خدمية",
      "صيانة وتحديث تطبيقات",
    ],
  },
];

/**
 * دالة للحصول على التخصصات المرتبطة بمهنة معينة
 * @param {string} professionName - اسم المهنة
 * @param {Array} userSpecializations - قائمة تخصصات المستخدم
 * @returns {Array} - قائمة التخصصات المرتبطة بالمهنة
 */
export const getRelatedSpecializations = (
  professionName,
  userSpecializations
) => {
 

  if (
    !professionName ||
    !userSpecializations ||
    !Array.isArray(userSpecializations)
  ) {
   
    return [];
  }

  const profData = professionsData.find((p) => p.name === professionName);
  if (!profData) {
   
    return [];
  }

  // تصفية تخصصات المستخدم التي تنتمي إلى هذه المهنة
  const relatedSpecs = userSpecializations.filter((spec) =>
    profData.specializations.includes(spec)
  );

  return relatedSpecs;
};

/**
 * دالة للحصول على جميع التخصصات المتاحة
 * @returns {Array} - قائمة بجميع التخصصات المتاحة
 */
export const getAllSpecializations = () => {
  const allSpecializations = [];
  professionsData.forEach((profession) => {
    profession.specializations.forEach((spec) => {
      if (!allSpecializations.includes(spec)) {
        allSpecializations.push(spec);
      }
    });
  });
  return allSpecializations;
};

/**
 * دالة للحصول على جميع المهن المتاحة
 * @returns {Array} - قائمة بجميع المهن المتاحة
 */
export const getAllProfessions = () => {
  return professionsData.map((profession) => profession.name);
};

export default professionsData;
