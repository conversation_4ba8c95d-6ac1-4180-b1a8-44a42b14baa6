import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';

/**
 * Hook لإعادة تعيين موضع التمرير عند الانتقال بين الصفحات
 * يمكن استخدامه في أي مكون للتأكد من أن الصفحة تبدأ من الأعلى
 * 
 * @param {boolean} resetOnMount - إذا كان true، سيتم إعادة تعيين موضع التمرير عند تحميل المكون
 * @param {boolean} resetOnPathChange - إذا كان true، سيتم إعادة تعيين موضع التمرير عند تغيير المسار
 */
const useScrollReset = (resetOnMount = true, resetOnPathChange = true) => {
  const { pathname } = useLocation();

  // إعادة تعيين موضع التمرير عند تحميل المكون
  useEffect(() => {
    if (resetOnMount) {
      window.scrollTo(0, 0);
    }
  }, [resetOnMount]);

  // إعادة تعيين موضع التمرير عند تغيير المسار
  useEffect(() => {
    if (resetOnPathChange) {
      window.scrollTo(0, 0);
    }
  }, [pathname, resetOnPathChange]);
};

export default useScrollReset;
