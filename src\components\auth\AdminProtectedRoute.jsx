import React, { useEffect, useState } from "react";
import { Navigate, Outlet } from "react-router-dom";
import useAdminStore from "../../store/adminStore";

// مكون لحماية مسارات الأدمن
const AdminProtectedRoute = () => {
  const { isAuthenticated, admin, checkAuth } = useAdminStore();
  const [isChecking, setIsChecking] = useState(true);

  useEffect(() => {
    const verifyAuth = async () => {
      try {
        console.log("AdminProtectedRoute: بدء التحقق من مصادقة الأدمن");

        // التحقق من وجود توكن الأدمن
        const adminToken = localStorage.getItem("adminToken");
        const adminData = localStorage.getItem("admin");

        if (!adminToken) {
          console.log("AdminProtectedRoute: لا يوجد توكن أدمن");
          setIsChecking(false);
          return;
        }

        // إذا كانت البيانات موجودة محلياً وحالة المصادقة صحيحة، لا نحتاج للتحقق من الخادم
        if (adminData && isAuthenticated && admin) {
          console.log("AdminProtectedRoute: الأدمن مصادق عليه محلياً");
          setIsChecking(false);
          return;
        }

        // التحقق من الخادم
        await checkAuth();
        console.log("AdminProtectedRoute: تم التحقق من مصادقة الأدمن بنجاح");
      } catch (error) {
        console.error(
          "AdminProtectedRoute: خطأ في التحقق من مصادقة الأدمن:",
          error
        );
      } finally {
        setIsChecking(false);
      }
    };

    verifyAuth();
  }, [checkAuth, isAuthenticated, admin]);

  // عرض شاشة تحميل أثناء التحقق
  if (isChecking) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-indigo-600 mx-auto mb-4"></div>
          <p className="text-indigo-600 font-medium">
            جاري التحقق من صلاحيات الأدمن...
          </p>
        </div>
      </div>
    );
  }

  // إذا لم يكن المدير مسجل دخوله، قم بتوجيهه إلى صفحة تسجيل دخول المدير
  if (!isAuthenticated || !admin) {
    console.log("AdminProtectedRoute: إعادة توجيه إلى صفحة تسجيل دخول الأدمن");
    return <Navigate to="/admin/login" replace />;
  }

  // إذا كان المدير مسجل دخوله، اعرض المحتوى المحمي
  console.log("AdminProtectedRoute: السماح بالوصول للمحتوى المحمي");
  return <Outlet />;
};

export default AdminProtectedRoute;
