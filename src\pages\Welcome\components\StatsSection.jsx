import React from "react";
import { motion } from "framer-motion";
import useThemeStore from "../../../store/themeStore";

const StatsSection = () => {
  const darkMode = useThemeStore((state) => state.darkMode);

  return (
    <section
      className={`py-20 ${
        darkMode
          ? "bg-gradient-to-b from-gray-800 to-gray-900"
          : "bg-gradient-to-b from-indigo-100 to-blue-50"
      } transition-colors duration-300 relative overflow-hidden`}
    >
      {/* أشكال زخرفية في الخلفية */}
      <div className="absolute inset-0 overflow-hidden">
        <div className={`absolute top-0 left-0 w-full h-1 ${darkMode ? 'bg-gradient-to-r from-transparent via-indigo-600/30 to-transparent' : 'bg-gradient-to-r from-transparent via-indigo-500/30 to-transparent'}`}></div>
        <div className={`absolute bottom-0 left-0 w-full h-1 ${darkMode ? 'bg-gradient-to-r from-transparent via-indigo-600/30 to-transparent' : 'bg-gradient-to-r from-transparent via-indigo-500/30 to-transparent'}`}></div>
      </div>
      <div className="container mx-auto px-4 relative z-10">
        <div className="text-center mb-10">
          <h2
            className={`text-3xl font-bold ${
              darkMode ? "text-indigo-300" : "text-indigo-800"
            } relative inline-block transition-colors duration-300`}
          >
            <span className="relative z-10">إحصائيات المنصة</span>
            <span
              className={`absolute bottom-0 left-0 right-0 h-3 ${
                darkMode ? "bg-indigo-500" : "bg-indigo-300"
              } opacity-40 transform -rotate-1 z-0`}
            ></span>
          </h2>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <motion.div
            className={`${
              darkMode
                ? "bg-gradient-to-br from-indigo-900 to-blue-900 border-indigo-700"
                : "bg-gradient-to-br from-indigo-500 to-indigo-600 border-indigo-400"
            } text-white p-8 rounded-xl text-center shadow-xl border`}
            whileHover={{ y: -5, boxShadow: "0 25px 50px -12px rgba(79, 70, 229, 0.4)" }}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            <div className="flex justify-center mb-4">
              <div className="w-16 h-16 rounded-full bg-white/20 flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
              </div>
            </div>
            <h3 className="text-4xl font-bold mb-2">+2000</h3>
            <p className="text-xl">حرفي مسجل</p>
          </motion.div>

          <motion.div
            className={`${
              darkMode
                ? "bg-gradient-to-br from-blue-900 to-indigo-900 border-blue-700"
                : "bg-gradient-to-br from-blue-500 to-indigo-500 border-blue-400"
            } text-white p-8 rounded-xl text-center shadow-xl border`}
            whileHover={{ y: -5, boxShadow: "0 25px 50px -12px rgba(79, 70, 229, 0.4)" }}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.1 }}
          >
            <div className="flex justify-center mb-4">
              <div className="w-16 h-16 rounded-full bg-white/20 flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
                </svg>
              </div>
            </div>
            <h3 className="text-4xl font-bold mb-2">+5000</h3>
            <p className="text-xl">طلب خدمة</p>
          </motion.div>

          <motion.div
            className={`${
              darkMode
                ? "bg-gradient-to-br from-indigo-900 to-purple-900 border-indigo-700"
                : "bg-gradient-to-br from-indigo-600 to-purple-600 border-indigo-400"
            } text-white p-8 rounded-xl text-center shadow-xl border`}
            whileHover={{ y: -5, boxShadow: "0 25px 50px -12px rgba(79, 70, 229, 0.4)" }}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.2 }}
          >
            <div className="flex justify-center mb-4">
              <div className="w-16 h-16 rounded-full bg-white/20 flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
              </div>
            </div>
            <h3 className="text-4xl font-bold mb-2">+10</h3>
            <p className="text-xl">مدن مغطاة</p>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default StatsSection;
