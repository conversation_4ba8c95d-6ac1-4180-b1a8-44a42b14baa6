import React, { useState, useEffect } from "react";
import useThemeStore from "../../../store/themeStore";
import adminService from "../../../services/adminService";

const ProfileSection = ({ admin }) => {
  const darkMode = useThemeStore((state) => state.darkMode);
  const [adminData, setAdminData] = useState(null);
  const [loading, setLoading] = useState(true);

  // جلب بيانات المدير من الباك اند
  useEffect(() => {
    const fetchAdminData = async () => {
      try {
        setLoading(true);
        const response = await adminService.checkAdminAuth();
        setAdminData(response);
      } catch (error) {
        console.error("خطأ في جلب بيانات المدير:", error);
        // استخدام البيانات المرسلة كـ props في حالة الخطأ
        setAdminData(admin);
      } finally {
        setLoading(false);
      }
    };

    fetchAdminData();
  }, [admin]);

  // دالة لتنسيق التاريخ والوقت
  const formatDateTime = (dateString) => {
    if (!dateString) return "غير محدد";

    try {
      const date = new Date(dateString);
      const now = new Date();
      const diffInHours = (now - date) / (1000 * 60 * 60);

      if (diffInHours < 1) {
        const diffInMinutes = Math.floor((now - date) / (1000 * 60));
        return `منذ ${diffInMinutes} دقيقة`;
      } else if (diffInHours < 24) {
        return `منذ ${Math.floor(diffInHours)} ساعة`;
      } else if (diffInHours < 48) {
        return "أمس";
      } else {
        return date.toLocaleDateString("en-US", {
          year: "numeric",
          month: "long",
          day: "numeric",
          hour: "2-digit",
          minute: "2-digit",
        });
      }
    } catch (error) {
      return dateString;
    }
  };

  // دالة لتنسيق التاريخ فقط (ميلادي)
  const formatDate = (dateString) => {
    if (!dateString) return "غير محدد";

    try {
      const date = new Date(dateString);
      return date.toLocaleDateString("en-US", {
        year: "numeric",
        month: "long",
        day: "numeric",
      });
    } catch (error) {
      return dateString;
    }
  };

  // دالة لتنسيق الصلاحيات
  const formatPermissions = (permissions) => {
    if (!permissions || !Array.isArray(permissions)) return "غير محدد";

    const permissionLabels = {
      manage_users: "إدارة المستخدمين",
      manage_craftsmen: "إدارة الحرفيين",
      manage_bookings: "إدارة الحجوزات",
      manage_content: "إدارة المحتوى",
      manage_professions: "إدارة المهن",
      manage_system: "إدارة النظام",
    };

    return permissions
      .map((permission) => permissionLabels[permission] || permission)
      .join("، ");
  };

  // عرض شاشة التحميل
  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  const currentAdmin = adminData || admin;

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h3
          className={`text-xl font-bold ${
            darkMode ? "text-indigo-300" : "text-indigo-800"
          } relative`}
        >
          <span className="relative z-10">الملف الشخصي</span>
          <span
            className={`absolute bottom-0 left-0 right-0 h-2 ${
              darkMode ? "bg-indigo-500" : "bg-indigo-300"
            } opacity-40 transform -rotate-1 z-0`}
          ></span>
        </h3>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* بطاقة المعلومات الشخصية */}
        <div
          className={`col-span-1 p-6 rounded-lg ${
            darkMode
              ? "bg-gray-700"
              : "bg-gradient-to-br from-white to-indigo-100/40"
          } border ${
            darkMode ? "border-gray-600" : "border-indigo-200"
          } shadow-md`}
        >
          <div className="flex flex-col items-center text-center">
            <img
              src={currentAdmin?.image || "/img/default-avatar-2-modified.svg"}
              alt={currentAdmin?.name}
              className="w-32 h-32 rounded-full border-4 border-indigo-300 mb-4"
              onError={(e) => {
                e.target.src = "/img/default-avatar-2-modified.svg";
              }}
            />
            <h3
              className={`text-xl font-bold ${
                darkMode ? "text-white" : "text-indigo-800"
              } mb-1`}
            >
              {currentAdmin?.name || "غير محدد"}
            </h3>
            <p
              className={`text-sm ${
                darkMode ? "text-gray-300" : "text-gray-600"
              } mb-4`}
            >
              {currentAdmin?.role || "admin"}
            </p>
            <div
              className={`w-full p-2 rounded-md ${
                darkMode
                  ? "bg-gray-800"
                  : "bg-gradient-to-r from-indigo-50/70 to-indigo-100/50"
              } text-center mb-2 border ${
                darkMode ? "border-gray-700" : "border-indigo-200"
              }`}
            >
              <p
                className={`text-sm ${
                  darkMode ? "text-gray-300" : "text-gray-600"
                }`}
              >
                البريد الإلكتروني
              </p>
              <p
                className={`font-medium ${
                  darkMode ? "text-indigo-300" : "text-indigo-700"
                }`}
              >
                {currentAdmin?.email || "غير محدد"}
              </p>
            </div>
            <div
              className={`w-full p-2 rounded-md ${
                darkMode
                  ? "bg-gray-800"
                  : "bg-gradient-to-r from-indigo-50/70 to-indigo-100/50"
              } text-center border ${
                darkMode ? "border-gray-700" : "border-indigo-200"
              }`}
            >
              <p
                className={`text-sm ${
                  darkMode ? "text-gray-300" : "text-gray-600"
                }`}
              >
                رقم الهاتف
              </p>
              <p
                className={`font-medium ${
                  darkMode ? "text-indigo-300" : "text-indigo-700"
                }`}
              >
                {currentAdmin?.phone || "غير محدد"}
              </p>
            </div>
          </div>
        </div>

        {/* معلومات إضافية */}
        <div
          className={`col-span-2 p-6 rounded-lg ${
            darkMode
              ? "bg-gray-700"
              : "bg-gradient-to-br from-white to-indigo-100/40"
          } border ${
            darkMode ? "border-gray-600" : "border-indigo-200"
          } shadow-md`}
        >
          <h4
            className={`text-lg font-bold mb-4 ${
              darkMode ? "text-indigo-300" : "text-indigo-700"
            } border-b ${
              darkMode ? "border-gray-600" : "border-gray-200"
            } pb-2`}
          >
            معلومات المدير
          </h4>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            <div>
              <p
                className={`text-sm ${
                  darkMode ? "text-gray-300" : "text-gray-600"
                } mb-1`}
              >
                تاريخ الانضمام (createdAt)
              </p>
              <p
                className={`font-medium ${
                  darkMode ? "text-white" : "text-gray-800"
                }`}
              >
                {formatDate(currentAdmin?.createdAt)}
              </p>
            </div>
            <div>
              <p
                className={`text-sm ${
                  darkMode ? "text-gray-300" : "text-gray-600"
                } mb-1`}
              >
                آخر تحديث (updatedAt)
              </p>
              <p
                className={`font-medium ${
                  darkMode ? "text-white" : "text-gray-800"
                }`}
              >
                {formatDate(currentAdmin?.updatedAt)}
              </p>
            </div>
            <div>
              <p
                className={`text-sm ${
                  darkMode ? "text-gray-300" : "text-gray-600"
                } mb-1`}
              >
                آخر تسجيل دخول (lastLogin)
              </p>
              <p
                className={`font-medium ${
                  darkMode ? "text-white" : "text-gray-800"
                }`}
              >
                {formatDateTime(currentAdmin?.lastLogin)}
              </p>
            </div>
            <div>
              <p
                className={`text-sm ${
                  darkMode ? "text-gray-300" : "text-gray-600"
                } mb-1`}
              >
                الصلاحيات (permissions)
              </p>
              <p
                className={`font-medium ${
                  darkMode ? "text-white" : "text-gray-800"
                }`}
              >
                {formatPermissions(currentAdmin?.permissions)}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProfileSection;
