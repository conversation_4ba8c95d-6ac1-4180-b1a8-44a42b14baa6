import React, { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import { motion } from "framer-motion";
import Input from "../../components/common/Input";
import Button from "../../components/common/Button";
import Layout from "../../components/layout/Layout";
import useAdminStore from "../../store/adminStore";
import useThemeStore from "../../store/themeStore";
import AdminLockoutTimer from "../../components/admin/AdminLockoutTimer";
import adminSecurity from "../../utils/adminSecurity";

const AdminLogin = () => {
  const {
    error,
    loading,
    isAuthenticated,
    admin,
    loginAdmin,
    resetError,
  } = useAdminStore();
  const darkMode = useThemeStore((state) => state.darkMode);

  const [formData, setFormData] = useState({
    username: "",
    password: "",
  });
  const [rememberMe, setRememberMe] = useState(false);
  const [errors, setErrors] = useState({});
  const [isLocked, setIsLocked] = useState(false);

  // التحقق من وجود جلسة سابقة ونظام الحماية
  useEffect(() => {
    const checkExistingSession = () => {
      console.log("AdminLogin: التحقق من وجود جلسة سابقة");

      // التحقق من حالة القفل أولاً
      const lockStatus = adminSecurity.isLocked();
      setIsLocked(lockStatus.locked);

      // إذا كان الأدمن مصادق عليه بالفعل، انتقل للداشبورد
      if (isAuthenticated && admin) {
        console.log(
          "AdminLogin: الأدمن مصادق عليه بالفعل، إعادة توجيه للداشبورد"
        );
        window.location.href = "/admin/dashboard";
        return;
      }

      const adminToken = localStorage.getItem("adminToken");
      const adminData = localStorage.getItem("admin");
      const adminTokenExpiry = localStorage.getItem("adminTokenExpiry");
      const savedCredentials = localStorage.getItem("adminCredentials");

      // التحقق من صحة الجلسة
      if (adminToken && adminData) {
        console.log("AdminLogin: تم العثور على توكن وبيانات أدمن محلية");

        // التحقق من انتهاء صلاحية التوكن إذا كان موجود
        if (adminTokenExpiry) {
          const expiryDate = new Date(adminTokenExpiry);
          const now = new Date();

          if (now > expiryDate) {
            console.log("AdminLogin: انتهت صلاحية التوكن، مسح البيانات");
            // انتهت الصلاحية، مسح البيانات
            localStorage.removeItem("adminToken");
            localStorage.removeItem("admin");
            localStorage.removeItem("adminTokenExpiry");
            return;
          }
        }

        console.log("AdminLogin: الجلسة صالحة، إعادة توجيه للداشبورد");
        // الجلسة صالحة، انتقل للداشبورد
        window.location.href = "/admin/dashboard";
        return;
      }

      // ملء البيانات المحفوظة إذا كانت موجودة
      if (savedCredentials) {
        try {
          const credentials = JSON.parse(savedCredentials);
          console.log("AdminLogin: تم العثور على بيانات اعتماد محفوظة");
          setFormData({
            username: credentials.email || credentials.username || "",
            password: "", // لا نحفظ كلمة المرور لأسباب أمنية
          });
          setRememberMe(true);
        } catch (error) {
          console.error(
            "AdminLogin: خطأ في تحليل بيانات الاعتماد المحفوظة:",
            error
          );
        }
      }
    };

    checkExistingSession();

    // إضافة مستمع لتحديثات نظام الحماية
    const handleSecurityUpdate = (lockStatus) => {
      setIsLocked(lockStatus.locked);
    };

    adminSecurity.addListener(handleSecurityUpdate);

    // تنظيف المستمع عند إلغاء المكون
    return () => {
      adminSecurity.removeListener(handleSecurityUpdate);
    };
  }, [isAuthenticated, admin]);

  // إعادة تعيين رسالة الخطأ عند تحميل الصفحة
  useEffect(() => {
    resetError();
  }, [resetError]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });

    // مسح الخطأ عندما يكتب المستخدم
    if (errors[name]) {
      setErrors({
        ...errors,
        [name]: "",
      });
    }
  };

  const validateForm = () => {
    let isValid = true;
    const newErrors = {};

    // التحقق من اسم المستخدم
    if (!formData.username) {
      newErrors.username = "اسم المستخدم مطلوب";
      isValid = false;
    }

    // التحقق من كلمة المرور
    if (!formData.password) {
      newErrors.password = "كلمة المرور مطلوبة";
      isValid = false;
    }

    setErrors(newErrors);
    return isValid;
  };

  const handleLogin = async (e) => {
    e.preventDefault();

    // التحقق من حالة القفل أولاً
    const lockStatus = adminSecurity.isLocked();
    if (lockStatus.locked) {
      setErrors({
        general: `تم قفل تسجيل الدخول. يرجى المحاولة بعد: ${adminSecurity.formatTimeRemaining(
          lockStatus.timeRemaining
        )}`,
      });
      return;
    }

    if (validateForm()) {
      console.log("AdminLogin: بدء عملية تسجيل الدخول");

      try {
        // استخدام adminStore لتسجيل الدخول
        const success = await loginAdmin({
          email: formData.username,
          password: formData.password,
          rememberMe: rememberMe,
        });

        if (success) {
          console.log(
            "AdminLogin: تم تسجيل الدخول بنجاح، إعادة توجيه للداشبورد"
          );
          // مسح بيانات القفل عند النجاح
          adminSecurity.reset();
          // انتقال مباشر للداشبورد
          window.location.href = "/admin/dashboard";
        } else {
          console.log("AdminLogin: فشل تسجيل الدخول");
          // تسجيل محاولة فاشلة
          const lockoutData = adminSecurity.recordFailedAttempt();

          if (lockoutData.isLocked) {
            setIsLocked(true);
            setErrors({
              general: `تم تجاوز عدد المحاولات المسموحة. تم قفل تسجيل الدخول لمدة ساعة.`,
            });
          } else {
            setErrors({
              general: `بيانات الدخول غير صحيحة. المحاولة ${lockoutData.attempts} من ${adminSecurity.MAX_ATTEMPTS}`,
            });
          }
        }
      } catch (error) {
        console.error("AdminLogin: خطأ في تسجيل الدخول:", error);

        // تسجيل محاولة فاشلة
        const lockoutData = adminSecurity.recordFailedAttempt();

        if (lockoutData.isLocked) {
          setIsLocked(true);
          setErrors({
            general: `تم تجاوز عدد المحاولات المسموحة. تم قفل تسجيل الدخول لمدة ساعة.`,
          });
        } else {
          setErrors({
            general: `بيانات الدخول غير صحيحة. المحاولة ${lockoutData.attempts} من ${adminSecurity.MAX_ATTEMPTS}`,
          });
        }
      }
    }
  };

  const handleRememberMeChange = (e) => {
    const isChecked = e.target.checked;
    setRememberMe(isChecked);

    // إذا تم إلغاء تفعيل "تذكرني"، امسح بيانات الاعتماد المحفوظة
    if (!isChecked) {
      localStorage.removeItem("adminCredentials");
      // مسح البيانات من النموذج أيضاً إذا كانت محفوظة
      const savedCredentials = localStorage.getItem("adminCredentials");
      if (savedCredentials) {
        setFormData({
          username: "",
          password: "",
        });
      }
    }
  };

  // دالة لمسح جميع بيانات الأدمن
  const clearAllAdminData = () => {
    localStorage.removeItem("adminToken");
    localStorage.removeItem("admin");
    localStorage.removeItem("adminTokenExpiry");
    localStorage.removeItem("adminCredentials");
    setFormData({ username: "", password: "" });
    setRememberMe(false);
    setErrors({});

    // إعادة تحميل الصفحة لضمان التنظيف الكامل
    window.location.reload();
  };

  return (
    <Layout hideFooter hideHeader>
      <div
        className={`min-h-screen py-12 ${
          darkMode
            ? "bg-gray-900"
            : "bg-gradient-to-br from-blue-50 to-indigo-100"
        } transition-colors duration-300`}
      >
        <style
          dangerouslySetInnerHTML={{
            __html: `
          @keyframes pulse {
            0% {
              box-shadow: 0 0 0 0 rgba(99, 102, 241, 0.4);
            }
            70% {
              box-shadow: 0 0 0 10px rgba(99, 102, 241, 0);
            }
            100% {
              box-shadow: 0 0 0 0 rgba(99, 102, 241, 0);
            }
          }
        `,
          }}
        />
        <div className="container mx-auto px-4">
          <motion.div
            className={`max-w-lg mx-auto rounded-lg shadow-xl overflow-hidden border-2 ${
              darkMode
                ? "bg-gray-800 border-gray-700"
                : "bg-white border-indigo-200"
            } transition-colors duration-300`}
            initial={{ opacity: 0, y: 20, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            transition={{ duration: 0.5, ease: "easeOut" }}
          >
            <div className="p-6">
              <h2
                className={`text-2xl font-bold text-center mb-4 ${
                  darkMode ? "text-indigo-300" : "text-indigo-800"
                } relative transition-colors duration-300`}
              >
                <span className="relative z-10">لوحة إدارة JobScope</span>
                <span
                  className={`absolute bottom-0 left-0 right-0 h-3 ${
                    darkMode ? "bg-indigo-500" : "bg-indigo-300"
                  } opacity-40 transform -rotate-1 z-0`}
                ></span>
              </h2>
              <p
                className={`text-center mb-6 ${
                  darkMode ? "text-gray-300" : "text-gray-600"
                }`}
              >
                تسجيل الدخول إلى لوحة التحكم
              </p>

              {(error || errors.general) && (
                <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
                  {error || errors.general}
                </div>
              )}

              {/* عرض العداد إذا كان مقفل */}
              {isLocked && (
                <div className="mb-6">
                  <AdminLockoutTimer />
                </div>
              )}

              <form onSubmit={handleLogin}>
                <Input
                  label="اسم المستخدم"
                  type="text"
                  name="username"
                  value={formData.username}
                  onChange={handleInputChange}
                  placeholder="أدخل اسم المستخدم"
                  error={errors.username}
                  required
                  disabled={isLocked}
                  className="text-base py-2 border-indigo-200 focus:border-indigo-500 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 mb-3"
                />

                <Input
                  label="كلمة المرور"
                  type="password"
                  name="password"
                  value={formData.password}
                  onChange={handleInputChange}
                  placeholder="أدخل كلمة المرور"
                  error={errors.password}
                  required
                  disabled={isLocked}
                  className="text-base py-2 border-indigo-200 focus:border-indigo-500 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                />

                <div className="flex items-center mt-4">
                  <input
                    type="checkbox"
                    id="rememberMe"
                    checked={rememberMe}
                    onChange={handleRememberMeChange}
                    className={`h-4 w-4 ${
                      darkMode
                        ? "bg-gray-700 border-gray-600 text-indigo-500"
                        : "bg-white border-indigo-300 text-indigo-600"
                    } rounded focus:ring-indigo-500 focus:ring-offset-0`}
                  />
                  <label
                    htmlFor="rememberMe"
                    className={`mr-2 text-sm ${
                      darkMode ? "text-gray-300" : "text-gray-700"
                    }`}
                  >
                    تذكرني
                  </label>
                </div>

                <Button
                  type="submit"
                  variant="primary"
                  fullWidth
                  disabled={loading || isLocked}
                  className={`mt-4 ${
                    isLocked
                      ? "bg-gray-400 cursor-not-allowed"
                      : darkMode
                      ? "bg-gradient-to-r from-[#3730A3] to-[#4238C8] hover:from-[#322e92] hover:to-[#3b32b4]"
                      : "bg-gradient-to-r from-[#4238C8] to-[#3730A3] hover:from-[#3b32b4] hover:to-[#322e92]"
                  } text-white transition-all duration-200 shadow-md hover:shadow-lg text-base py-2 relative overflow-hidden group ${
                    loading ? "opacity-75 cursor-not-allowed" : ""
                  }`}
                  style={{ animation: loading || isLocked ? "none" : "pulse 2s infinite" }}
                >
                  {loading ? (
                    <div className="flex items-center justify-center">
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                      <span>جاري تسجيل الدخول...</span>
                    </div>
                  ) : isLocked ? (
                    <span className="relative z-10">🔒 تم قفل تسجيل الدخول</span>
                  ) : (
                    <>
                      <span className="relative z-10">تسجيل الدخول</span>
                      <span className="absolute inset-0 bg-white opacity-20 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700"></span>
                    </>
                  )}
                </Button>
              </form>

              <div className="mt-6 text-center">
                <p
                  className={`text-sm mb-4 ${
                    darkMode ? "text-gray-400" : "text-gray-600"
                  }`}
                >
                  هذه الصفحة مخصصة لمديري النظام فقط
                </p>

                {/* الأزرار المحسنة */}
                <div className="flex flex-col gap-3">
                  {/* زر مسح البيانات المحفوظة */}
                  {(localStorage.getItem("adminCredentials") ||
                    localStorage.getItem("adminToken") ||
                    localStorage.getItem("admin")) && (
                    <button
                      onClick={clearAllAdminData}
                      className={`px-4 py-2 rounded-md text-sm font-medium transition-all duration-200 ${
                        darkMode
                          ? "bg-red-600/20 text-red-400 border border-red-600/30 hover:bg-red-600/30 hover:border-red-500"
                          : "bg-red-50 text-red-600 border border-red-200 hover:bg-red-100 hover:border-red-300"
                      } shadow-sm hover:shadow-md relative overflow-hidden group`}
                    >
                      <span className="relative z-10 flex items-center justify-center gap-2">
                        🗑️ مسح جميع البيانات المحفوظة
                      </span>
                      <span className={`absolute inset-0 ${darkMode ? "bg-red-500" : "bg-red-100"} opacity-20 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700`}></span>
                    </button>
                  )}

                  {/* زر العودة للصفحة الرئيسية */}
                  <Link
                    to="/"
                    className={`px-4 py-2 rounded-md text-sm font-medium transition-all duration-200 ${
                      darkMode
                        ? "bg-indigo-600/20 text-indigo-400 border border-indigo-600/30 hover:bg-indigo-600/30 hover:border-indigo-500"
                        : "bg-indigo-50 text-indigo-600 border border-indigo-200 hover:bg-indigo-100 hover:border-indigo-300"
                    } shadow-sm hover:shadow-md relative overflow-hidden group`}
                  >
                    <span className="relative z-10 flex items-center justify-center gap-2">
                      🏠 العودة إلى الصفحة الرئيسية
                    </span>
                    <span className={`absolute inset-0 ${darkMode ? "bg-indigo-500" : "bg-indigo-100"} opacity-20 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700`}></span>
                  </Link>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </Layout>
  );
};

export default AdminLogin;
